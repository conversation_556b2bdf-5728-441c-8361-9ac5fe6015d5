# Otomatik Yedekleme Sistemi Dokümantasyonu

PROWORK projesinde otomatik yedekleme sistemi, veritabanı ve medya dosyalarını düzenli olarak yedekler ve eski yedekleri temizler.

## Özellikler

- Günlük veritabanı yedeği (MySQL dump)
- Me<PERSON>a dosyalarının ZIP arşivi
- 30 günden eski yedeklemelerin otomatik temizlenmesi
- Yedekleme dosyaları `storage/app/backups` klasöründe saklanır

## Kurulum

1. Yedekleme komutunun düzgün çalışması için sistem üzerinde `mysqldump` ve `zip` komutlarının yüklü olduğundan emin olun.

2. Laravel'in zamanlayıcısını çalıştırmak için sunucunuzun crontab dosyasına aşağıdaki satırı ekleyin:

```bash
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1
```

Not: `/path/to/your/project` kısmını projenin gerçek dizin yolu ile değiştirin.

## Manuel Yedekleme

İsterseniz manuel olarak yedekleme almak için aşağıdaki komutu çalıştırabilirsiniz:

```bash
php artisan backup:database
```

## Yedekleme Programı

- Veritabanı ve medya dosyaları her gün saat 02:00'de yedeklenir.
- Yedek dosyaları `backup_YYYY-MM-DD_HH-mm-ss.sql` ve `media_backup_YYYY-MM-DD_HH-mm-ss.zip` formatında adlandırılır.

## Yedekleme Dosyalarını Yönetme

Yedekleme dosyalarını `storage/app/backups` klasöründe bulabilirsiniz. İsterseniz bu yedekleme dosyalarını harici bir depolama alanına da taşıyabilirsiniz.
