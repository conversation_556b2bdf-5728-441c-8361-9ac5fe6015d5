<?php

use App\Http\Controllers\Admin\PageController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\MenuController;
use App\Http\Controllers\Admin\GalleryController;
use App\Http\Controllers\Admin\ContactController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Admin\FileManagerController;
use App\Http\Controllers\Admin\BannerController;
use App\Http\Controllers\Front\FrontController;
use App\Http\Controllers\Admin\BackupController; // Added BackupController
use App\Http\Controllers\Admin\GalleryImageController;
use App\Http\Controllers\Front\B2BController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\PayTRController;
use App\Http\Controllers\Admin\OrderController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Test route
Route::get('/test', function () {
    return response('Test çalışıyor!');
});

// Inertia test route
Route::get('/inertia-test', function () {
    return Inertia::render('Test');
});

Route::get('/', [FrontController::class, 'home'])->name('home');

Route::get('/bakiyeOmurTablosu', [FrontController::class, 'bakiyeOmurTablosu'])->name('bakiyeOmurTablosu');

Route::get('/bakiyePMFTablosu', [FrontController::class, 'bakiyePMFTablosu'])->name('bakiyePMFTablosu');


/*
Route::get('/b2b', [FrontController::class, 'b2b'])->name('b2b');

Route::post('/b2b/basket/add', [B2BController::class, 'basketAdd'])->name('b2b.basketAdd');

Route::get('/b2b/basket/get', [B2BController::class, 'getBasket'])->name('b2b.getBasket');

Route::get('/b2b/basket', [B2BController::class, 'basket'])->name('b2b.basket');

*/

Route::get('/set-main-grup/{grup}', [FrontController::class, 'setMainGrup'])->name('front.set_main_grup');

Route::get('/pages/{slug}', [FrontController::class, 'showPage'])->name('front.pages.show');

Route::get('/products', [FrontController::class, 'listProducts'])->name('front.products.index');

Route::get('/products/proworker-products', [FrontController::class, 'proworker'])->name('front.products.proworker');

Route::get('/products/solar-products', [FrontController::class, 'proworker'])->name('front.products.solar');

Route::get('/products/{slug}', [FrontController::class, 'showProduct'])->name('front.products.show');

Route::get('/gallery', [FrontController::class, 'listGallery'])->name('front.gallery.index');

Route::get('/contact', [FrontController::class, 'contactForm'])->name('front.contact.form');

Route::get('/privacy-policy', function () {
    return Inertia::render('PrivacyPolicy');
})->name('privacy.policy');

// Front API routes for direct browser access
Route::get('/api/menus', [App\Http\Controllers\Api\MenuController::class, 'index']);

Route::post('/api/locale/{locale}', [App\Http\Controllers\Api\LocaleController::class, 'setLocale'])->name('api.locale.set');

// Cart and Checkout routes
Route::get('/cart', [CartController::class, 'index'])->name('cart.index');
Route::post('/cart/add', [CartController::class, 'addItem'])->name('cart.add');
Route::delete('/cart/remove', [CartController::class, 'removeItem'])->name('cart.remove');
Route::patch('/cart/update', [CartController::class, 'updateQuantity'])->name('cart.update');
Route::delete('/cart/clear', [CartController::class, 'clear'])->name('cart.clear');

Route::get('/checkout', [CheckoutController::class, 'index'])->name('checkout.index');
Route::post('/checkout', [CheckoutController::class, 'store'])->name('checkout.store');
Route::get('/checkout/payment/{order}', [CheckoutController::class, 'payment'])->name('checkout.payment');
Route::get('/checkout/confirmation/{orderNumber}', [CheckoutController::class, 'confirmation'])->name('checkout.confirmation');

// PayTR routes
Route::post('/paytr/process', [PayTRController::class, 'processPayment'])->name('paytr.process');
Route::post('/paytr/success', [PayTRController::class, 'success'])->name('paytr.success');
Route::post('/paytr/fail', [PayTRController::class, 'fail'])->name('paytr.fail');
Route::post('/paytr/ipn', [PayTRController::class, 'ipn'])->name('paytr.ipn');

// Redirect dashboard to admin dashboard
Route::get('dashboard', function () {
    return redirect()->route('admin.dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');


// Admin routes
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Resources with role middleware - temporarily disabled role check
    Route::group([], function () {
        Route::resource('roles', RoleController::class);
        Route::resource('permissions', PermissionController::class);
        Route::resource('users', UserController::class);
    });

    // Resources accessible to admin and editor roles - temporarily disabled role check
    Route::group([], function () {
        Route::resource('pages', PageController::class);
        Route::resource('products', ProductController::class);

        Route::post('products/imageUpload', [ProductController::class, 'imageUpload'])->name('products.imageUpload');

        Route::get('products/{product}/images', [ProductController::class, 'getProductImages'])->name('product.getProductImages');

        Route::delete('products/images/{productImage}', [ProductController::class, 'deleteProductImage'])->name('products.deleteProductImage');

        Route::post('products/imageOrderUpadte', [ProductController::class, 'imageOrderUpadte'])->name('products.imageOrderUpadte');

        Route::resource('categories', CategoryController::class);

        Route::resource('menus', MenuController::class);

        Route::resource('gallery', GalleryController::class);

        Route::resource('contacts', ContactController::class);

        Route::resource('banners', BannerController::class);

        // Order management routes
        Route::resource('orders', OrderController::class)->only(['index', 'show', 'destroy']);
        Route::patch('orders/{order}/status', [OrderController::class, 'updateStatus'])->name('orders.update-status');
        Route::patch('orders/{order}/notes', [OrderController::class, 'updateNotes'])->name('orders.update-notes');
        Route::get('orders/export', [OrderController::class, 'export'])->name('orders.export');
        Route::get('orders/{order}/pdf', [OrderController::class, 'generatePDF'])->name('orders.pdf');
        Route::get('orders/{order}/print', [OrderController::class, 'print'])->name('orders.print');

        Route::get('/galleries/{gallery}/galleryimages', [GalleryImageController::class, 'show'])
            ->name('gallery_images.show');

        Route::get('/galleries/{gallery}/galleryimages/create', [GalleryImageController::class, 'create'])
            ->name('gallery_images.create');

        Route::post('/galleries/{gallery}/galleryimages', [GalleryImageController::class, 'store'])
            ->name('gallery_images.store');

        Route::get('/galleries/{gallery}/galleryimages/{id}/edit', [GalleryImageController::class, 'edit'])
            ->name('gallery_images.edit');

        Route::put('/galleries/{gallery}/galleryimages/{id}', [GalleryImageController::class, 'update'])
            ->name('gallery_images.update');

        Route::delete('/galleries/{gallery}/galleryimages/{id}', [GalleryImageController::class, 'destroy'])
            ->name('gallery_images.destroy');
        // File Manager routes
        Route::resource('files', FileManagerController::class);
        Route::post('files/bulk-upload', [FileManagerController::class, 'bulkUpload'])->name('files.bulk-upload');
        // Backup routes
        Route::middleware(['verified'])->group(function () {
            Route::get('backups', [BackupController::class, 'index'])->name('backups.index');
            Route::post('backups', [BackupController::class, 'create'])->name('backups.create');
            Route::get('backups/{filename}', [BackupController::class, 'download'])->name('backups.download');
            Route::delete('backups/{filename}', [BackupController::class, 'destroy'])->name('backups.destroy');
        });
    });
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
