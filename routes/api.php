<?php
use App\Http\Controllers\Api\LocaleController;
use App\Http\Controllers\Api\MenuController;
use App\Http\Controllers\Api\SearchController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {return $request->user()}};

// Public API routes
Route::get('/menus', [MenuController::class, 'index']);
Route::post('/locale/{locale}', [LocaleController::class, 'setLocale']);
Route::get('/search', [SearchController::class, 'search']);
