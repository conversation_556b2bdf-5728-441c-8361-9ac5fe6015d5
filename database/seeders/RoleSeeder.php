<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $editorRole = Role::firstOrCreate(['name' => 'editor']);
        $userRole = Role::firstOrCreate(['name' => 'user']);

        // Create permissions
        $permissions = [
            // User management
            'users.view',
            'users.create',
            'users.edit',
            'users.delete',
            
            // Content management
            'pages.view',
            'pages.create',
            'pages.edit',
            'pages.delete',
            
            'products.view',
            'products.create',
            'products.edit', 
            'products.delete',
            
            'categories.view',
            'categories.create',
            'categories.edit',
            'categories.delete',
            
            'menus.view',
            'menus.create',
            'menus.edit',
            'menus.delete',
            
            'galleries.view',
            'galleries.create',
            'galleries.edit',
            'galleries.delete',
            
            'contacts.view',
            'contacts.delete',
            
            'files.view',
            'files.create',
            'files.edit',
            'files.delete',
            
            // Role management
            'roles.view',
            'roles.create',
            'roles.edit',
            'roles.delete',
            
            // Permission management
            'permissions.view',
            'permissions.create',
            'permissions.edit',
            'permissions.delete',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Give all permissions to admin
        $adminRole->givePermissionTo(Permission::all());
        
        // Give content permissions to editor
        $editorRole->givePermissionTo([
            'pages.view', 'pages.create', 'pages.edit', 'pages.delete',
            'products.view', 'products.create', 'products.edit', 'products.delete',
            'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
            'menus.view', 'menus.create', 'menus.edit', 'menus.delete',
            'galleries.view', 'galleries.create', 'galleries.edit', 'galleries.delete',
            'contacts.view', 'contacts.delete',
            'files.view', 'files.create', 'files.edit', 'files.delete',
        ]);
        
        // Basic permissions for user
        $userRole->givePermissionTo([
            'pages.view',
            'products.view',
            'categories.view',
            'galleries.view',
        ]);
    }
}
