<?php

namespace Database\Seeders;

use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if admin user already exists
        $admin = User::where('email', '<EMAIL>')->first();
        
        if (!$admin) {
            $admin = User::create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'user_type' =>  'admin'
            ]);
        }

        // Ensure admin role exists
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        
        // Assign admin role to admin user
        $admin->assignRole($adminRole);
    }
}
