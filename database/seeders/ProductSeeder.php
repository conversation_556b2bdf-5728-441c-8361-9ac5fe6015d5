<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // <PERSON><PERSON><PERSON> kategor<PERSON> yo<PERSON>, önce birkaç kategori ekleyelim
        if (Category::count() == 0) {
            Category::factory(5)->create();
        }
        Product::truncate();
        // Mevcut kategorileri al
        $categories = Category::pluck('id')->toArray();

        // 50 adet ürün oluştur
        for ($i = 0; $i < 50; $i++) {
            $nameTr = 'Ürün ' . ($i + 1);
            $nameEn = 'Product ' . ($i + 1);

            Product::create([
                'main_grup' => 'Ana Grup ' . rand(1, 5),
                'category_id' => $categories[array_rand($categories)], // Rastgele kategori seç
                'slug' => Str::slug($nameTr),
                'name' => [
                    'tr' => $nameTr,
                    'en' => $nameEn
                ],
                'description' => [
                    'tr' => 'Bu, ' . $nameTr . ' için açıklamadır.',
                    'en' => 'This is the description for ' . $nameEn . '.'
                ],
                'features' => [
                    'tr' => 'Özellikler: ' . Str::random(20),
                    'en' => 'Features: ' . Str::random(20)
                ],
                'price' => rand(100, 1000) / 10, // 10 ile 100 arasında rastgele fiyat
                'sku' => 'SKU-' . strtoupper(Str::random(8)),
                'stock' => rand(0, 100),
                'is_active' => (bool)rand(0, 1),
                'order' => $i + 1,
                'meta_description' => [
                    'tr' => 'Meta açıklama: ' . $nameTr,
                    'en' => 'Meta description: ' . $nameEn
                ],
                'meta_keywords' => [
                    'tr' => 'Anahtar kelimeler, ' . $nameTr,
                    'en' => 'Keywords, ' . $nameEn
                ],
            ]);
        }
    }
}
