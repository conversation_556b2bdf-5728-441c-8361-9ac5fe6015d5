<?php

namespace Database\Seeders;

use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles
        $adminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'web',
        ]);

        $editorRole = Role::firstOrCreate([
            'name' => 'editor',
            'guard_name' => 'web',
        ]);

        $writerRole = Role::firstOrCreate([
            'name' => 'writer',
            'guard_name' => 'web',
        ]);

        // Create permissions for users
        Permission::create(['name' => 'users.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'users.create', 'guard_name' => 'web']);
        Permission::create(['name' => 'users.edit', 'guard_name' => 'web']);
        Permission::create(['name' => 'users.delete', 'guard_name' => 'web']);

        // Create permissions for roles
        Permission::create(['name' => 'roles.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'roles.create', 'guard_name' => 'web']);
        Permission::create(['name' => 'roles.edit', 'guard_name' => 'web']);
        Permission::create(['name' => 'roles.delete', 'guard_name' => 'web']);

        // Create permissions for pages
        Permission::create(['name' => 'pages.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'pages.create', 'guard_name' => 'web']);
        Permission::create(['name' => 'pages.edit', 'guard_name' => 'web']);
        Permission::create(['name' => 'pages.delete', 'guard_name' => 'web']);

        // Create permissions for products
        Permission::create(['name' => 'products.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'products.create', 'guard_name' => 'web']);
        Permission::create(['name' => 'products.edit', 'guard_name' => 'web']);
        Permission::create(['name' => 'products.delete', 'guard_name' => 'web']);

        // Create permissions for menus
        Permission::create(['name' => 'menus.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'menus.create', 'guard_name' => 'web']);
        Permission::create(['name' => 'menus.edit', 'guard_name' => 'web']);
        Permission::create(['name' => 'menus.delete', 'guard_name' => 'web']);

        // Create permissions for galleries
        Permission::create(['name' => 'galleries.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'galleries.create', 'guard_name' => 'web']);
        Permission::create(['name' => 'galleries.edit', 'guard_name' => 'web']);
        Permission::create(['name' => 'galleries.delete', 'guard_name' => 'web']);

        // Create permissions for contacts
        Permission::create(['name' => 'contacts.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'contacts.delete', 'guard_name' => 'web']);

        // Create permissions for file manager
        Permission::create(['name' => 'files.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'files.create', 'guard_name' => 'web']);
        Permission::create(['name' => 'files.delete', 'guard_name' => 'web']);

        // Create permissions for categories
        Permission::create(['name' => 'categories.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'categories.create', 'guard_name' => 'web']);
        Permission::create(['name' => 'categories.edit', 'guard_name' => 'web']);
        Permission::create(['name' => 'categories.delete', 'guard_name' => 'web']);

        // Create permissions for settings
        Permission::create(['name' => 'settings.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'settings.edit', 'guard_name' => 'web']);

        // Assign all permissions to admin role
        $adminRole->givePermissionTo(Permission::all());

        // Assign editor permissions
        $editorPermissions = Permission::whereIn('name', [
            'pages.view', 'pages.create', 'pages.edit', 'pages.delete',
            'products.view', 'products.create', 'products.edit', 'products.delete',
            'galleries.view', 'galleries.create', 'galleries.edit', 'galleries.delete',
            'menus.view', 'menus.create', 'menus.edit', 'menus.delete',
            'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
            'files.view', 'files.create', 'files.delete',
            'contacts.view', 'contacts.delete',
        ])->get();
        $editorRole->givePermissionTo($editorPermissions);

        // Assign only view and edit permissions to writer role
        $writerPermissions = Permission::whereIn('name', [
            'pages.view', 'pages.create', 'pages.edit',
            'products.view', 'products.create', 'products.edit',
            'galleries.view', 'galleries.create', 'galleries.edit',
        ])->get();
        $writerRole->givePermissionTo($writerPermissions);

        // Create admin user if it doesn't exist
        $admin = User::where('email', '<EMAIL>')->first();
        
        if (!$admin) {
            $admin = User::create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ]);
        }
        
        // Assign admin role to admin user
        $admin->assignRole($adminRole);

        // Create editor user
        $editor = User::create([
            'name' => 'Editor',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
        
        // Assign editor role to editor user
        $editor->assignRole($editorRole);

        // Create writer user
        $writer = User::create([
            'name' => 'Writer',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
        
        // Assign writer role to writer user
        $writer->assignRole($writerRole);
    }
}
