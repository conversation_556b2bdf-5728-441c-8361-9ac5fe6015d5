<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Illuminate\Database\Seeder;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some products for the orders
        $products = Product::take(3)->get();
        
        if ($products->isEmpty()) {
            $this->command->info('No products found. Please create some products first.');
            return;
        }

        // Create sample orders
        $orders = [
            [
                'customer_name' => 'Ahmet Yılmaz',
                'customer_email' => '<EMAIL>',
                'customer_phone' => '+90 ************',
                'status' => 'pending',
                'payment_method' => 'bank_transfer',
                'billing_address' => [
                    'street' => 'Atatürk Caddesi No: 123',
                    'city' => 'İstanbul',
                    'postal_code' => '34000',
                    'country' => 'Türkiye',
                ],
                'notes' => 'Lütfen hızlı teslimat yapın.',
            ],
            [
                'customer_name' => 'Fatma Demir',
                'customer_email' => '<EMAIL>',
                'customer_phone' => '+90 ************',
                'status' => 'processing',
                'payment_method' => 'bank_transfer',
                'billing_address' => [
                    'street' => 'İnönü Bulvarı No: 456',
                    'city' => 'Ankara',
                    'postal_code' => '06000',
                    'country' => 'Türkiye',
                ],
                'notes' => 'Ofis adresine teslim edilsin.',
            ],
            [
                'customer_name' => 'Mehmet Kaya',
                'customer_email' => '<EMAIL>',
                'customer_phone' => '+90 ************',
                'status' => 'completed',
                'payment_method' => 'bank_transfer',
                'billing_address' => [
                    'street' => 'Cumhuriyet Meydanı No: 789',
                    'city' => 'İzmir',
                    'postal_code' => '35000',
                    'country' => 'Türkiye',
                ],
                'notes' => null,
                'admin_notes' => 'Müşteri çok memnun kaldı.',
            ],
        ];

        foreach ($orders as $orderData) {
            // Calculate totals
            $subtotal = 0;
            $orderItems = [];

            // Create random order items
            $itemCount = rand(1, 3);
            $selectedProducts = $products->random($itemCount);

            foreach ($selectedProducts as $product) {
                $quantity = rand(1, 3);
                $price = $product->price ?? rand(100, 1000);
                $total = $price * $quantity;
                $subtotal += $total;

                $orderItems[] = [
                    'product_id' => $product->id,
                    'product_name' => $product->name['tr'] ?? $product->name['en'] ?? 'Test Product',
                    'product_sku' => $product->sku ?? 'TEST-' . $product->id,
                    'unit_price' => $price,
                    'quantity' => $quantity,
                    'total_price' => $total,
                ];
            }

            $taxAmount = $subtotal * 0.20; // 20% VAT
            $totalAmount = $subtotal + $taxAmount;

            // Create the order
            $order = Order::create([
                'order_number' => Order::generateOrderNumber(),
                'customer_name' => $orderData['customer_name'],
                'customer_email' => $orderData['customer_email'],
                'customer_phone' => $orderData['customer_phone'],
                'billing_address' => $orderData['billing_address'],
                'shipping_address' => $orderData['billing_address'], // Same as billing for simplicity
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'payment_method' => $orderData['payment_method'],
                'status' => $orderData['status'],
                'notes' => $orderData['notes'],
                'admin_notes' => $orderData['admin_notes'] ?? null,
                'created_at' => now()->subDays(rand(0, 30)), // Random dates within last 30 days
            ]);

            // Create order items
            foreach ($orderItems as $itemData) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $itemData['product_id'],
                    'product_name' => $itemData['product_name'],
                    'product_sku' => $itemData['product_sku'],
                    'unit_price' => $itemData['unit_price'],
                    'quantity' => $itemData['quantity'],
                    'total_price' => $itemData['total_price'],
                ]);
            }

            $this->command->info("Created order: {$order->order_number}");
        }

        $this->command->info('Order seeding completed!');
    }
}
