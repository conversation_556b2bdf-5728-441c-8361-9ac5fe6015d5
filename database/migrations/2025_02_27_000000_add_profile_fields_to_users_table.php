<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->text('bio')->nullable()->after('phone');
            $table->string('address')->nullable()->after('bio');
            $table->string('city')->nullable()->after('address');
            $table->string('country')->nullable()->after('city');
            $table->string('postal_code')->nullable()->after('country');
            $table->date('birth_date')->nullable()->after('postal_code');
            $table->string('profile_photo_path', 2048)->nullable()->after('birth_date');
            $table->json('preferences')->nullable()->after('profile_photo_path');
            $table->boolean('two_factor_enabled')->default(false)->after('preferences');
            $table->timestamp('last_active_at')->nullable()->after('two_factor_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'bio',
                'address',
                'city',
                'country',
                'postal_code',
                'birth_date',
                'profile_photo_path',
                'preferences',
                'two_factor_enabled',
                'last_active_at',
            ]);
        });
    }
};
