<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('banners', function (Blueprint $table) {
            $table->id();
            $table->string('main_grup')->default('default');
            $table->json('title');
            $table->json('description')->nullable();
            $table->string('image');
            $table->string('link')->nullable();
            $table->json('button_text')->nullable();
            $table->string('type')->default('main'); // Types: main, banner1, banner2, banner3, banner4
            $table->integer('order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('banners');
    }
};
