<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->index('category_id');
            $table->index('main_grup');
            $table->index([ 'created_at']);
        });

        Schema::table('pages', function (Blueprint $table) {
            $table->index('main_grup');
            $table->index([ 'created_at']);
        });

        Schema::table('galleries', function (Blueprint $table) {
            $table->index('main_grup');
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['category_id']);
            $table->dropIndex(['main_grup']);
            $table->dropIndex([ 'created_at']);
        });

        Schema::table('pages', function (Blueprint $table) {
            $table->dropIndex(['main_grup']);
            $table->dropIndex([ 'created_at']);
        });

        Schema::table('galleries', function (Blueprint $table) {
            $table->dropIndex(['main_grup']);
            $table->dropIndex([ 'created_at']);
        });
    }
};
