{"private": true, "type": "module", "scripts": {"build": "vite build && cp public/build/.vite/manifest.json public/build/", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.13.5", "@vue/eslint-config-typescript": "^14.3.0", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.9", "terser": "^5.39.0", "typescript-eslint": "^8.23.0", "vue-tsc": "^2.2.4"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@inertiajs/inertia": "^0.11.1", "@inertiajs/vue3": "^2.0.5", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/vite": "^4.0.10", "@vitejs/plugin-vue": "^5.2.1", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^12.0.0", "autoprefixer": "^10.4.20", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "flowbite": "^3.1.2", "flowbite-vue": "^0.1.9", "laravel-vite-plugin": "^1.0", "lodash": "^4.17.21", "lucide": "^0.468.0", "lucide-vue-next": "^0.468.0", "pinia": "^3.0.1", "radix-vue": "^1.9.11", "sweetalert2": "^11.17.2", "swiper": "^11.2.5", "tailwind-merge": "^2.5.5", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.2.2", "vite": "^6.2.0", "vue": "^3.5.13", "vue-toastr": "^2.1.2", "vue3-toastify": "^0.2.8", "ziggy-js": "^2.4.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}