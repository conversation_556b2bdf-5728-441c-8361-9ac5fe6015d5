<?php

namespace Tests\Feature;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CartTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for test database
        $this->artisan('migrate', ['--database' => 'testing']);

        // Create a test category
        $category = Category::create([
            'name' => ['tr' => 'Test Kategori', 'en' => 'Test Category'],
            'slug' => 'test-category',
            'is_active' => true,
        ]);

        // Create a test product
        Product::create([
            'name' => ['tr' => 'Test Ürün', 'en' => 'Test Product'],
            'slug' => 'test-product',
            'description' => ['tr' => 'Test açıklama', 'en' => 'Test description'],
            'price' => 100.00,
            'sku' => 'TEST-001',
            'category_id' => $category->id,
            'is_active' => true,
        ]);
    }

    public function test_cart_page_loads_successfully()
    {
        $response = $this->get('/cart');
        $response->assertStatus(200);
    }

    public function test_checkout_page_loads_successfully()
    {
        $response = $this->get('/checkout');
        $response->assertStatus(200);
    }

    public function test_add_to_cart_api_endpoint()
    {
        $product = Product::first();

        $response = $this->post('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 2,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'quantity' => 2,
                ]);
    }

    public function test_add_to_cart_with_invalid_product()
    {
        $response = $this->post('/cart/add', [
            'product_id' => 999,
            'quantity' => 1,
        ]);

        $response->assertStatus(422);
    }

    public function test_add_to_cart_with_invalid_quantity()
    {
        $product = Product::first();

        $response = $this->post('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 0,
        ]);

        $response->assertStatus(422);
    }

    public function test_remove_from_cart_api_endpoint()
    {
        $response = $this->delete('/cart/remove', [
            'product_id' => 1,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ]);
    }

    public function test_update_cart_quantity_api_endpoint()
    {
        $response = $this->patch('/cart/update', [
            'product_id' => 1,
            'quantity' => 3,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ]);
    }

    public function test_clear_cart_api_endpoint()
    {
        $response = $this->delete('/cart/clear');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ]);
    }

    public function test_checkout_form_validation()
    {
        $response = $this->post('/checkout', []);

        $response->assertStatus(302); // Redirect back with validation errors
    }

    public function test_successful_checkout_creates_order()
    {
        $product = Product::first();

        $orderData = [
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+90 ************',
            'billing_address' => [
                'street' => '123 Test Street',
                'city' => 'Istanbul',
                'postal_code' => '34000',
                'country' => 'Türkiye',
            ],
            'cart_items' => [
                [
                    'id' => $product->id,
                    'quantity' => 2,
                    'price' => $product->price,
                ]
            ],
            'subtotal' => $product->price * 2,
            'tax_amount' => ($product->price * 2) * 0.20,
            'total_amount' => ($product->price * 2) * 1.20,
            'notes' => 'Test order notes',
        ];

        $response = $this->post('/checkout', $orderData);

        $response->assertStatus(302); // Redirect to confirmation page

        $this->assertDatabaseHas('orders', [
            'customer_email' => '<EMAIL>',
            'total_amount' => ($product->price * 2) * 1.20,
        ]);
    }

    public function test_admin_orders_index_page_loads()
    {
        $user = \App\Models\User::factory()->create();

        $response = $this->actingAs($user)->get('/admin/orders');
        $response->assertStatus(200);
    }

    public function test_admin_can_view_order_details()
    {
        $user = \App\Models\User::factory()->create();
        $product = Product::first();

        // Create a test order
        $order = \App\Models\Order::create([
            'order_number' => 'TEST-ORDER-001',
            'customer_name' => 'Test Customer',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+90 ************',
            'billing_address' => [
                'street' => 'Test Street',
                'city' => 'Test City',
                'postal_code' => '12345',
                'country' => 'Türkiye',
            ],
            'shipping_address' => [
                'street' => 'Test Street',
                'city' => 'Test City',
                'postal_code' => '12345',
                'country' => 'Türkiye',
            ],
            'subtotal' => 100.00,
            'tax_amount' => 20.00,
            'total_amount' => 120.00,
            'payment_method' => 'bank_transfer',
            'status' => 'pending',
            'notes' => 'Test order notes',
        ]);

        $response = $this->actingAs($user)->get("/admin/orders/{$order->id}");
        $response->assertStatus(200);
    }
}
