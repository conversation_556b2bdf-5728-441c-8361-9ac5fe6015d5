import vue from '@vitejs/plugin-vue';
import autoprefixer from 'autoprefixer';
import laravel from 'laravel-vite-plugin';
import path from 'path';
import tailwindcss from 'tailwindcss';
import { defineConfig } from 'vite';
import { splitVendorChunkPlugin } from 'vite';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/js/app.ts'],
            refresh: true,
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
        splitVendorChunkPlugin(),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './resources/js'),
        },
    },
    css: {
        postcss: {
            plugins: [tailwindcss, autoprefixer],
        },
    },
    build: {
        manifest: true,
        chunkSizeWarningLimit: 1000,
        rollupOptions: {
            output: {
                // manualChunks fonksiyon formuna çevrildi
                manualChunks(id) {
                    if (id.includes('node_modules')) {
                        // "vue" dışarıda bırakılıyor, vendor chunk ise yalnızca diğer belirtilen kütüphaneleri kapsıyor
                        if (
                            id.includes('@inertiajs/vue3') ||
                            id.includes('@headlessui/vue') ||
                            id.includes('@vueup/vue-quill')
                        ) {
                            return 'vendor';
                        }
                    }
                },
            },
        },
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: true,
                drop_debugger: true,
            },
        },
    },
});
