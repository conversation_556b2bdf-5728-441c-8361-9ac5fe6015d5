<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class SetThemeColors extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'theme:colors 
                            {--primary= : Primary color RGB values (e.g., "239 68 68")}
                            {--secondary= : Secondary color RGB values (e.g., "185 28 28")}
                            {--accent= : Accent color RGB values (e.g., "251 146 60")}
                            {--preset= : Use a color preset (red, blue, green, purple, orange)}';

    /**
     * The console command description.
     */
    protected $description = 'Set theme colors in .env file';

    /**
     * Color presets
     */
    protected $presets = [
        'red' => [
            'primary' => '239 68 68',
            'secondary' => '185 28 28',
            'accent' => '251 146 60'
        ],
        'blue' => [
            'primary' => '59 130 246',
            'secondary' => '37 99 235',
            'accent' => '249 115 22'
        ],
        'green' => [
            'primary' => '34 197 94',
            'secondary' => '21 128 61',
            'accent' => '59 130 246'
        ],
        'purple' => [
            'primary' => '147 51 234',
            'secondary' => '126 34 206',
            'accent' => '249 115 22'
        ],
        'orange' => [
            'primary' => '249 115 22',
            'secondary' => '234 88 12',
            'accent' => '59 130 246'
        ]
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $envPath = base_path('.env');
        
        if (!File::exists($envPath)) {
            $this->error('.env file not found!');
            return 1;
        }

        $envContent = File::get($envPath);
        
        // Check if using preset
        if ($preset = $this->option('preset')) {
            if (!isset($this->presets[$preset])) {
                $this->error("Preset '{$preset}' not found. Available presets: " . implode(', ', array_keys($this->presets)));
                return 1;
            }
            
            $colors = $this->presets[$preset];
            $this->info("Using '{$preset}' preset...");
        } else {
            // Use individual color options
            $colors = [
                'primary' => $this->option('primary'),
                'secondary' => $this->option('secondary'),
                'accent' => $this->option('accent')
            ];
        }

        // Update .env file
        foreach ($colors as $type => $color) {
            if ($color) {
                $key = 'APP_' . strtoupper($type) . '_COLOR';
                $pattern = '/^' . preg_quote($key, '/') . '=.*$/m';
                $replacement = $key . '="' . $color . '"';
                
                if (preg_match($pattern, $envContent)) {
                    $envContent = preg_replace($pattern, $replacement, $envContent);
                } else {
                    $envContent .= "\n" . $replacement;
                }
                
                $this->info("Set {$type} color to: {$color}");
            }
        }

        File::put($envPath, $envContent);
        
        // Clear config cache
        $this->call('config:clear');
        
        $this->info('Theme colors updated successfully!');
        $this->info('Please refresh your browser to see the changes.');
        
        return 0;
    }
}
