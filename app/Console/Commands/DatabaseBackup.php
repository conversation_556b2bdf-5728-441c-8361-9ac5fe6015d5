<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class DatabaseBackup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:database';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a backup of the database and store it in the storage folder';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Create backup folder if it doesn't exist
        $backupPath = storage_path('app/backups');
        if (!File::exists($backupPath)) {
            File::makeDirectory($backupPath, 0755, true);
        }
        
        // Format the file name with date and time
        $fileName = 'backup_' . Carbon::now()->format('Y-m-d_H-i-s') . '.sql';
        $filePath = $backupPath . '/' . $fileName;
        
        // Database configuration
        $database = config('database.connections.mysql.database');
        $user = config('database.connections.mysql.username');
        $password = config('database.connections.mysql.password');
        $host = config('database.connections.mysql.host');
        
        // Build the mysqldump command
        $command = "mysqldump --user={$user} --password={$password} --host={$host} {$database} > {$filePath}";
        
        try {
            exec($command);
            
            // Create a ZIP file of the media files
            $mediaPath = public_path('media');
            $zipFileName = 'media_backup_' . Carbon::now()->format('Y-m-d_H-i-s') . '.zip';
            $zipFilePath = $backupPath . '/' . $zipFileName;
            
            if (File::exists($mediaPath)) {
                // Use zip command for Mac OS
                exec("cd {$mediaPath} && zip -r {$zipFilePath} .");
                $this->info('Media files backup created successfully: ' . $zipFileName);
            }
            
            // Delete backups older than 30 days
            $this->cleanOldBackups($backupPath);
            
            $this->info('Database backup created successfully: ' . $fileName);
            return 0;
        } catch (\Exception $e) {
            $this->error('Backup failed: ' . $e->getMessage());
            return 1;
        }
    }
    
    /**
     * Delete backups older than 30 days
     */
    private function cleanOldBackups($backupPath)
    {
        $files = File::files($backupPath);
        $thirtyDaysAgo = Carbon::now()->subDays(30);
        
        foreach ($files as $file) {
            $fileCreatedAt = Carbon::createFromTimestamp(File::lastModified($file));
            
            if ($fileCreatedAt->lt($thirtyDaysAgo)) {
                File::delete($file);
                $this->info('Deleted old backup: ' . basename($file));
            }
        }
    }
}
