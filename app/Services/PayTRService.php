<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PayTRService
{
    private $merchantId;
    private $merchantKey;
    private $merchantSalt;
    private $testMode;

    public function __construct()
    {
        $this->merchantId = config('paytr.merchant_id');
        $this->merchantKey = config('paytr.merchant_key');
        $this->merchantSalt = config('paytr.merchant_salt');
        $this->testMode = config('paytr.test_mode', true);
    }

    /**
     * Create payment request
     */
    public function createPayment($orderData)
    {
        $merchantOid = $orderData['order_id'];
        $userBasket = $this->formatBasket($orderData['items']);
        $userIp = request()->ip();
        $emailAddress = $orderData['email'];
        $paymentAmount = $orderData['amount'] * 100; // PayTR expects amount in kuruş
        $currency = 'TL';
        $testMode = $this->testMode ? '1' : '0';
        $noInstallment = '0'; // Taksit yok
        $maxInstallment = '0';
        $userAddress = $orderData['address'];
        $userPhone = $orderData['phone'];
        $merchantOkUrl = route('paytr.success');
        $merchantFailUrl = route('paytr.fail');
        $userBasketEncoded = base64_encode($userBasket);
        $timeout = 30; // 30 dakika

        // Hash oluşturma
        $hashStr = $this->merchantId . $userIp . $merchantOid . $emailAddress . $paymentAmount . $userBasketEncoded . $noInstallment . $maxInstallment . $currency . $testMode . $this->merchantSalt;
        $token = base64_encode(hash_hmac('sha256', $hashStr, $this->merchantKey, true));

        $postData = [
            'merchant_id' => $this->merchantId,
            'user_ip' => $userIp,
            'merchant_oid' => $merchantOid,
            'email' => $emailAddress,
            'payment_amount' => $paymentAmount,
            'paytr_token' => $token,
            'user_basket' => $userBasketEncoded,
            'debug_on' => $testMode,
            'no_installment' => $noInstallment,
            'max_installment' => $maxInstallment,
            'user_name' => $orderData['name'],
            'user_address' => $userAddress,
            'user_phone' => $userPhone,
            'merchant_ok_url' => $merchantOkUrl,
            'merchant_fail_url' => $merchantFailUrl,
            'timeout_limit' => $timeout,
            'currency' => $currency,
            'test_mode' => $testMode,
        ];

        try {
            $response = Http::asForm()->post('https://www.paytr.com/odeme/api/get-token', $postData);
            
            if ($response->successful()) {
                $result = json_decode($response->body(), true);
                
                if ($result['status'] == 'success') {
                    return [
                        'success' => true,
                        'token' => $result['token'],
                        'payment_url' => 'https://www.paytr.com/odeme/guvenli/' . $result['token']
                    ];
                } else {
                    Log::error('PayTR Error: ' . $result['reason']);
                    return [
                        'success' => false,
                        'error' => $result['reason']
                    ];
                }
            } else {
                Log::error('PayTR HTTP Error: ' . $response->status());
                return [
                    'success' => false,
                    'error' => 'Payment service unavailable'
                ];
            }
        } catch (\Exception $e) {
            Log::error('PayTR Exception: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Payment processing failed'
            ];
        }
    }

    /**
     * Verify callback
     */
    public function verifyCallback($postData)
    {
        $merchantOid = $postData['merchant_oid'];
        $status = $postData['status'];
        $totalAmount = $postData['total_amount'];
        $hash = $postData['hash'];

        // Hash doğrulama
        $hashStr = $merchantOid . $this->merchantSalt . $status . $totalAmount;
        $calculatedHash = base64_encode(hash_hmac('sha256', $hashStr, $this->merchantKey, true));

        if ($hash !== $calculatedHash) {
            Log::error('PayTR Hash verification failed');
            return [
                'success' => false,
                'error' => 'Hash verification failed'
            ];
        }

        return [
            'success' => true,
            'order_id' => $merchantOid,
            'status' => $status,
            'amount' => $totalAmount / 100, // Convert from kuruş to TL
            'transaction_id' => $postData['payment_id'] ?? null
        ];
    }

    /**
     * Format basket for PayTR
     */
    private function formatBasket($items)
    {
        $basket = [];
        foreach ($items as $item) {
            $basket[] = [
                $item['name'],
                $item['price'] * 100, // Convert to kuruş
                $item['quantity']
            ];
        }
        return json_encode($basket);
    }

    /**
     * Get payment status text
     */
    public function getStatusText($status, $locale = 'tr')
    {
        $statuses = [
            'tr' => [
                'success' => 'Ödeme başarılı',
                'failed' => 'Ödeme başarısız',
                'pending' => 'Ödeme beklemede'
            ],
            'en' => [
                'success' => 'Payment successful',
                'failed' => 'Payment failed',
                'pending' => 'Payment pending'
            ]
        ];

        return $statuses[$locale][$status] ?? $status;
    }
}
