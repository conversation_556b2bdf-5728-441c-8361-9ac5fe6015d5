<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;
use App\Models\Category;
use App\Models\ProductImage;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Product extends BaseModel
{
    protected $fillable = [
        'main_grup',
        'category_id',
        'slug',
        'name',
        'description',
        'features',
        'price',
        'sku',
        'stock',
        'is_active',
        'order',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'price'       => 'decimal:2',
        'stock'       => 'integer',
        'is_active'   => 'boolean',
        'order'       => 'integer',
        'category_id' => 'integer',
        'name'        => 'array',
    ];

    public $translatable = [
        'name',
        'description',
        'features',
        'meta_description',
        'meta_keywords',
    ];

    /**
     * Ürünün ait olduğu kategoriyi getirir.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Ürüne ait resimleri getirir.
     */
    public function productImages(): HasMany
    {
        return $this->hasMany(ProductImage::class);
    }

    /**
     * Ürüne ait resimler arasında, 'order' değeri en küçük olanın image_path değerini döndürür.
     *
     * @return string|null
     */
    public function getMinOrderImagePath()
    {
        // First try to get from loaded relationship
        if ($this->relationLoaded('productImages')) {
            $image = $this->productImages
                ->where('is_active', true)
                ->sortBy('order')
                ->first();
            return $image ? $image->image_path : null;
        }

        // Fallback to database query
        return $this->productImages()
                    ->where('is_active', true)
                    ->orderBy('order', 'asc')
                    ->value('image_path');
    }

    /**
     * JSON çıktısına eklenecek accessor.
     * Eğer image_path bulunursa başına "/storage/" ekler.
     *
     * @return string|null
     */
    public function getMinOrderImagePathAttribute()
    {
        $imagePath = $this->getMinOrderImagePath();
        return $imagePath ? '/storage/' . $imagePath : null;
    }

    /**
     * Get the primary product image (order = 0) or first available image
     *
     * @return string|null
     */
    public function getPrimaryImageAttribute()
    {
        // First try to get from loaded relationship
        if ($this->relationLoaded('productImages')) {
            $images = $this->productImages->where('is_active', true);

            // Try to get primary image (order = 0)
            $primaryImage = $images->where('order', 0)->first();
            if ($primaryImage) {
                return '/storage/' . $primaryImage->image_path;
            }

            // Fallback to first available image
            $firstImage = $images->sortBy('order')->first();
            if ($firstImage) {
                return '/storage/' . $firstImage->image_path;
            }
        }

        // Fallback to database query for primary image
        $primaryImage = $this->productImages()
            ->where('is_active', true)
            ->where('order', 0)
            ->first();

        if ($primaryImage) {
            return '/storage/' . $primaryImage->image_path;
        }

        // Fallback to first available image
        $firstImage = $this->productImages()
            ->where('is_active', true)
            ->orderBy('order', 'asc')
            ->first();

        if ($firstImage) {
            return '/storage/' . $firstImage->image_path;
        }

        return null;
    }

    protected $appends = ['min_order_image_path', 'primary_image'];
}