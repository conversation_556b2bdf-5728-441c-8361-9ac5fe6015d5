<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;

class ProductImage extends BaseModel
{
    protected $fillable = [
        'product_id',
        'image_path',
        'thumbnail_path',
        'is_active',
        'order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'order'     => 'integer',
    ];

    /**
     * Resmin ait olduğu galeriyi döner.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Resmin tam URL'sini döner.
     */
    public function getImageUrl()
    {
        return asset('storage/' . $this->image_path);
    }

   
   
}
