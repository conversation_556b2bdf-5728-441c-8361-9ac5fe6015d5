<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;

class Gallery extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'main_grup',
        'slug',
        'title',
        'description',
        'image_path',
        'thumbnail_path',
        'is_active',
        'order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * The attributes that are translatable.
     *
     * @var array<int, string>
     */
    public $translatable = [
        'title',
        'description',
    ];
    
    public function galleryImages()
    {
        return $this->hasMany(GalleryImage::class);
    }


    
    /**
     * 
     * Get the full URL for the image
     */
    public function getImageUrl()
    {
        return asset('storage/' . $this->image_path);
    }
    
    /**
     * Get the full URL for the thumbnail
     */
    public function getThumbnailUrl()
    {
        if ($this->thumbnail_path) {
            return asset('storage/' . $this->thumbnail_path);
        }
        
        return $this->getImageUrl();
    }
}
