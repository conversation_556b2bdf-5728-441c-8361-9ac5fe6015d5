<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;

class GalleryImage extends BaseModel
{
    protected $fillable = [
        'gallery_id',
        'image_path',
        'thumbnail_path',
        'is_active',
        'order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'order'     => 'integer',
    ];

    /**
     * Resmin ait olduğu galeriyi döner.
     */
    public function gallery()
    {
        return $this->belongsTo(Gallery::class);
    }

    /**
     * Resmin tam URL'sini döner.
     */
    public function getImageUrl()
    {
        return asset('storage/' . $this->image_path);
    }

    /**
     * <PERSON><PERSON><PERSON> küçük resim (thumbnail) tanımlıysa onun URL'sini, değilse normal resmin URL'sini döner.
     */
    public function getThumbnailUrl()
    {
        return $this->thumbnail_path ? asset('storage/' . $this->thumbnail_path) : $this->getImageUrl();
    }
}
