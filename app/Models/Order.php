<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Order extends Model
{
    protected $fillable = [
        'order_number',
        'customer_name',
        'customer_email',
        'customer_phone',
        'billing_address',
        'shipping_address',
        'subtotal',
        'tax_amount',
        'total_amount',
        'payment_method',
        'payment_token',
        'payment_status',
        'transaction_id',
        'payment_completed_at',
        'status',
        'notes',
        'admin_notes',
        'confirmed_at',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'confirmed_at' => 'datetime',
        'payment_completed_at' => 'datetime',
        'billing_address' => 'array',
        'shipping_address' => 'array',
    ];

    /**
     * Get the order items for the order.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Generate a unique order number.
     */
    public static function generateOrderNumber(): string
    {
        do {
            $orderNumber = 'ORD-' . date('Y') . '-' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (self::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Get the status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'yellow',
            'confirmed' => 'blue',
            'processing' => 'indigo',
            'shipped' => 'purple',
            'delivered' => 'green',
            'cancelled' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get the status label.
     */
    public function getStatusLabelAttribute(): array
    {
        return match ($this->status) {
            'pending' => ['tr' => 'Beklemede', 'en' => 'Pending'],
            'confirmed' => ['tr' => 'Onaylandı', 'en' => 'Confirmed'],
            'processing' => ['tr' => 'İşleniyor', 'en' => 'Processing'],
            'shipped' => ['tr' => 'Kargoya Verildi', 'en' => 'Shipped'],
            'delivered' => ['tr' => 'Teslim Edildi', 'en' => 'Delivered'],
            'cancelled' => ['tr' => 'İptal Edildi', 'en' => 'Cancelled'],
            default => ['tr' => 'Bilinmiyor', 'en' => 'Unknown'],
        };
    }
}
