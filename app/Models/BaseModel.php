<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

abstract class BaseModel extends Model
{
    use HasTranslations;
    
    /**
     * All models will have a main_grup property
     */
    protected $fillable = [
        'main_grup',
    ];
    
    /**
     * Get the main group of the model
     */
    public function getMainGroup()
    {
        return $this->main_grup;
    }
    
    /**
     * Set the main group of the model
     */
    public function setMainGroup($value)
    {
        $this->main_grup = $value;
        return $this;
    }
}
