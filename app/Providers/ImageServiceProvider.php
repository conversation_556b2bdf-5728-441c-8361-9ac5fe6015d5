<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Intervention\Image\ImageManager;

class ImageServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton('image', function ($app) {
            $config = config('image'); // config dosyanızdaki tüm array
            // Sadece driver ve options'u geçiriyoruz:
            return new ImageManager($config['driver'], $config['options']);
        });
    }

    public function boot()
    {
        //
    }
}
