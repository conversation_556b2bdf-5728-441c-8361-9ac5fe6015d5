<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Inspiring;
use Illuminate\Http\Request;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        [$message, $author] = str(Inspiring::quotes()->random())->explode('-');

        return [
            ...parent::share($request),
            'name' => config('app.name'),
            'quote' => ['message' => trim($message), 'author' => trim($author)],
            'auth' => [
                'user' => $request->user(),
            ],
            'flash' => [
                'message'  => $request->session()->get('message'),
                'error'  => $request->session()->get('error')
            ],
            'locale' => app()->getLocale(),
            'main_grup' => $request->session()->get('main_grup', 'proworker'),
            'themeColors' => [
                'primary' => config('theme.colors.primary', '119 176 170'),
                'secondary' => config('theme.colors.secondary', '19 93 102'),
                'general' => config('theme.colors.general', '0 60 67'),
                'accent' => config('theme.colors.accent', '227 254 247'),
            ],
        ];
    }
}
