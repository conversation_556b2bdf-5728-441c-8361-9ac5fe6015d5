<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check for locale in session, cookie, or default to app.locale
        $locale = null;
        
        // First priority: URL parameter (if this is a locale-setting request)
        if ($request->route() && $request->route()->getName() === 'api.locale.set' && $request->route('locale')) {
            $locale = $request->route('locale');
        }
        // Second priority: Session
        elseif (Session::has('locale')) {
            $locale = Session::get('locale');
        }
        // Third priority: <PERSON>ie
        elseif ($request->cookie('locale')) {
            $locale = $request->cookie('locale');
        }
        // Last resort: Default locale
        else {
            $locale = config('app.locale');
        }

        // Check if the locale is supported
        if (!in_array($locale, ['en', 'tr'])) {
            $locale = config('app.locale');
        }

        // Set the locale in session for consistency
        Session::put('locale', $locale);
        
        // Set the application locale
        App::setLocale($locale);
        
        return $next($request);
    }
}
