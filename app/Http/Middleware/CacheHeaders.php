<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class CacheHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $type
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, ?string $type = null)
    {
        $response = $next($request);
        
        if (!$response instanceof SymfonyResponse || $request->isMethod('POST') || $request->ajax()) {
            return $response;
        }
        
        // Default cache settings - no cache for dynamic content
        $response->headers->set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
        $response->headers->set('Pragma', 'no-cache');
        $response->headers->set('Expires', 'Sat, 01 Jan 2000 00:00:00 GMT');

        // Apply specific cache settings based on the $type parameter
        switch ($type) {
            case 'static':
                // For static resources like CSS, JS, images
                $response->headers->set('Cache-Control', 'public, max-age=31536000'); // 1 year
                $response->headers->set('Expires', gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
                break;
                
            case 'assets':
                // For public assets with longer cache
                $response->headers->set('Cache-Control', 'public, max-age=604800'); // 1 week
                $response->headers->set('Expires', gmdate('D, d M Y H:i:s', time() + 604800) . ' GMT');
                break;
                
            case 'public':
                // For public pages with shorter cache
                $response->headers->set('Cache-Control', 'public, max-age=3600'); // 1 hour
                $response->headers->set('Expires', gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
                break;
        }
        
        return $response;
    }
}
