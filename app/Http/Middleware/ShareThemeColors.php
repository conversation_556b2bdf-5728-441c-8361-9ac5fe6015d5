<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ShareThemeColors
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Share theme colors with all Inertia responses
        Inertia::share([
            'themeColors' => [
                'primary' => config('theme.colors.primary'),
                'secondary' => config('theme.colors.secondary'),
                'accent' => config('theme.colors.accent'),
            ],
        ]);

        return $next($request);
    }
}
