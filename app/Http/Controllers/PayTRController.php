<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Services\PayTRService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class PayTRController extends Controller
{
    protected $paytrService;

    public function __construct(PayTRService $paytrService)
    {
        $this->paytrService = $paytrService;
    }

    /**
     * Process PayTR payment
     */
    public function processPayment(Request $request)
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id',
        ]);

        $order = Order::with('orderItems')->findOrFail($request->order_id);

        // Prepare payment data
        $paymentData = [
            'order_id' => $order->id,
            'amount' => $order->total_amount,
            'email' => $order->customer_email,
            'name' => $order->customer_name,
            'phone' => $order->customer_phone,
            'address' => $order->billing_address['street'] . ', ' . 
                        $order->billing_address['city'] . ', ' . 
                        $order->billing_address['postal_code'] . ', ' . 
                        $order->billing_address['country'],
            'items' => $order->orderItems->map(function ($item) {
                return [
                    'name' => $item->product_name,
                    'price' => $item->unit_price,
                    'quantity' => $item->quantity,
                ];
            })->toArray(),
        ];

        $result = $this->paytrService->createPayment($paymentData);

        if ($result['success']) {
            // Update order with PayTR token
            $order->update([
                'payment_token' => $result['token'],
                'status' => 'payment_pending',
            ]);

            return response()->json([
                'success' => true,
                'payment_url' => $result['payment_url'],
            ]);
        } else {
            return response()->json([
                'success' => false,
                'error' => $result['error'],
            ], 400);
        }
    }

    /**
     * Handle PayTR success callback
     */
    public function success(Request $request)
    {
        $verification = $this->paytrService->verifyCallback($request->all());

        if ($verification['success']) {
            $order = Order::find($verification['order_id']);
            
            if ($order) {
                if ($verification['status'] === 'success') {
                    $order->update([
                        'status' => 'paid',
                        'payment_status' => 'completed',
                        'transaction_id' => $verification['transaction_id'],
                        'confirmed_at' => now(),
                    ]);

                    return Inertia::render('Front/Payment/Success', [
                        'order' => $order,
                        'message' => 'Ödemeniz başarıyla tamamlandı.',
                    ]);
                } else {
                    $order->update([
                        'status' => 'payment_failed',
                        'payment_status' => 'failed',
                    ]);

                    return Inertia::render('Front/Payment/Failed', [
                        'order' => $order,
                        'message' => 'Ödeme işlemi başarısız oldu.',
                    ]);
                }
            }
        }

        return Inertia::render('Front/Payment/Failed', [
            'message' => 'Ödeme doğrulama hatası.',
        ]);
    }

    /**
     * Handle PayTR failure callback
     */
    public function fail(Request $request)
    {
        $verification = $this->paytrService->verifyCallback($request->all());

        if ($verification['success']) {
            $order = Order::find($verification['order_id']);
            
            if ($order) {
                $order->update([
                    'status' => 'payment_failed',
                    'payment_status' => 'failed',
                ]);

                return Inertia::render('Front/Payment/Failed', [
                    'order' => $order,
                    'message' => 'Ödeme işlemi iptal edildi veya başarısız oldu.',
                ]);
            }
        }

        return Inertia::render('Front/Payment/Failed', [
            'message' => 'Ödeme işlemi başarısız oldu.',
        ]);
    }

    /**
     * Handle PayTR IPN (Instant Payment Notification)
     */
    public function ipn(Request $request)
    {
        $verification = $this->paytrService->verifyCallback($request->all());

        if ($verification['success']) {
            $order = Order::find($verification['order_id']);
            
            if ($order) {
                if ($verification['status'] === 'success') {
                    $order->update([
                        'status' => 'paid',
                        'payment_status' => 'completed',
                        'transaction_id' => $verification['transaction_id'],
                        'confirmed_at' => now(),
                    ]);

                    Log::info('PayTR IPN: Payment successful for order ' . $order->id);
                    return response('OK', 200);
                } else {
                    $order->update([
                        'status' => 'payment_failed',
                        'payment_status' => 'failed',
                    ]);

                    Log::warning('PayTR IPN: Payment failed for order ' . $order->id);
                    return response('OK', 200);
                }
            }
        }

        Log::error('PayTR IPN: Verification failed', $request->all());
        return response('FAIL', 400);
    }
}
