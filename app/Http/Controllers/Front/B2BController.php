<?php

namespace App\Http\Controllers\Front;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class B2BController extends Controller
{

    public function basketAdd(Request $request)
    {

        $basket = session('basket', []);

        // request’ten ürün bilgilerini al
        $product = $request->only(['sku', 'name', 'price', 'quantity']);

        // cart’a ekle
        $basket[] = $product;

        // session’a geri yaz
        session(['basket' => $basket]);

        // inertia veya JSON olarak geri dön
        return response()->json(['message' => 'OK', 'basket' => $basket]);
    }

    public function getBasket()
    {

        $basket = session('basket', []);

        return response()->json(['message' => 'OK', 'basket' => $basket]);
    }

    public function basket()
    {


        return Inertia::render('Front/B2B/Basket');
    }
}
