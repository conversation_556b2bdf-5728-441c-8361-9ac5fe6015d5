<?php

namespace App\Http\Controllers\Front;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use App\Models\Contact;
use App\Models\Category;
use App\Models\Gallery;
use App\Models\Page;
use App\Models\Product;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FrontController extends Controller
{
    /**
     * Show the home page
     */
    public function home(Request $request)
    {


        // Get main_grup from session, default to 'tr'
        $mainGrup = $request->session()->get('main_grup', 'tr');

        $featuredProducts = Product::with(['productImages' => function($query) {
                $query->where('is_active', true)->orderBy('order', 'asc');
            }])
            ->where('is_active', true)
            ->where('main_grup', $mainGrup)
            ->orderBy('order')
            ->limit(3)
            ->get();


        $latestGallery = Gallery::where('is_active', true)
            ->where('main_grup', $mainGrup)
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        // Get all main banners with main_grup matching session
        $mainBanners = Banner::where('is_active', true)
            ->where('main_grup', $mainGrup)
            ->where('type', 'main')
            ->where('main_grup', $mainGrup)
            ->orderBy('order')
            ->get();

        // For backward compatibility
        $allMainBanners = Banner::where('is_active', true)
            ->where('main_grup', $mainGrup)
            ->where('type', 'main')
            ->orderBy('order')
            ->get();

        $banner1 = Banner::where('is_active', true)
            ->where('main_grup', $mainGrup)

            ->where('type', 'banner1')
            ->orderBy('order')
            ->get();

        $banner2 = Banner::where('is_active', true)
            ->where('main_grup', $mainGrup)

            ->where('type', 'banner2')
            ->orderBy('order')
            ->get();

        $banner3 = Banner::where('is_active', true)
            ->where('main_grup', $mainGrup)

            ->where('type', 'banner3')
            ->orderBy('order')
            ->get();

        $banner4 = Banner::where('is_active', true)
            ->where('main_grup', $mainGrup)

            ->where('type', 'banner4')
            ->orderBy('order')
            ->get();

        return Inertia::render('Front/Home', [
            'featuredProducts' => $featuredProducts,
            'latestGallery' => $latestGallery,
            'mainBanners' => $mainBanners,
            'allMainBanners' => $mainBanners, // Use filtered banners instead of all
            'banner1' => $banner1,
            'banner2' => $banner2,
            'banner3' => $banner3,
            'banner4' => $banner4,
        ]);
    }

    /**
     * Show a specific page by slug
     */
    public function showPage(string $slug)
    {

        $page = Page::where('slug', $slug)
            ->firstOrFail();


        return Inertia::render('Front/PagesDetail', [
            'page' => $page,
        ]);
    }

    /**
     * List all products with filtering
     */
    public function listProducts(Request $request)
    {
        $products = Product::with(['category', 'productImages' => function($query) {
                $query->where('is_active', true)->orderBy('order', 'asc');
            }])
            ->where('is_active', true)
            ->orderBy('order')
            ->get();

        return Inertia::render('Front/Products', [
            'products' => $products,
        ]);
    }

    public function homee(Request $request)
    {
        $products = Product::with(['category', 'productImages' => function($query) {
                $query->where('is_active', true)->orderBy('order', 'asc');
            }])
            ->where('is_active', true)
            ->where('main_grup', 'tr')
            ->orderBy('order')
            ->get();

        return Inertia::render('Front/Products', [
            'products' => $products,
        ]);
    }

    public function solar(Request $request)
    {
        $products = Product::with(['category', 'productImages' => function($query) {
                $query->where('is_active', true)->orderBy('order', 'asc');
            }])
            ->where('is_active', true)
            ->where('main_grup', 'solar')
            ->orderBy('order')
            ->get();

        return Inertia::render('Front/Products', [
            'products' => $products,
        ]);
    }

    /**
     * Show a specific product by slug
     */
    public function showProduct(string $slug)
    {
        $product = Product::with(['category', 'productImages' => function($query) {
                $query->where('is_active', true)->orderBy('order', 'asc');
            }])
            ->where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Get related products from the same category
        $relatedProducts = collect([]);
        if ($product->category_id) {
            $relatedProducts = Product::with(['productImages' => function($query) {
                    $query->where('is_active', true)->orderBy('order', 'asc');
                }])
                ->where('category_id', $product->category_id)
                ->where('id', '!=', $product->id)
                ->where('is_active', true)
                ->limit(3)
                ->get();
        }

        return Inertia::render('Front/ProductDetail', [
            'product' => $product,
            'relatedProducts' => $relatedProducts,
        ]);
    }

    /**
     * List all gallery items
     */
    public function listGallery()
    {
        $galleries = Gallery::where('is_active', true)
            ->orderBy('order')
            ->get();

        return Inertia::render('Front/Gallery', [
            'galleries' => $galleries,
        ]);
    }


    public function showGallery($slug)
    {
        $gallery = Gallery::where('is_active', true)
            ->where('slug', $slug)
            ->firstOrFail();

        return Inertia::render('Front/GalleryShow', [
            'gallery' => $gallery,
        ]);
    }

    /**
     * Show the contact form
     */
    public function contactForm()
    {
        return Inertia::render('Front/Contact');
    }

    /**
     * Submit the contact form
     */
    public function submitContact(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'nullable|string|max:255',
            'message' => 'required|string',
        ]);

        Contact::create([
            'main_grup' => 'website',
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'] ?? null,
            'subject' => $validated['subject'] ?? null,
            'message' => $validated['message'],
        ]);

        return redirect()->back()->with('success', 'Your message has been sent successfully!');
    }

    /**
     * Show the first page with just the logo
     */
    public function first()
    {
        return Inertia::render('Front/First');
    }

    /**
     * Set main_grup session variable and redirect to home
     */
    public function setMainGrup(Request $request, $grup)
    {
        // Validate the group
        if (!in_array($grup, ['tr', 'tr'])) {
            return redirect()->route('home');
        }

        // Set the session variable
        $request->session()->put('main_grup', $grup);

        return redirect()->route('home');
    }

    public function b2b(Request $request)
    {



        return Inertia::render('Front/B2B/Home');
    }
    public function bakiyeOmurTablosu()
    {

        return Inertia::render('Front/BakiyeOmur', [
            'data' => 'omer',
        ]);
    }

    public function bakiyePMFTablosu()
    {

        return Inertia::render('Front/BakiyePMF', [
            'data' => 'omer',
        ]);
    }
}
