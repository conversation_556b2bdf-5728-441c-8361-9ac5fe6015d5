<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class PageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $pages = Page::orderBy('order')->get();

        return Inertia::render('Admin/Pages/Index', [
            'pages' => $pages,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/Pages/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|array',
            'title.tr' => 'required|string|max:255',
            'title.en' => 'required|string|max:255',
            'content' => 'required|array',
            'content.tr' => 'required|string',
            'content.en' => 'required|string',
            'meta_description' => 'nullable|array',
            'meta_keywords' => 'nullable|array',
            'is_active' => 'boolean',
            'order' => 'integer',
            'main_grup' => 'required|string|max:255',
        ]);

        // Generate slug from title
        $slug = Str::slug($validated['title']['tr']);

        // Check if slug exists
        $count = Page::where('slug', $slug)->count();
        if ($count > 0) {
            $slug = $slug . '-' . ($count + 1);
        }

        Page::create([
            'title' => $validated['title'],
            'content' => $validated['content'],
            'meta_description' => $validated['meta_description'] ?? null,
            'meta_keywords' => $validated['meta_keywords'] ?? null,
            'is_active' => $validated['is_active'] ?? true,
            'order' => $validated['order'] ?? 0,
            'main_grup' => $validated['main_grup'],
            'slug' => $validated['main_grup'] . '-' . $slug,
        ]);

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $page = Page::findOrFail($id);

        return Inertia::render('Admin/Pages/Show', [
            'page' => $page,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $page = Page::findOrFail($id);

        return Inertia::render('Admin/Pages/Edit', [
            'page' => $page,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $page = Page::findOrFail($id);

        $validated = $request->validate([
            'title' => 'required|array',
            'title.tr' => 'required|string|max:255',
            'title.en' => 'required|string|max:255',
            'content' => 'required|array',
            'content.tr' => 'required|string',
            'content.en' => 'required|string',
            'meta_description' => 'nullable|array',
            'meta_keywords' => 'nullable|array',
            'is_active' => 'boolean',
            'order' => 'integer',
            'main_grup' => 'required|string|max:255',
        ]);

        $page->update([
            'title' => $validated['title'],
            'content' => $validated['content'],
            'meta_description' => $validated['meta_description'] ?? null,
            'meta_keywords' => $validated['meta_keywords'] ?? null,
            'is_active' => $validated['is_active'] ?? true,
            'order' => $validated['order'] ?? 0,
            'main_grup' => $validated['main_grup'],
        ]);

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $page = Page::findOrFail($id);
        $page->delete();


        return redirect()->route('admin.pages.index')
            ->with('success', 'Page deleted successfully.');
    }
}
