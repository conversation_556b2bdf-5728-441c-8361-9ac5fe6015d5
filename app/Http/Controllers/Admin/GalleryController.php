<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Gallery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

use Intervention\Image\Laravel\Facades\Image;

class GalleryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $galleries = Gallery::orderBy('order')->get();
        
        return Inertia::render('Admin/Galleries/Index', [
            'galleries' => $galleries,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/Galleries/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title'             => 'required|array',
            'title.tr'          => 'required|string|max:255',
            'title.en'          => 'required|string|max:255',
            'description'       => 'nullable|array',
            'image'             => 'required|image|max:2048',
            'is_active'         => 'boolean',
            'order'             => 'integer',
            'main_grup'         => 'required|string|max:255',
        ]);
    
        // Generate slug from title
        $slug  = Str::slug($validated['title']['tr']);
        $count = \App\Models\Gallery::where('slug', $slug)->count();
        if ($count > 0) {
            $slug .= '-' . ($count + 1);
        }
    
        $imagePath = null;
        $thumbnailPath = null;
    
        // Handle image upload
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
    
            // Orijinal resmi 'galleries' klasörüne kaydet (public disk)
            $originalPath = $image->storeAs('galleries', $imageName, 'public');
            $imagePath = 'storage/' . $originalPath;
    
            // **Thumbnail oluştur ve kaydet**
            // Thumbnail için benzersiz bir isim üret (public disk yolu içerisinde 'galleries/thumbnails' alt dizini)
            $thumbRelativePath = 'galleries/thumbnails/' . Str::random() . '.' . $image->getClientOriginalExtension();
    
            // Intervention Image kullanarak thumbnail oluştur
            $thumbnail = Image::read($image)
                ->resize(300, 200)
                ->encodeByExtension($image->getClientOriginalExtension(), quality: 70);
    
            // Thumbnail'i public disk'e kaydet (dosya storage/app/public altında oluşturulacak)
            Storage::disk('public')->put($thumbRelativePath, (string) $thumbnail);
    
            // DB'ye kaydetmek için thumbnail yolunu "storage/" öneki ile belirle
            $thumbnailPath = 'storage/' . $thumbRelativePath;
        }
    
        \App\Models\Gallery::create([
            'title'          => $validated['title'],
            'description'    => $validated['description'] ?? null,
            'image_path'     => $imagePath,
            'thumbnail_path' => $thumbnailPath,
            'is_active'      => $validated['is_active'] ?? true,
            'order'          => $validated['order'] ?? 0,
            'main_grup'      => $validated['main_grup'],
            'slug'           => $slug,
        ]);
    
        return redirect()->route('admin.gallery.index')
            ->with('success', 'Gallery item created successfully.');
    }
    
    
    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $gallery = Gallery::findOrFail($id);
        
        return Inertia::render('Admin/Galleries/Show', [
            'gallery' => $gallery,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $gallery = Gallery::findOrFail($id);
        
        return Inertia::render('Admin/Galleries/Edit', [
            'gallery' => $gallery,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $gallery = Gallery::findOrFail($id);
    
        $validated = $request->validate([
            'title' => 'required|array',
            'title.tr' => 'required|string|max:255',
            'title.en' => 'required|string|max:255',
            'description' => 'nullable|array',
            'image' => 'nullable|image|max:2048',
            'is_active' => 'boolean',
            'order' => 'integer',
            'main_grup' => 'required|string|max:255',
        ]);
    
        $updateData = [
            'title' => $validated['title'],
            'description' => $validated['description'] ?? null,
            'is_active' => $validated['is_active'] ?? true,
            'order' => $validated['order'] ?? 0,
            'main_grup' => $validated['main_grup'],
        ];
    
        // Eğer yeni bir resim yüklenmişse
        if ($request->hasFile('image')) {
            $upload = $request->file('image');
            $imageName = time() . '.' . $upload->getClientOriginalExtension();
    
            // Eski resim ve thumbnail'ı sil
            if ($gallery->image_path) {
                Storage::disk('public')->delete(str_replace('storage/', '', $gallery->image_path));
            }
    
            if ($gallery->thumbnail_path) {
                Storage::disk('public')->delete(str_replace('storage/', '', $gallery->thumbnail_path));
            }
    
            // **Orijinal resmi kaydet**
            $imagePath = $upload->storeAs('galleries', $imageName, 'public');
    
            // **Thumbnail oluştur ve kaydet**
            $thumbnailPath = 'galleries/thumbnails/' . Str::random() . '.' . $upload->getClientOriginalExtension();
    
            $thumbnail = Image::read($upload)
                ->resize(300, 200)
                ->encodeByExtension($upload->getClientOriginalExtension(), quality: 70);
    
            Storage::disk('public')->put($thumbnailPath, (string) $thumbnail);
    
            // **Veri tabanına kaydetmek için yolları güncelle**
            $updateData['image_path'] = 'storage/' . $imagePath;
            $updateData['thumbnail_path'] = 'storage/' . $thumbnailPath;
        }
    
        $gallery->update($updateData);
    
        return redirect()->route('admin.gallery.index')
            ->with('success', 'Gallery item updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $gallery = Gallery::findOrFail($id);
        
        // Delete image and thumbnail if they exist
        if ($gallery->image_path) {
            Storage::disk('public')->delete(str_replace('storage/', '', $gallery->image_path));
        }
        
        if ($gallery->thumbnail_path) {
            Storage::disk('public')->delete(str_replace('storage/', '', $gallery->thumbnail_path));
        }
        
        $gallery->delete();
        
        return redirect()->route('admin.galleries.index')
            ->with('success', 'Gallery item deleted successfully.');
    }
}
