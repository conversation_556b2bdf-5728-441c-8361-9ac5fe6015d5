<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Spatie\Backup\BackupDestination\Backup;
use Spatie\Backup\BackupDestination\BackupDestination;
use Spatie\Backup\Tasks\Backup\BackupJob;
use Symfony\Component\HttpFoundation\StreamedResponse;

class BackupController extends Controller
{
    public function index()
    {
        $backupFiles = collect(Storage::files('ProWork'))
            ->filter(function ($file) {
                return str_ends_with($file, '.zip');
            })
            ->map(function ($file) {
                return [
                    'name' => basename($file),
                    'size' => Storage::size($file),
                    'last_modified' => Storage::lastModified($file),
                    'path' => $file,
                ];
            })
            ->sortByDesc('last_modified')
            ->values();

        return Inertia::render('Admin/Backup/Index', [
            'backups' => $backupFiles
        ]);
    }

    public function create()
    {
        try {
            // Veritabanı yedeklemesi atla, sadece dosyaları yedekle
            \Artisan::call('backup:run --only-files');
            return redirect()->back()->with('success', 'Dosya yedeklemesi başlatıldı.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Yedekleme oluşturulurken hata: ' . $e->getMessage());
        }
    }

    public function download($filename)
    {
        $path = 'ProWork/' . $filename;

        if (!Storage::exists($path)) {
            return redirect()->back()->with('error', 'Yedek dosyası bulunamadı.');
        }

        return Storage::download($path);
    }

    public function destroy($filename)
    {
        $path = 'ProWork/' . $filename;

        if (!Storage::exists($path)) {
            return redirect()->back()->with('error', 'Yedek dosyası bulunamadı.');
        }

        Storage::delete($path);
        return redirect()->back()->with('success', 'Yedek dosyası silindi.');
    }
}
