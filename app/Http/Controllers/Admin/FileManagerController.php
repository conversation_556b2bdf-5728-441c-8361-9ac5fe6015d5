<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FileManager;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class FileManagerController extends Controller
{
    /**
     * Display a listing of the files.
     */
    public function index(Request $request)
    {
        $query = FileManager::query();

        // Filter by file type
        if ($request->has('type')) {
            $type = $request->input('type');
            if ($type === 'image') {
                $query->whereIn('mime_type', [
                    'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/svg+xml', 'image/webp',
                ]);
            } elseif ($type === 'document') {
                $query->whereIn('mime_type', [
                    'application/pdf', 'application/msword', 
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'application/vnd.ms-excel', 
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'application/vnd.ms-powerpoint', 
                    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                    'text/plain',
                ]);
            }
        }

        // Filter by search term
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('original_filename', 'like', "%{$search}%")
                  ->orWhere('mime_type', 'like', "%{$search}%");
            });
        }

        // Filter by folder
        if ($request->has('folder')) {
            $query->where('folder', $request->input('folder'));
        }

        $files = $query->orderBy('created_at', 'desc')->paginate(20);

        // Check if this is a regular AJAX request or explicitly requesting JSON format
        if (!$request->header('X-Inertia') && 
            (($request->ajax() && $request->header('X-Requested-With') === 'XMLHttpRequest') || 
            $request->input('format') === 'json')) {
            return response()->json([
                'files' => $files->items(),
                'meta' => [
                    'current_page' => $files->currentPage(),
                    'from' => $files->firstItem(),
                    'last_page' => $files->lastPage(),
                    'links' => $files->links()->toHtml(),
                    'path' => $files->path(),
                    'per_page' => $files->perPage(),
                    'to' => $files->lastItem(),
                    'total' => $files->total(),
                ],
            ]);
        }

        // For all other requests, including Inertia requests, return the Inertia response
        return Inertia::render('Admin/FileManager/Index', [
            'files' => $files,
            'filters' => $request->only(['search', 'type', 'folder']),
        ]);
    }

    /**
     * Show the form for creating a new file.
     */
    public function create()
    {
        return Inertia::render('Admin/FileManager/Create');
    }

    /**
     * Store a newly uploaded file in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
            'folder' => 'nullable|string|max:255',
        ]);

        $file = $request->file('file');
        $folder = $request->input('folder', 'uploads');
        
        // Generate a unique filename
        $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
        
        // Store the file
        $path = $file->storeAs($folder, $filename, 'public');
        
        // Create the file record
        $fileManager = FileManager::create([
            'filename' => $filename,
            'original_filename' => $file->getClientOriginalName(),
            'file_path' => $path,
            'mime_type' => $file->getMimeType(),
            'size' => $file->getSize(),
            'extension' => $file->getClientOriginalExtension(),
            'folder' => $folder,
            'user_id' => Auth::id(),
            'fileable_type' => 'App\\Models\\User', // Varsayılan olarak User modeline bağlı
            'fileable_id' => Auth::id(), // Yükleyen kullanıcıya bağlı
        ]);

        if ($request->wantsJson()) {
            return response()->json([
                'file' => $fileManager,
                'message' => 'File uploaded successfully',
            ]);
        }

        return redirect()->route('admin.files.index')
            ->with('success', 'File uploaded successfully');
    }

    /**
     * Display the specified file.
     */
    public function show(FileManager $file)
    {
        return Inertia::render('Admin/FileManager/Show', [
            'file' => $file,
        ]);
    }

    /**
     * Show the form for editing the specified file.
     */
    public function edit(FileManager $file)
    {
        return Inertia::render('Admin/FileManager/Edit', [
            'file' => $file,
        ]);
    }

    /**
     * Update the specified file in storage.
     */
    public function update(Request $request, FileManager $file)
    {
        $request->validate([
            'original_filename' => 'required|string|max:255',
            'folder' => 'nullable|string|max:255',
        ]);

        // Update file metadata
        $file->update([
            'original_filename' => $request->input('original_filename'),
            'folder' => $request->input('folder', $file->folder),
        ]);

        if ($request->wantsJson()) {
            return response()->json([
                'file' => $file,
                'message' => 'File updated successfully',
            ]);
        }

        return redirect()->route('admin.files.index')
            ->with('success', 'File updated successfully');
    }

    /**
     * Remove the specified file from storage.
     */
    public function destroy(FileManager $file)
    {
        // The file will be deleted from storage via the model's boot method
        $file->delete();

        return redirect()->route('admin.files.index')
            ->with('success', 'File deleted successfully');
    }

    /**
     * Upload multiple files at once.
     */
    public function bulkUpload(Request $request)
    {
        $request->validate([
            'files' => 'required|array',
            'files.*' => 'file|max:10240', // 10MB max per file
            'folder' => 'nullable|string|max:255',
        ]);

        $uploadedFiles = [];
        $folder = $request->input('folder', 'uploads');

        foreach ($request->file('files') as $file) {
            // Generate a unique filename
            $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
            
            // Store the file
            $path = $file->storeAs($folder, $filename, 'public');
            
            // Create the file record
            $fileManager = FileManager::create([
                'filename' => $filename,
                'original_filename' => $file->getClientOriginalName(),
                'file_path' => $path,
                'mime_type' => $file->getMimeType(),
                'size' => $file->getSize(),
                'extension' => $file->getClientOriginalExtension(),
                'folder' => $folder,
                'user_id' => Auth::id(),
                'fileable_type' => 'App\\Models\\User', // Varsayılan olarak User modeline bağlı
                'fileable_id' => Auth::id(), // Yükleyen kullanıcıya bağlı
            ]);

            $uploadedFiles[] = $fileManager;
        }

        if ($request->wantsJson()) {
            return response()->json([
                'files' => $uploadedFiles,
                'message' => count($uploadedFiles) . ' files uploaded successfully',
            ]);
        }

        return redirect()->route('admin.files.index')
            ->with('success', count($uploadedFiles) . ' files uploaded successfully');
    }
}
