<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Barryvdh\DomPDF\Facade\Pdf;

class OrderController extends Controller
{
    /**
     * Display a listing of orders.
     */
    public function index(Request $request)
    {
        $query = Order::with(['orderItems'])
            ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Sort functionality
        if ($request->filled('sort')) {
            $sortField = $request->sort;
            $sortDirection = $request->direction === 'desc' ? 'desc' : 'asc';

            switch ($sortField) {
                case 'order_number':
                case 'customer_name':
                case 'total_amount':
                case 'status':
                case 'created_at':
                    $query->orderBy($sortField, $sortDirection);
                    break;
            }
        }

        $orders = $query->paginate(20)->withQueryString();

        // Calculate statistics
        $stats = [
            'total_orders' => Order::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'completed_orders' => Order::where('status', 'completed')->count(),
            'cancelled_orders' => Order::where('status', 'cancelled')->count(),
            'total_revenue' => Order::where('status', 'completed')->sum('total_amount'),
        ];

        return Inertia::render('Admin/Orders/Index', [
            'orders' => $orders,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status', 'date_from', 'date_to', 'sort', 'direction']),
            'statuses' => [
                'pending' => 'Beklemede',
                'payment_pending' => 'Ödeme Bekliyor',
                'paid' => 'Ödendi',
                'payment_failed' => 'Ödeme Başarısız',
                'processing' => 'İşleniyor',
                'shipped' => 'Kargoya Verildi',
                'delivered' => 'Teslim Edildi',
                'completed' => 'Tamamlandı',
                'cancelled' => 'İptal Edildi',
                'refunded' => 'İade Edildi',
            ],
        ]);
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order)
    {
        $order->load(['orderItems']);

        return Inertia::render('Admin/Orders/Show', [
            'order' => $order,
            'statuses' => [
                'pending' => 'Beklemede',
                'payment_pending' => 'Ödeme Bekliyor',
                'paid' => 'Ödendi',
                'payment_failed' => 'Ödeme Başarısız',
                'processing' => 'İşleniyor',
                'shipped' => 'Kargoya Verildi',
                'delivered' => 'Teslim Edildi',
                'completed' => 'Tamamlandı',
                'cancelled' => 'İptal Edildi',
                'refunded' => 'İade Edildi',
            ],
        ]);
    }

    /**
     * Update the order status.
     */
    public function updateStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,payment_pending,paid,payment_failed,processing,shipped,delivered,completed,cancelled,refunded',
        ]);

        $order->update([
            'status' => $request->status,
        ]);

        return redirect()->back()->with('success', 'Order status updated successfully.');
    }

    /**
     * Update order notes.
     */
    public function updateNotes(Request $request, Order $order)
    {
        $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $order->update([
            'admin_notes' => $request->admin_notes,
        ]);

        return redirect()->back()->with('success', 'Order notes updated successfully.');
    }

    /**
     * Delete the specified order.
     */
    public function destroy(Order $order)
    {
        // Only allow deletion of cancelled orders
        if ($order->status !== 'cancelled') {
            return redirect()->back()->withErrors(['error' => 'Only cancelled orders can be deleted.']);
        }

        $order->orderItems()->delete();
        $order->delete();

        return redirect()->route('admin.orders.index')->with('success', 'Order deleted successfully.');
    }

    /**
     * Export orders to CSV.
     */
    public function export(Request $request)
    {
        $query = Order::with(['orderItems']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->get();

        $filename = 'orders_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($orders) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");

            // CSV headers
            fputcsv($file, [
                'Order Number',
                'Customer Name',
                'Customer Email',
                'Customer Phone',
                'Total Amount',
                'Status',
                'Payment Method',
                'Order Date',
                'Items Count',
            ]);

            foreach ($orders as $order) {
                fputcsv($file, [
                    $order->order_number,
                    $order->customer_name,
                    $order->customer_email,
                    $order->customer_phone,
                    $order->total_amount,
                    $order->status,
                    $order->payment_method,
                    $order->created_at->format('Y-m-d H:i:s'),
                    $order->orderItems->count(),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Generate PDF invoice for the order.
     */
    public function generatePDF(Order $order)
    {
        $order->load(['orderItems']);

        $statuses = [
            'pending' => 'Beklemede',
            'processing' => 'İşleniyor',
            'shipped' => 'Kargoya Verildi',
            'delivered' => 'Teslim Edildi',
            'completed' => 'Tamamlandı',
            'cancelled' => 'İptal Edildi',
            'refunded' => 'İade Edildi',
        ];

        $pdf = Pdf::loadView('admin.orders.invoice', [
            'order' => $order,
            'statuses' => $statuses,
        ]);

        // PDF ayarları
        $pdf->setPaper('A4', 'portrait');
        $pdf->setOptions([
            'isHtml5ParserEnabled' => true,
            'isPhpEnabled' => true,
            'defaultFont' => 'DejaVu Sans',
            'isFontSubsettingEnabled' => true,
        ]);

        $filename = 'siparis_' . $order->order_number . '_' . now()->format('Y-m-d') . '.pdf';

        return $pdf->download($filename);
    }

    /**
     * Print order (view PDF in browser).
     */
    public function print(Order $order)
    {
        $order->load(['orderItems']);

        $statuses = [
            'pending' => 'Beklemede',
            'processing' => 'İşleniyor',
            'shipped' => 'Kargoya Verildi',
            'delivered' => 'Teslim Edildi',
            'completed' => 'Tamamlandı',
            'cancelled' => 'İptal Edildi',
            'refunded' => 'İade Edildi',
        ];

        $pdf = Pdf::loadView('admin.orders.invoice', [
            'order' => $order,
            'statuses' => $statuses,
        ]);

        // PDF ayarları
        $pdf->setPaper('A4', 'portrait');
        $pdf->setOptions([
            'isHtml5ParserEnabled' => true,
            'isPhpEnabled' => true,
            'defaultFont' => 'DejaVu Sans',
            'isFontSubsettingEnabled' => true,
        ]);

        return $pdf->stream('siparis_' . $order->order_number . '.pdf');
    }
}
