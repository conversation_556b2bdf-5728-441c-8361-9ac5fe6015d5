<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Menu;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class MenuController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $menus = Menu::with('children')
            ->whereNull('parent_id')
            ->orderBy('order')
            ->get();
        
        return Inertia::render('Admin/Menus/Index', [
            'menus' => $menus,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $parentMenus = Menu::whereNull('parent_id')
            ->orderBy('order')
            ->get();
        
        return Inertia::render('Admin/Menus/Create', [
            'parentMenus' => $parentMenus,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|array',
            'name.tr' => 'required|string|max:255',
            'name.en' => 'required|string|max:255',
            'url' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:menus,id',
            'is_active' => 'boolean',
            'order' => 'integer',
            'main_grup' => 'required|string|max:255',
            'liste' => 'boolean',
        ]);
        
        // Generate slug from name
        $slug = Str::slug($validated['name']['tr']);
        
        // Check if slug exists
        $count = Menu::where('slug', $slug)->count();
        if ($count > 0) {
            $slug = $slug . '-' . ($count + 1);
        }
        
        Menu::create([
            'name' => $validated['name'],
            'url' => $validated['url'],
            'parent_id' => $validated['parent_id'] ?? null,
            'is_active' => $validated['is_active'] ?? true,
            'order' => $validated['order'] ?? 0,
            'main_grup' => $validated['main_grup'],
            'slug' => $slug,
            'liste' => $validated['liste'] ?? false,
        ]);
        
        return redirect()->route('admin.menus.index')
            ->with('success', 'Menu created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $menu = Menu::with('children', 'parent')->findOrFail($id);
        
        return Inertia::render('Admin/Menus/Show', [
            'menu' => $menu,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $menu = Menu::findOrFail($id);
        
        $parentMenus = Menu::whereNull('parent_id')
            ->where('id', '!=', $id)
            ->orderBy('order')
            ->get();
        
        return Inertia::render('Admin/Menus/Edit', [
            'menu' => $menu,
            'parentMenus' => $parentMenus,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $menu = Menu::findOrFail($id);
        
        $validated = $request->validate([
            'name' => 'required|array',
            'name.tr' => 'required|string|max:255',
            'name.en' => 'required|string|max:255',
            'url' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:menus,id',
            'is_active' => 'boolean',
            'order' => 'integer',
            'main_grup' => 'required|string|max:255',
            'liste' => 'boolean',
        ]);
        
        // Prevent menu from being its own parent
        if ($validated['parent_id'] == $id) {
            return redirect()->back()->withErrors(['parent_id' => 'A menu cannot be its own parent.']);
        }
        
        $menu->update([
            'name' => $validated['name'],
            'url' => $validated['url'],
            'parent_id' => $validated['parent_id'] ?? null,
            'is_active' => $validated['is_active'] ?? true,
            'order' => $validated['order'] ?? 0,
            'main_grup' => $validated['main_grup'],
            'liste' => $validated['liste'] ?? false,
        ]);
        
        return redirect()->route('admin.menus.index')
            ->with('success', 'Menu updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    
    public function destroy(string $id)
    {
        $menu = Menu::findOrFail($id);
        
        // Check if menu has children
        if ($menu->children()->count() > 0) {
            return redirect()->back()->withErrors(['error' => 'Cannot delete menu with children. Delete children first.']);
        }
        
        $menu->delete();
       return ; 
        return redirect()->route('admin.menus.index')
            ->with('success', 'Menu deleted successfully.');
    }
}
