<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;
class BannerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $type = $request->input('type', 'all');

        $query = Banner::orderBy('order');

        if ($type !== 'all') {
            $query->where('type', $type);
        }

        $banners = $query->get();

        return Inertia::render('Admin/Banners/Index', [
            'banners' => $banners,
            'currentType' => $type,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/Banners/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $validated = $request->validate([
            'title' => 'required|array',
            'title.tr' => 'required|string|max:255',
            'title.en' => 'required|string|max:255',
            'image' => 'required|string',
            'link' => 'nullable|string',
            'button_text' => 'nullable|array',
            'button_text.tr' => 'nullable|string|max:50',
            'button_text.en' => 'nullable|string|max:50',
            'type' => 'required|string|in:main,banner1,banner2,banner3,banner4',
            'order' => 'integer',
            'is_active' => 'boolean',
            'main_grup' => 'required|string|max:255',
        ]);

        try {
            $banner = Banner::create([
                'title' => $validated['title'],
                'image' => $validated['image'],
                'link' => $validated['link'] ?? null,
                'button_text' => $validated['button_text'] ?? null,
                'type' => $validated['type'],
                'is_active' => $validated['is_active'] ?? true,
                'order' => $validated['order'] ?? 0,
                'main_grup' => $validated['main_grup'],
            ]);


            Log::info('Banner başarıyla oluşturuldu', ['banner_id' => $banner->id]);

        } catch (\Exception $e) {
            Log::error('Banner oluşturulamadı', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $validated
            ]);

            return redirect()->back()
                ->withErrors(['error' => 'Banner could not be created.'])
                ->withInput();
        }

        return redirect()->route('admin.banners.index')
            ->with('success', 'Banner created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $banner = Banner::findOrFail($id);

        return Inertia::render('Admin/Banners/Show', [
            'banner' => $banner,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $banner = Banner::findOrFail($id);

        return Inertia::render('Admin/Banners/Edit', [
            'banner' => $banner,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $banner = Banner::findOrFail($id);

        $validated = $request->validate([
            'title' => 'required|array',
            'title.tr' => 'required|string|max:255',
            'title.en' => 'required|string|max:255',
            'image' => 'required|string',
            'link' => 'nullable|string',
            'button_text' => 'nullable|array',
            'button_text.tr' => 'nullable|string|max:50',
            'button_text.en' => 'nullable|string|max:50',
            'type' => 'required|string|in:main,banner1,banner2,banner3,banner4',
            'order' => 'integer',
            'is_active' => 'boolean',
            'main_grup' => 'required|string|max:255',
        ]);

        $banner->update([
            'title' => $validated['title'],
            'image' => $validated['image'],
            'link' => $validated['link'] ?? null,
            'button_text' => $validated['button_text'] ?? null,
            'type' => $validated['type'],
            'is_active' => $validated['is_active'] ?? true,
            'order' => $validated['order'] ?? 0,
            'main_grup' => $validated['main_grup'],
        ]);

        return redirect()->route('admin.banners.index')
            ->with('success', 'Banner updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $banner = Banner::findOrFail($id);
        $banner->delete();

        return redirect()->route('admin.banners.index')
            ->with('success', 'Banner deleted successfully.');
    }
}
