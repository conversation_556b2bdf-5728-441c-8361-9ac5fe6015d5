<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\ProductImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Intervention\Image\Laravel\Facades\Image;

use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $products = Product::with('category')->orderBy('id', 'desc')->paginate(10)->through(fn($product) => [
            'id' => $product->id,
            'name' => $product->getTranslations('name'),
            'slug' => $product->slug,
            'price' => $product->price,
            'sku' => $product->sku,
            'is_active' => $product->is_active,
            'order' => $product->order,
            'category' => $product->category ? [
                'id' => $product->category->id,
                'name' => $product->category->getTranslations('name'),
                'slug' => $product->category->slug,
            ] : null,
        ]);

        return Inertia::render('Admin/Products/Index', [
            'products' => $products
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::where('is_active', true)->orderBy('name->tr')->get();

        return Inertia::render('Admin/Products/Create', [
            'categories' => $categories,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|array',
            'name.tr' => 'required|string|max:255',
            'name.en' => 'required|string|max:255',
            'description' => 'required|array',
            'description.tr' => 'required|string',
            'description.en' => 'required|string',
            'features' => 'nullable|array',
            'price' => 'required|numeric',
            'sku' => 'nullable|string|max:255',
            'stock' => 'nullable|integer',
            'is_active' => 'boolean',
            'order' => 'integer',
            'meta_description' => 'nullable|array',
            'meta_keywords' => 'nullable|array',
            'main_grup' => 'required|string|max:255',
            'category_id' => 'nullable|exists:categories,id',
        ]);

        // Generate slug from name
        $slug = Str::slug($validated['name']['tr']);

        // Check if slug exists
        $count = Product::where('slug', $slug)->count();
        if ($count > 0) {
            $slug = $slug . '-' . ($count + 1);
        }

        // Translatable alanları doğrudan create içinde set etmiyoruz.
        $product = Product::create([
            'name' => $validated['name'],
            'price' => $validated['price'],
            'sku' => $validated['sku'] ?? null,
            'stock' => $validated['stock'] ?? 0,
            'is_active' => $validated['is_active'] ?? true,
            'order' => $validated['order'] ?? 0,
            'main_grup' => $validated['main_grup'],
            'category_id' => $validated['category_id'] ?? null,
            'slug' => $validated['main_grup'] . '-' . $slug,

        ]);

        // Çok dilli alanların çevirilerini ayarlıyoruz.
        $product->setTranslations('description', $validated['description']);
        $product->setTranslations('features', $validated['features'] ?? []);
        $product->setTranslations('meta_description', $validated['meta_description'] ?? []);
        $product->setTranslations('meta_keywords', $validated['meta_keywords'] ?? []);
        $product->save();


        return redirect()->route('admin.products.index')
            ->with('message', 'Product created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $product = Product::with('category')->findOrFail($id);

        return Inertia::render('Admin/Products/Show', [
            'product' => $product,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $product = Product::findOrFail($id);
        $categories = Category::where('is_active', true)->orderBy('name->tr')->get();

        return Inertia::render('Admin/Products/Edit', [
            'product' => $product,
            'categories' => $categories,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $product = Product::findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|array',
            'name.tr' => 'required|string|max:255',
            'name.en' => 'required|string|max:255',
            'description' => 'required|array',
            'features' => 'nullable|array',
            'price' => 'required|numeric',
            'sku' => 'nullable|string|max:255',
            'stock' => 'nullable|integer',
            'is_active' => 'boolean',
            'order' => 'integer',
            'meta_description' => 'nullable|array',
            'meta_keywords' => 'nullable|array',
            'main_grup' => 'required|string|max:255',
            'category_id' => 'nullable|exists:categories,id',
        ]);

        $product->update([
            'name' => $validated['name'],
            'description' => $validated['description'],
            'features' => $validated['features'] ?? null,
            'price' => $validated['price'],
            'sku' => $validated['sku'] ?? null,
            'stock' => $validated['stock'] ?? 0,
            'is_active' => $validated['is_active'] ?? true,
            'order' => $validated['order'] ?? 0,
            'meta_description' => $validated['meta_description'] ?? null,
            'meta_keywords' => $validated['meta_keywords'] ?? null,
            'main_grup' => $validated['main_grup'],
            'category_id' => $validated['category_id'] ?? null,
        ]);


        return redirect()->route('admin.products.index')
            ->with('message', 'Ürün başarıyla güncellendi');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $product = Product::findOrFail($id);
        $product->delete();
        return redirect()->route('admin.products.index')
            ->with('message', 'Ürün başarıyla silindi');
    }

    public function imageUpload(Request $request)
    {
        Log::info('Image upload started', [
            'request_data' => $request->all(),
            'has_file' => $request->hasFile('image'),
            'file_info' => $request->hasFile('image') ? [
                'name' => $request->file('image')->getClientOriginalName(),
                'size' => $request->file('image')->getSize(),
                'mime' => $request->file('image')->getMimeType(),
            ] : null
        ]);

        try {
            $validated = $request->validate([
                'image'      => 'required|image|max:10240', // 10MB max
                'product_id' => 'required|exists:products,id',
                'is_active'  => 'sometimes|boolean',
                'order'      => 'nullable|integer',
            ]);

            Log::info('Validation passed', $validated);

            if ($request->hasFile('image')) {
                $image = $request->file('image');
                $product_id = $request->input('product_id');
                $imageName = time() . '.' . $image->getClientOriginalExtension();

                // Orijinal resmi storeAs yöntemiyle kaydet
                $originalPath = $image->storeAs('products', $imageName, 'public');
                $imagePath = 'storage/' . $originalPath;

                // Şimdilik thumbnail olmadan devam edelim
                $thumbnailPath = $imagePath; // Orijinal resmi thumbnail olarak kullan

                Log::info('Image processing completed', [
                    'original_path' => $imagePath,
                    'thumbnail_path' => $thumbnailPath
                ]);

                // Veritabanına kayıt ekle (ProductImage modelinde thumbnail_path sütunu olmalı)
                $productImage = \App\Models\ProductImage::create([
                    'product_id'     => $product_id,
                    'image_path'     => $imagePath,
                    'thumbnail_path' => $thumbnailPath,
                    'is_active'      => $validated['is_active'] ?? true,
                    'order'          => $validated['order'] ?? 0,
                ]);

                Log::info('Image uploaded successfully', [
                    'product_id' => $product_id,
                    'image_id' => $productImage->id,
                    'image_path' => $imagePath,
                    'thumbnail_path' => $thumbnailPath
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Image uploaded successfully',
                    'image_path' => $imagePath,
                    'thumbnail_path' => $thumbnailPath,
                ]);
            }

            return response()->json(['message' => 'No image provided'], 400);
        } catch (\Exception $e) {
            Log::error('Image upload error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'request_data' => $request->except(['image']) // Resim dosyasını log'a ekleme
            ]);
            return response()->json([
                'message' => 'Image upload failed: ' . $e->getMessage(),
                'error'   => $e->getMessage(),
                'debug'   => [
                    'file' => basename($e->getFile()),
                    'line' => $e->getLine(),
                ]
            ], 500);
        }
    }


    public function getProductImages($productId)
    {


        $product = ProductImage::where('product_id', $productId)->get();


        // Eğer $product bir Product model örneği değilse (muhtemelen geçersiz bir ID)
        if (!$product) {
            return response()->json(['message' => 'Ürün bulunamadı veya resim yok'], 404); // 404 Not Found döndür
        }

        return response()->json($product); // Resim URL'lerini JSON olarak döndürür.

    }

    public function deleteProductImage(ProductImage $productImage)
    {
        $productImage->delete();

        return response()->json(['message' => 'Resim başarıyla silindi']);
    }

    public function imageOrderUpadte(Request $request)
    {
        $validated = $request->validate([
            'order' => 'required|integer',
            'image_id' => 'required|exists:product_images,id',
        ]);

        $image = ProductImage::findOrFail($validated['image_id']);
        $image->update(['order' => $validated['order']]);

        return response()->json(['message' => 'Sıra başarıyla güncellendi!']);
    }
}
