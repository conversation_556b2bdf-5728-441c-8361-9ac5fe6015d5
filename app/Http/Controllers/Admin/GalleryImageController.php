<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Gallery;
use App\Models\GalleryImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage; // Ekleyin
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;


class GalleryImageController extends Controller
{
    /**
     * Belirli bir galerinin tüm resimlerini listeler.
     */
    public function show($galleryId)
    {
        $gallery = Gallery::findOrFail($galleryId);
        $images = $gallery->galleryImages()->orderBy('order')->get();

        return inertia('Admin/GalleryImages/Index', [
            'gallery' => $gallery,
            'images'  => $images,
        ]);
    }

    /**
     * Yeni resim oluşturma formunu gösterir.
     */
    public function create($galleryId)
    {
        $gallery = Gallery::findOrFail($galleryId);
        return inertia('Admin/GalleryImages/Create', compact('gallery'));
    }

    /**
     * Yeni resim kaydını oluşturur.
     */
    public function store(Request $request, $galleryId)
    {
        $gallery = Gallery::findOrFail($galleryId);
    
        $validated = $request->validate([
            'image_path'      => 'required|image',  // dosya türü kontrolü için 'image'
            'thumbnail_path'  => 'nullable|image',
            'is_active'       => 'sometimes|boolean',
            'order'           => 'nullable|integer',
        ]);
    
        if ($request->hasFile('image_path')) {
            $image = $request->file('image_path');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            // Orijinal resmi "galleries" klasörüne yükle
            $imagePath = $image->storeAs('galleries', $imageName, 'public');
    
    
            // Prepend "storage/" to paths so that they can be accessed via asset() helper
            $validated['image_path'] = 'storage/' . $imagePath;
        }
    
        $validated['gallery_id'] = $gallery->id;
        GalleryImage::create($validated);
    
        return redirect()->route('admin.gallery_images.show', $gallery->id)
                         ->with('success', 'Resim başarıyla oluşturuldu.');
    }
    
    
    /**
     * Düzenleme formunu gösterir.
     */
    public function edit($galleryId, $id)
    {
        $gallery = Gallery::findOrFail($galleryId);
        $image = GalleryImage::findOrFail($id);

        return inertia('Admin/GalleryImages/Edit', [
            'gallery' => $gallery,
            'image'   => $image,
        ]);
    }

    /**
     * Resim kaydını günceller.
     */
    public function update(Request $request, $galleryId, $id)
    {
        $gallery = Gallery::findOrFail($galleryId);
        $image = GalleryImage::findOrFail($id);

        $validated = $request->validate([
            'image_path'      => 'required|image',
            'thumbnail_path'  => 'nullable|image',
            'is_active'       => 'sometimes|boolean',
            'order'           => 'nullable|integer',
        ]);

        if ($request->hasFile('image_path')) {
            $imageFile = $request->file('image_path');
            $originalPath = $imageFile->store('gallery_images', 'public');
            $validated['image_path'] = $originalPath;

        
        }

        $image->update($validated);

        return redirect()->route('admin.gallery_images.show', $gallery->id)
                         ->with('success', 'Resim başarıyla güncellendi.');
    }

    /**
     * Belirli bir resim kaydını siler.
     */
    public function destroy($galleryId, $id)
    {
        $gallery = Gallery::findOrFail($galleryId);
        $image = GalleryImage::findOrFail($id);
        $image->delete();

        return redirect()->route('admin.gallery_images.show', $gallery->id)
                         ->with('success', 'Resim başarıyla silindi.');
    }
}
