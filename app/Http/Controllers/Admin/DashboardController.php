<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use App\Models\Gallery;
use App\Models\Menu;
use App\Models\Page;
use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        $statistics = [
            'users_count' => User::count(),
            'pages_count' => Page::count(),
            'products_count' => Product::count(),
            'menus_count' => Menu::count(),
            'galleries_count' => Gallery::count(),
            'contacts_count' => Contact::count(),
            'recent_users' => User::latest()->take(5)->get(),
            'recent_pages' => Page::latest()->take(5)->get(),
            'recent_products' => Product::latest()->take(5)->get(),
            'recent_contacts' => Contact::latest()->take(5)->get(),
        ];

        return Inertia::render('Admin/Dashboard', [
            'statistics' => $statistics
        ]);
    }
}
