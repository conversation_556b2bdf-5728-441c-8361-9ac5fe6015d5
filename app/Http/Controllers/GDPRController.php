<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use ZipArchive;

class GDPRController extends Controller
{
    /**
     * Require authentication for all methods
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Show the GDPR data management page
     */
    public function index()
    {
        return inertia('GDPR/Index');
    }
    
    /**
     * Download user data
     */
    public function downloadData()
    {
        $user = Auth::user();
        
        // Collect user data
        $userData = [
            'user' => $user->toArray(),
            'activity_logs' => $user->activityLogs()->get()->toArray(),
            'date_exported' => now()->toDateTimeString(),
        ];
        
        // Generate JSON file
        $jsonData = json_encode($userData, JSON_PRETTY_PRINT);
        $tempFile = storage_path("app/tmp/user_{$user->id}_data.json");
        
        // Ensure tmp directory exists
        if (!file_exists(storage_path('app/tmp'))) {
            mkdir(storage_path('app/tmp'), 0755, true);
        }
        
        // Save JSON to file
        file_put_contents($tempFile, $jsonData);
        
        // Download file and then delete it
        return response()->download($tempFile)->deleteFileAfterSend(true);
    }
    
    /**
     * Initiate account deletion process
     */
    public function requestDeletion(Request $request)
    {
        $request->validate([
            'password' => 'required|current_password',
            'confirmation' => 'required|in:DELETE',
        ]);
        
        $user = Auth::user();
        
        // Mark user for deletion (soft delete)
        $user->deletion_requested_at = now();
        $user->save();
        
        // Schedule permanent deletion after 30 days
        // In a real application, you'd use a job scheduler for this
        
        // Log the user out
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect()->route('home')->with('success', 'Hesap silme talebiniz alınmıştır. Hesabınız 30 gün içinde kalıcı olarak silinecektir.');
    }
    
    /**
     * Permanently delete user data (admin function)
     */
    public function permanentlyDeleteUser(User $user)
    {
        // Only admins can force delete
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Bu işlem için yetkiniz yok.');
        }
        
        // Permanently delete user and related data
        $user->forceDelete();
        
        return redirect()->back()->with('success', 'Kullanıcı kalıcı olarak silindi.');
    }
    
    /**
     * Cancel scheduled deletion
     */
    public function cancelDeletion()
    {
        $user = Auth::user();
        
        // Unmark user for deletion
        $user->deletion_requested_at = null;
        $user->save();
        
        return redirect()->back()->with('success', 'Hesap silme talebiniz iptal edildi.');
    }
}
