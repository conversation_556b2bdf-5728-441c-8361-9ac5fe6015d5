<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CartController extends Controller
{
    /**
     * Display the cart page.
     */
    public function index()
    {
        return Inertia::render('Front/Cart/Index');
    }

    /**
     * Add item to cart (API endpoint).
     */
    public function addItem(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
        ]);

        $product = Product::findOrFail($request->product_id);

        // Return product data for frontend cart management
        return response()->json([
            'success' => true,
            'product' => [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'price' => $product->price,
                'sku' => $product->sku,
                'image' => $product->min_order_image_path,
            ],
            'quantity' => $request->quantity,
            'message' => 'Product added to cart successfully',
        ]);
    }

    /**
     * Remove item from cart (API endpoint).
     */
    public function removeItem(Request $request)
    {
        $request->validate([
            'product_id' => 'required|integer',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Product removed from cart successfully',
        ]);
    }

    /**
     * Update item quantity in cart (API endpoint).
     */
    public function updateQuantity(Request $request)
    {
        $request->validate([
            'product_id' => 'required|integer',
            'quantity' => 'required|integer|min:1',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Cart updated successfully',
        ]);
    }

    /**
     * Clear the entire cart (API endpoint).
     */
    public function clear()
    {
        return response()->json([
            'success' => true,
            'message' => 'Cart cleared successfully',
        ]);
    }
}
