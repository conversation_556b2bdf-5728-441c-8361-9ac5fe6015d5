<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class CheckoutController extends Controller
{
    /**
     * Display the checkout page.
     */
    public function index()
    {
        return Inertia::render('Front/Checkout/Index');
    }

    /**
     * Process the checkout and create order.
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'required|string|max:20',
            'billing_address' => 'required|array',
            'billing_address.street' => 'required|string|max:255',
            'billing_address.city' => 'required|string|max:100',
            'billing_address.postal_code' => 'required|string|max:20',
            'billing_address.country' => 'required|string|max:100',
            'shipping_address' => 'nullable|array',
            'shipping_address.street' => 'nullable|string|max:255',
            'shipping_address.city' => 'nullable|string|max:100',
            'shipping_address.postal_code' => 'nullable|string|max:20',
            'shipping_address.country' => 'nullable|string|max:100',
            'cart_items' => 'required|array|min:1',
            'cart_items.*.id' => 'required|exists:products,id',
            'cart_items.*.quantity' => 'required|integer|min:1',
            'cart_items.*.price' => 'required|numeric|min:0',
            'subtotal' => 'required|numeric|min:0',
            'tax_amount' => 'required|numeric|min:0',
            'total_amount' => 'required|numeric|min:0',
            'payment_method' => 'required|in:bank_transfer,paytr',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            // Create the order
            $order = Order::create([
                'order_number' => Order::generateOrderNumber(),
                'customer_name' => $request->customer_name,
                'customer_email' => $request->customer_email,
                'customer_phone' => $request->customer_phone,
                'billing_address' => $request->billing_address,
                'shipping_address' => $request->shipping_address ?? $request->billing_address,
                'subtotal' => $request->subtotal,
                'tax_amount' => $request->tax_amount,
                'total_amount' => $request->total_amount,
                'payment_method' => $request->payment_method,
                'payment_status' => 'pending',
                'status' => $request->payment_method === 'bank_transfer' ? 'pending' : 'payment_pending',
                'notes' => $request->notes,
            ]);

            // Create order items
            foreach ($request->cart_items as $item) {
                $product = Product::find($item['id']);

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_name' => $product->name['tr'] ?? $product->name['en'] ?? 'Unknown Product',
                    'product_sku' => $product->sku,
                    'unit_price' => $item['price'],
                    'quantity' => $item['quantity'],
                    'total_price' => $item['price'] * $item['quantity'],
                ]);
            }

            DB::commit();

            // Handle different payment methods
            if ($request->payment_method === 'paytr') {
                // For PayTR, return order ID for frontend to process payment
                return response()->json([
                    'success' => true,
                    'payment_method' => 'paytr',
                    'order_id' => $order->id,
                    'redirect_url' => route('checkout.payment', $order->id),
                ]);
            } else {
                // For bank transfer, redirect to confirmation page
                return redirect()->route('checkout.confirmation', $order->order_number)
                    ->with('success', 'Your order has been placed successfully!');
            }

        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors([
                'error' => 'There was an error processing your order. Please try again.'
            ])->withInput();
        }
    }

    /**
     * Display the order confirmation page.
     */
    public function confirmation($orderNumber)
    {
        $order = Order::where('order_number', $orderNumber)
            ->with('orderItems.product')
            ->firstOrFail();

        return Inertia::render('Front/Checkout/Confirmation', [
            'order' => $order,
            'bankDetails' => [
                'bank_name' => 'Türkiye İş Bankası',
                'account_name' => 'Pro Tazminat Ltd. Şti.',
                'account_number' => '**********',
                'iban' => 'TR12 0006 4000 0011 2345 6789 01',
                'swift' => 'ISBKTRIS',
            ],
        ]);
    }

    /**
     * Display the payment processing page for PayTR.
     */
    public function payment($orderId)
    {
        $order = Order::with('orderItems.product')->findOrFail($orderId);

        // Only allow payment processing for orders with PayTR payment method
        if ($order->payment_method !== 'paytr') {
            return redirect()->route('checkout.confirmation', $order->order_number);
        }

        return Inertia::render('Front/Checkout/Payment', [
            'order' => $order,
        ]);
    }
}
