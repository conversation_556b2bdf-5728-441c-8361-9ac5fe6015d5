<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LocaleController extends Controller
{
    /**
     * Set the application locale
     */
    public function setLocale(string $locale)
    {
        // Validate locale
        if (!in_array($locale, ['en', 'tr'])) {
            return response()->json(['error' => 'Invalid locale'], 400);
        }
        
        // Set locale in session
        Session::put('locale', $locale);
        
        // Set application locale
        App::setLocale($locale);
        
        // Create response with cookie
        $response = response()->json([
            'success' => true, 
            'locale' => $locale,
            'message' => 'Locale set successfully'
        ]);
        
        // Add cookie that lasts 1 year
        $response->cookie('locale', $locale, 60 * 24 * 365);
        
        return $response;
    }
}
