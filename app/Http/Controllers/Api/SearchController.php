<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Gallery;
use App\Models\Page;
use App\Models\Product;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    /**
     * Search across products, pages, and galleries
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');
        
        if (empty($query) || strlen($query) < 2) {
            return response()->json([
                'products' => [],
                'pages' => [],
                'galleries' => [],
            ]);
        }
        
        // Search in products
        $products = Product::where('is_active', true)
            ->where(function ($q) use ($query) {
                $q->whereRaw("JSON_EXTRACT(name, '$.tr') LIKE ?", ['%' . $query . '%'])
                  ->orWhereRaw("JSON_EXTRACT(name, '$.en') LIKE ?", ['%' . $query . '%'])
                  ->orWhereRaw("JSON_EXTRACT(description, '$.tr') LIKE ?", ['%' . $query . '%'])
                  ->orWhereRaw("JSON_EXTRACT(description, '$.en') LIKE ?", ['%' . $query . '%']);
            })
            ->with('category')
            ->limit(5)
            ->get();
        
        // Search in pages
        $pages = Page::where('is_active', true)
            ->where(function ($q) use ($query) {
                $q->whereRaw("JSON_EXTRACT(name, '$.tr') LIKE ?", ['%' . $query . '%'])
                  ->orWhereRaw("JSON_EXTRACT(name, '$.en') LIKE ?", ['%' . $query . '%'])
                  ->orWhereRaw("JSON_EXTRACT(content, '$.tr') LIKE ?", ['%' . $query . '%'])
                  ->orWhereRaw("JSON_EXTRACT(content, '$.en') LIKE ?", ['%' . $query . '%']);
            })
            ->limit(5)
            ->get();
        
        // Search in galleries
        $galleries = Gallery::where('is_active', true)
            ->where(function ($q) use ($query) {
                $q->whereRaw("JSON_EXTRACT(title, '$.tr') LIKE ?", ['%' . $query . '%'])
                  ->orWhereRaw("JSON_EXTRACT(title, '$.en') LIKE ?", ['%' . $query . '%'])
                  ->orWhereRaw("JSON_EXTRACT(description, '$.tr') LIKE ?", ['%' . $query . '%'])
                  ->orWhereRaw("JSON_EXTRACT(description, '$.en') LIKE ?", ['%' . $query . '%']);
            })
            ->limit(5)
            ->get();
        
        return response()->json([
            'products' => $products,
            'pages' => $pages,
            'galleries' => $galleries,
        ]);
    }
}
