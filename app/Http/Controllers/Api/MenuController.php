<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Menu;
use Illuminate\Http\Request;

class MenuController extends Controller
{
    /**
     * Get all active menus
     */
    public function index(Request $request)
    {
        $mainGrup = $request->session()->get('main_grup', 'proworker');

        $menus = Menu::with('children')
        ->where('main_grup', $mainGrup)
            ->where('is_active', true)
            ->orderBy('order')
            ->get();

        return response()->json($menus);
    }
}
