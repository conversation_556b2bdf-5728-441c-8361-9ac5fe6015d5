<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sipariş {{ $order->order_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }

        .company-info {
            flex: 1;
        }

        .company-logo {
            width: 60px;
            height: 60px;
            background-color: #2563eb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }

        .company-details {
            font-size: 11px;
            color: #666;
            line-height: 1.3;
        }

        .invoice-info {
            text-align: right;
            flex: 1;
        }

        .invoice-title {
            font-size: 20px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }

        .invoice-details {
            font-size: 11px;
        }

        .section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }

        .customer-info {
            display: flex;
            justify-content: space-between;
        }

        .address-block {
            flex: 1;
            margin-right: 20px;
        }

        .address-block:last-child {
            margin-right: 0;
        }

        .address-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #374151;
        }

        .address-content {
            font-size: 11px;
            line-height: 1.4;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .items-table th {
            background-color: #f3f4f6;
            border: 1px solid #d1d5db;
            padding: 10px 8px;
            text-align: left;
            font-weight: bold;
            font-size: 11px;
        }

        .items-table td {
            border: 1px solid #d1d5db;
            padding: 8px;
            font-size: 11px;
        }

        .items-table tr:nth-child(even) {
            background-color: #f9fafb;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .totals {
            float: right;
            width: 300px;
            margin-top: 20px;
        }

        .totals table {
            width: 100%;
            border-collapse: collapse;
        }

        .totals td {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
        }

        .totals .total-row {
            font-weight: bold;
            background-color: #f3f4f6;
            border-top: 2px solid #2563eb;
        }

        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 10px;
            color: #666;
            text-align: center;
            clear: both;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-pending { background-color: #fef3c7; color: #92400e; }
        .status-processing { background-color: #dbeafe; color: #1e40af; }
        .status-shipped { background-color: #e0e7ff; color: #5b21b6; }
        .status-delivered { background-color: #d1fae5; color: #065f46; }
        .status-completed { background-color: #d1fae5; color: #065f46; }
        .status-cancelled { background-color: #fee2e2; color: #991b1b; }
        .status-refunded { background-color: #f3f4f6; color: #374151; }

        .payment-info {
            background-color: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .payment-title {
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 8px;
        }

        .bank-details {
            font-size: 11px;
            line-height: 1.4;
        }

        .notes {
            background-color: #f9fafb;
            border-left: 4px solid #2563eb;
            padding: 15px;
            margin-top: 20px;
        }

        .notes-title {
            font-weight: bold;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-info">
                <div class="company-logo">PT</div>
                <div class="company-name">Pro Tazminat</div>
                <div class="company-details">
                    Pro Tazminat Ltd. Şti.<br>
                    Merkez Mah. Büyükdere Cad. No:123<br>
                    Şişli / İstanbul<br>
                    Telefon: +90 212 123 45 67<br>
                    E-posta: <EMAIL><br>
                    Vergi No: **********
                </div>
            </div>
            <div class="invoice-info">
                <div class="invoice-title">SİPARİŞ FORMU</div>
                <div class="invoice-details">
                    <strong>Sipariş No:</strong> {{ $order->order_number }}<br>
                    <strong>Tarih:</strong> {{ $order->created_at->format('d.m.Y H:i') }}<br>
                    <strong>Durum:</strong>
                    <span class="status-badge status-{{ $order->status }}">
                        {{ $statuses[$order->status] ?? $order->status }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="section">
            <div class="section-title">MÜŞTERİ BİLGİLERİ</div>
            <div class="customer-info">
                <div class="address-block">
                    <div class="address-title">Müşteri Detayları</div>
                    <div class="address-content">
                        <strong>{{ $order->customer_name }}</strong><br>
                        E-posta: {{ $order->customer_email }}<br>
                        Telefon: {{ $order->customer_phone }}
                    </div>
                </div>
                <div class="address-block">
                    <div class="address-title">Fatura Adresi</div>
                    <div class="address-content">
                        {{ $order->billing_address['street'] }}<br>
                        {{ $order->billing_address['city'] }}, {{ $order->billing_address['postal_code'] }}<br>
                        {{ $order->billing_address['country'] }}
                    </div>
                </div>
                @if($order->shipping_address && $order->shipping_address != $order->billing_address)
                <div class="address-block">
                    <div class="address-title">Teslimat Adresi</div>
                    <div class="address-content">
                        {{ $order->shipping_address['street'] }}<br>
                        {{ $order->shipping_address['city'] }}, {{ $order->shipping_address['postal_code'] }}<br>
                        {{ $order->shipping_address['country'] }}
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Order Items -->
        <div class="section">
            <div class="section-title">SİPARİŞ DETAYLARI</div>
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 5%">#</th>
                        <th style="width: 45%">Ürün Adı</th>
                        <th style="width: 15%">Ürün Kodu</th>
                        <th style="width: 10%" class="text-center">Adet</th>
                        <th style="width: 12%" class="text-right">Birim Fiyat</th>
                        <th style="width: 13%" class="text-right">Toplam</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($order->orderItems as $index => $item)
                    <tr>
                        <td class="text-center">{{ $index + 1 }}</td>
                        <td>{{ $item->product_name }}</td>
                        <td>{{ $item->product_sku ?? '-' }}</td>
                        <td class="text-center">{{ $item->quantity }}</td>
                        <td class="text-right">{{ number_format($item->unit_price, 2, ',', '.') }} ₺</td>
                        <td class="text-right">{{ number_format($item->total_price, 2, ',', '.') }} ₺</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Totals -->
        <div class="totals">
            <table>
                <tr>
                    <td>Ara Toplam:</td>
                    <td class="text-right">{{ number_format($order->subtotal, 2, ',', '.') }} ₺</td>
                </tr>
                <tr>
                    <td>KDV (%20):</td>
                    <td class="text-right">{{ number_format($order->tax_amount, 2, ',', '.') }} ₺</td>
                </tr>
                <tr class="total-row">
                    <td><strong>GENEL TOPLAM:</strong></td>
                    <td class="text-right"><strong>{{ number_format($order->total_amount, 2, ',', '.') }} ₺</strong></td>
                </tr>
            </table>
        </div>

        <!-- Payment Information -->
        @if($order->payment_method === 'bank_transfer')
        <div class="payment-info">
            <div class="payment-title">ÖDEME BİLGİLERİ - BANKA HAVALESİ</div>
            <div class="bank-details">
                <strong>Banka Adı:</strong> Türkiye İş Bankası A.Ş.<br>
                <strong>Şube:</strong> Şişli Şubesi<br>
                <strong>Hesap Sahibi:</strong> Pro Tazminat Ltd. Şti.<br>
                <strong>Hesap No:</strong> 1234-5678-901234<br>
                <strong>IBAN:</strong> TR12 0006 4000 0011 2345 6789 01<br>
                <strong>Swift Kodu:</strong> ISBKTRIS<br>
                <strong>Havale Açıklaması:</strong> Sipariş No: {{ $order->order_number }}<br>
                <strong>Ödenecek Tutar:</strong> {{ number_format($order->total_amount, 2, ',', '.') }} ₺
            </div>
        </div>
        @endif

        <!-- Notes -->
        @if($order->notes || $order->admin_notes)
        <div class="notes">
            @if($order->notes)
            <div class="notes-title">Müşteri Notu:</div>
            <div>{{ $order->notes }}</div>
            @endif

            @if($order->admin_notes)
            <div class="notes-title" style="margin-top: 10px;">Admin Notu:</div>
            <div>{{ $order->admin_notes }}</div>
            @endif
        </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <p>Bu belge {{ now()->format('d.m.Y H:i') }} tarihinde sistem tarafından otomatik olarak oluşturulmuştur.</p>
            <p>Pro Tazminat Ltd. Şti. - www.protazminat.com</p>
        </div>
    </div>
</body>
</html>
