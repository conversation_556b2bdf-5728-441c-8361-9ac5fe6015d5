@tailwind base;
@tailwind components;
@tailwind utilities;

/* Theme Colors - CSS Custom Properties */
:root {
    /* Default theme colors - will be overridden by .env values via JavaScript */
    --theme-primary: 119 176 170; /* <PERSON><PERSON><PERSON><PERSON>, linkler */
    --theme-secondary: 19 93 102; /* <PERSON><PERSON>, ikonlar */
    --theme-general: 0 60 67; /* <PERSON><PERSON>/<PERSON> - <PERSON>, <PERSON><PERSON><PERSON>, footer */
    --theme-accent: 227 254 247; /* Çok Açık Nane Yeşili - Ana arka plan */

    /* RGB versions for use with opacity */
    --theme-primary-rgb: 119 176 170;
    --theme-secondary-rgb: 19 93 102;
    --theme-general-rgb: 0 60 67;
    --theme-accent-rgb: 227 254 247;
}

/* Theme utility classes */
.bg-theme-primary {
    background-color: rgb(var(--theme-primary));
}

.bg-theme-secondary {
    background-color: rgb(var(--theme-secondary));
}

.bg-theme-accent {
    background-color: rgb(var(--theme-accent));
}

.bg-theme-general {
    background-color: rgb(var(--theme-general));
}

.text-theme-primary {
    color: rgb(var(--theme-primary));
}

.text-theme-secondary {
    color: rgb(var(--theme-secondary));
}

.text-theme-accent {
    color: rgb(var(--theme-accent));
}

.text-theme-general {
    color: rgb(var(--theme-general));
}

.border-theme-primary {
    border-color: rgb(var(--theme-primary));
}

.border-theme-secondary {
    border-color: rgb(var(--theme-secondary));
}

.border-theme-accent {
    border-color: rgb(var(--theme-accent));
}

.border-theme-general {
    border-color: rgb(var(--theme-general));
}

/* Hover states */
.hover\:bg-theme-primary:hover {
    background-color: rgb(var(--theme-primary) / 0.9);
}

.hover\:bg-theme-secondary:hover {
    background-color: rgb(var(--theme-secondary) / 0.9);
}

.hover\:bg-theme-general:hover {
    background-color: rgb(var(--theme-general) / 0.9);
}

.hover\:text-theme-primary:hover {
    color: rgb(var(--theme-primary));
}

.hover\:text-theme-secondary:hover {
    color: rgb(var(--theme-secondary));
}

.hover\:text-theme-general:hover {
    color: rgb(var(--theme-general));
}

.text-theme-primary {
    color: rgb(var(--theme-primary));
}

.text-theme-secondary {
    color: rgb(var(--theme-secondary));
}

.text-theme-accent {
    color: rgb(var(--theme-accent));
}

.border-theme-primary {
    border-color: rgb(var(--theme-primary));
}

.border-theme-secondary {
    border-color: rgb(var(--theme-secondary));
}

.border-theme-accent {
    border-color: rgb(var(--theme-accent));
}

/* Hover states */
.hover\:bg-theme-primary:hover {
    background-color: rgb(var(--theme-primary));
}

.hover\:bg-theme-secondary:hover {
    background-color: rgb(var(--theme-secondary));
}

.hover\:bg-theme-accent:hover {
    background-color: rgb(var(--theme-accent));
}

.hover\:text-theme-primary:hover {
    color: rgb(var(--theme-primary));
}

.hover\:text-theme-secondary:hover {
    color: rgb(var(--theme-secondary));
}

.hover\:text-theme-accent:hover {
    color: rgb(var(--theme-accent));
}

.hover\:border-theme-primary:hover {
    border-color: rgb(var(--theme-primary));
}

.hover\:border-theme-secondary:hover {
    border-color: rgb(var(--theme-secondary));
}

.hover\:border-theme-accent:hover {
    border-color: rgb(var(--theme-accent));
}

/* Focus states */
.focus\:ring-theme-primary:focus {
    --tw-ring-color: rgb(var(--theme-primary) / 0.5);
}

.focus\:ring-theme-secondary:focus {
    --tw-ring-color: rgb(var(--theme-secondary) / 0.5);
}

.focus\:ring-theme-accent:focus {
    --tw-ring-color: rgb(var(--theme-accent) / 0.5);
}
/* or in your `app.css` file */

body,
html {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 0 0% 9%;
        --primary-foreground: 0 0% 98%;
        --secondary: 0 0% 92.1%;
        --secondary-foreground: 0 0% 9%;
        --muted: 0 0% 96.1%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 92.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 0 0% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 0 0% 94%;
        --sidebar-accent-foreground: 0 0% 30%;
        --sidebar-border: 0 0% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
        /* .env'den gelen özel renkler - JavaScript ile set edilecek */
    }

    .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 6.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 84% 60%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
        --sidebar-background: 0 0% 7%;
        --sidebar-foreground: 0 0% 95.9%;
        --sidebar-primary: 360, 100%, 100%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 0 0% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 0 0% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
        /* .env'den gelen özel renkler (dark mode için) - JavaScript ile set edilecek */
    }
}
.myFont {
    font-family: 'Roboto', sans-serif;
    font-weight: bold;
    font-size: 1rem;
}
