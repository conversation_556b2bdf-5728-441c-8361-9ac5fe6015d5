/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON>. (<PERSON><PERSON><PERSON> - 1931)
 * Represents the average remaining life expectancy based on age in 1931.
 * Keys are ages (as numbers), values are average remaining years (as numbers).
 */
export type LifeExpectancyTable1931 = Record<number, number>;

/**
 * P.M.F. (<PERSON><PERSON><PERSON> - 1931) Data
 * Source: Provided table data.
 */
export const bakiyeOmur: LifeExpectancyTable1931 = {
    0: 56.64,
    1: 60.6,
    2: 60.58,
    3: 59.97,
    4: 59.22,
    5: 58.41,
    6: 57.57,
    7: 56.71,
    8: 55.83,
    9: 54.93,
    10: 54.03,
    11: 53.11,
    12: 52.19,
    13: 51.28,
    14: 50.37,
    15: 49.49,
    16: 48.62,
    17: 47.78,
    18: 46.96,
    19: 46.15,
    20: 45.9,
    21: 44.59,
    22: 43.83,
    23: 43.03,
    24: 42.27,
    25: 41.49,
    26: 40.7,
    27: 39.9,
    28: 39.1,
    29: 38.32,
    30: 37.5,
    31: 36.7,
    32: 35.9,
    33: 35.1,
    34: 34.29,
    35: 33.49,
    36: 32.69,
    37: 31.9,
    38: 31.1,
    39: 30.31,
    40: 29.73,
    41: 28.73,
    42: 27.95,
    43: 27.18,
    44: 26.4,
    45: 25.64,
    46: 24.78,
    47: 24.12,
    48: 23.36,
    49: 22.62,
    50: 21.88,
    51: 21.15,
    52: 20.42,
    53: 19.7,
    54: 18.98,
    55: 18.28,
    56: 17.82,
    57: 16.9,
    58: 16.1,
    59: 15.55,
    60: 14.89,
    61: 14.23,
    62: 13.59,
    63: 12.97,
    64: 12.35,
    65: 11.75,
    66: 11.17,
    67: 10.51,
    68: 10.05,
    69: 9.5,
    70: 8.98,
    71: 8.47,
    72: 7.98,
    73: 7.54,
    74: 7.08,
    75: 6.88,
    76: 6.25,
    77: 5.86,
    78: 5.5,
    79: 5.15,
    80: 4.85,
    81: 4.52,
    82: 4.22,
    83: 3.95,
    84: 3.71,
    90: 2.71,
    95: 2.4,
    100: 2.0,
    105: 1.0,
};
