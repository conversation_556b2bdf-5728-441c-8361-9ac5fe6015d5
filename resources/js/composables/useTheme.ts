import { usePage } from '@inertiajs/vue3';
import { computed } from 'vue';

export function useTheme() {
    const page = usePage();

    const themeColors = computed(() => {
        return (
            page.props.themeColors || {
                primary: '184 207 206',
                secondary: '160 200 120',
                general: '20 61 96',
                accent: '221 235 157',
            }
        );
    });

    // CSS custom properties'i set et
    const setCSSCustomProperties = () => {
        const root = document.documentElement;
        const colors = themeColors.value;

        root.style.setProperty('--theme-primary', colors.primary);
        root.style.setProperty('--theme-secondary', colors.secondary);
        root.style.setProperty('--theme-general', colors.general);
        root.style.setProperty('--theme-accent', colors.accent);

        // RGB versiyonları da set et
        root.style.setProperty('--theme-primary-rgb', colors.primary);
        root.style.setProperty('--theme-secondary-rgb', colors.secondary);
        root.style.setProperty('--theme-general-rgb', colors.general);
        root.style.setProperty('--theme-accent-rgb', colors.accent);
    };

    // Utility functions
    const getPrimaryColor = () => `rgb(${themeColors.value.primary})`;
    const getSecondaryColor = () => `rgb(${themeColors.value.secondary})`;
    const getAccentColor = () => `rgb(${themeColors.value.accent})`;

    const getPrimaryRGB = () => themeColors.value.primary;
    const getSecondaryRGB = () => themeColors.value.secondary;
    const getAccentRGB = () => themeColors.value.accent;

    // Inline style helpers
    const primaryStyle = computed(() => ({
        backgroundColor: getPrimaryColor(),
    }));

    const secondaryStyle = computed(() => ({
        backgroundColor: getSecondaryColor(),
    }));

    const accentStyle = computed(() => ({
        backgroundColor: getAccentColor(),
    }));

    const primaryTextStyle = computed(() => ({
        color: getPrimaryColor(),
    }));

    const secondaryTextStyle = computed(() => ({
        color: getSecondaryColor(),
    }));

    const accentTextStyle = computed(() => ({
        color: getAccentColor(),
    }));

    return {
        themeColors,
        setCSSCustomProperties,
        getPrimaryColor,
        getSecondaryColor,
        getAccentColor,
        getPrimaryRGB,
        getSecondaryRGB,
        getAccentRGB,
        primaryStyle,
        secondaryStyle,
        accentStyle,
        primaryTextStyle,
        secondaryTextStyle,
        accentTextStyle,
    };
}
