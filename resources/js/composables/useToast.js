import { ref } from 'vue';

// Create a reactive toast state
const toasts = ref([]);
let toastId = 0;

export function useToast() {
  /**
   * Show a toast notification
   * 
   * @param {string} message - The message to display
   * @param {string} type - The type of toast (success, error, info, warning)
   * @param {number} duration - How long to display the toast in milliseconds
   */
  const showToast = (message, type = 'info', duration = 3000) => {
    const id = ++toastId;
    
    // Add the toast to the array
    toasts.value.push({
      id,
      message,
      type,
      duration,
    });
    
    // Remove the toast after the duration
    setTimeout(() => {
      removeToast(id);
    }, duration);
  };
  
  /**
   * Remove a toast by ID
   * 
   * @param {number} id - The ID of the toast to remove
   */
  const removeToast = (id) => {
    const index = toasts.value.findIndex(toast => toast.id === id);
    if (index !== -1) {
      toasts.value.splice(index, 1);
    }
  };
  
  return {
    toasts,
    showToast,
    removeToast,
  };
}
