import { usePage } from '@inertiajs/vue3';
import { computed } from 'vue';

export function useTheme() {
    // Basit fallback renkler
    const defaultColors = {
        primary: '14 165 233',
        secondary: '79 70 229',
        accent: '249 115 22',
    };

    let themeColors;
    try {
        const page = usePage();
        themeColors = computed(() => page.props.themeColors || defaultColors);
    } catch (error) {
        // Eğer usePage çalışmazsa, varsayılan renkleri kullan
        themeColors = computed(() => defaultColors);
    }

    // Helper functions to generate different color variations
    const getRgbColor = (colorKey) => {
        return themeColors.value[colorKey] || themeColors.value.primary;
    };

    const getTailwindColor = (colorKey, opacity = 1) => {
        const rgb = getRgbColor(colorKey);
        return opacity === 1 ? `rgb(${rgb})` : `rgba(${rgb} / ${opacity})`;
    };

    const getCssVariable = (colorKey) => {
        return `rgb(${getRgbColor(colorKey)})`;
    };

    // Generate Tailwind classes dynamically
    const getBackgroundClass = (colorKey, shade = 500) => {
        const colorMap = {
            primary: 'bg-sky-' + shade,
            secondary: 'bg-indigo-' + shade,
            accent: 'bg-orange-' + shade,
        };
        return colorMap[colorKey] || colorMap.primary;
    };

    const getTextClass = (colorKey, shade = 500) => {
        const colorMap = {
            primary: 'text-sky-' + shade,
            secondary: 'text-indigo-' + shade,
            accent: 'text-orange-' + shade,
        };
        return colorMap[colorKey] || colorMap.primary;
    };

    const getBorderClass = (colorKey, shade = 500) => {
        const colorMap = {
            primary: 'border-sky-' + shade,
            secondary: 'border-indigo-' + shade,
            accent: 'border-orange-' + shade,
        };
        return colorMap[colorKey] || colorMap.primary;
    };

    const getHoverBackgroundClass = (colorKey, shade = 600) => {
        const colorMap = {
            primary: 'hover:bg-sky-' + shade,
            secondary: 'hover:bg-indigo-' + shade,
            accent: 'hover:bg-orange-' + shade,
        };
        return colorMap[colorKey] || colorMap.primary;
    };

    // Generate inline styles for custom colors
    const getInlineStyles = (colorKey, property = 'background-color', opacity = 1) => {
        const rgb = getRgbColor(colorKey);
        const color = opacity === 1 ? `rgb(${rgb})` : `rgba(${rgb} / ${opacity})`;

        return {
            [property]: color,
        };
    };

    // Generate gradient styles
    const getGradientStyle = (fromColor, toColor, direction = 'to right') => {
        const fromRgb = getRgbColor(fromColor);
        const toRgb = getRgbColor(toColor);

        return {
            background: `linear-gradient(${direction}, rgb(${fromRgb}), rgb(${toRgb}))`,
        };
    };

    return {
        themeColors,
        getRgbColor,
        getTailwindColor,
        getCssVariable,
        getBackgroundClass,
        getTextClass,
        getBorderClass,
        getHoverBackgroundClass,
        getInlineStyles,
        getGradientStyle,
    };
}
