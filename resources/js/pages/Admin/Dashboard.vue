<script setup>
import AdminLayout from '@/layouts/AdminLayout.vue';
import { useLocaleStore } from '@/Stores/locale';
import { Head, Link } from '@inertiajs/vue3';
import { computed } from 'vue';

const props = defineProps({
    statistics: Object,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
</script>

<template>
    <Head :title="locale === 'tr' ? 'Yönetim Paneli' : 'Admin Dashboard'" />

    <AdminLayout>
        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                            <!-- Products Card -->
                            <div class="overflow-hidden rounded-lg bg-indigo-50 shadow">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 rounded-md bg-indigo-500 p-3">
                                            <svg
                                                class="h-6 w-6 text-white"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10"
                                                />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dt class="truncate text-sm font-medium text-gray-500">
                                                {{ locale === 'tr' ? 'Ürünler' : 'Products' }}
                                            </dt>
                                            <dd class="flex items-baseline">
                                                <div class="text-2xl font-semibold text-gray-900">
                                                    {{ statistics.products_count }}
                                                </div>
                                            </dd>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-gray-50 px-5 py-3">
                                    <div class="text-sm">
                                        <Link :href="route('admin.products.index')" class="font-medium text-indigo-700 hover:text-indigo-900">
                                            {{ locale === 'tr' ? 'Tümünü Görüntüle' : 'View all' }}
                                        </Link>
                                    </div>
                                </div>
                            </div>

                            <!-- Pages Card -->
                            <div class="overflow-hidden rounded-lg bg-green-50 shadow">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 rounded-md bg-green-500 p-3">
                                            <svg
                                                class="h-6 w-6 text-white"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                                />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dt class="truncate text-sm font-medium text-gray-500">
                                                {{ locale === 'tr' ? 'Sayfalar' : 'Pages' }}
                                            </dt>
                                            <dd class="flex items-baseline">
                                                <div class="text-2xl font-semibold text-gray-900">
                                                    {{ statistics.pages_count }}
                                                </div>
                                            </dd>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-gray-50 px-5 py-3">
                                    <div class="text-sm">
                                        <Link :href="route('admin.pages.index')" class="font-medium text-green-700 hover:text-green-900">
                                            {{ locale === 'tr' ? 'Tümünü Görüntüle' : 'View all' }}
                                        </Link>
                                    </div>
                                </div>
                            </div>

                            <!-- Users Card -->
                            <div class="overflow-hidden rounded-lg bg-blue-50 shadow">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 rounded-md bg-blue-500 p-3">
                                            <svg
                                                class="h-6 w-6 text-white"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                                                />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dt class="truncate text-sm font-medium text-gray-500">
                                                {{ locale === 'tr' ? 'Kullanıcılar' : 'Users' }}
                                            </dt>
                                            <dd class="flex items-baseline">
                                                <div class="text-2xl font-semibold text-gray-900">
                                                    {{ statistics.users_count }}
                                                </div>
                                            </dd>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-gray-50 px-5 py-3">
                                    <div class="text-sm">
                                        <Link :href="route('admin.users.index')" class="font-medium text-blue-700 hover:text-blue-900">
                                            {{ locale === 'tr' ? 'Tümünü Görüntüle' : 'View all' }}
                                        </Link>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Messages Card -->
                            <div class="overflow-hidden rounded-lg bg-yellow-50 shadow">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 rounded-md bg-yellow-500 p-3">
                                            <svg
                                                class="h-6 w-6 text-white"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                                />
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dt class="truncate text-sm font-medium text-gray-500">
                                                {{ locale === 'tr' ? 'İletişim Mesajları' : 'Contact Messages' }}
                                            </dt>
                                            <dd class="flex items-baseline">
                                                <div class="text-2xl font-semibold text-gray-900">
                                                    {{ statistics.contacts_count }}
                                                </div>
                                            </dd>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-gray-50 px-5 py-3">
                                    <div class="text-sm">
                                        <Link :href="route('admin.contacts.index')" class="font-medium text-yellow-700 hover:text-yellow-900">
                                            {{ locale === 'tr' ? 'Tümünü Görüntüle' : 'View all' }}
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity Section -->
                        <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
                            <!-- Recent Users -->
                            <div class="overflow-hidden rounded-lg bg-white shadow">
                                <div class="px-4 py-5 sm:px-6">
                                    <h3 class="text-lg font-medium leading-6 text-gray-900">
                                        {{ locale === 'tr' ? 'Son Eklenen Kullanıcılar' : 'Recent Users' }}
                                    </h3>
                                </div>
                                <div class="border-t border-gray-200">
                                    <ul class="divide-y divide-gray-200">
                                        <li v-for="user in statistics.recent_users" :key="user.id" class="px-4 py-4 sm:px-6">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <div class="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-gray-200">
                                                        <span class="font-medium text-gray-500">{{ user.name.charAt(0) }}</span>
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                                                        <div class="text-sm text-gray-500">{{ user.email }}</div>
                                                    </div>
                                                </div>
                                                <div>
                                                    <Link :href="route('admin.users.show', user.id)" class="text-indigo-600 hover:text-indigo-900">
                                                        {{ locale === 'tr' ? 'Görüntüle' : 'View' }}
                                                    </Link>
                                                </div>
                                            </div>
                                        </li>
                                        <li v-if="statistics.recent_users.length === 0" class="px-4 py-4 text-center text-gray-500 sm:px-6">
                                            {{ locale === 'tr' ? 'Henüz kullanıcı bulunmamaktadır.' : 'No users found yet.' }}
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Recent Pages -->
                            <div class="overflow-hidden rounded-lg bg-white shadow">
                                <div class="px-4 py-5 sm:px-6">
                                    <h3 class="text-lg font-medium leading-6 text-gray-900">
                                        {{ locale === 'tr' ? 'Son Eklenen Sayfalar' : 'Recent Pages' }}
                                    </h3>
                                </div>
                                <div class="border-t border-gray-200">
                                    <ul class="divide-y divide-gray-200">
                                        <li v-for="page in statistics.recent_pages" :key="page.id" class="px-4 py-4 sm:px-6">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">{{ page.title }}</div>
                                                    <div class="text-sm text-gray-500">
                                                        {{
                                                            locale === 'tr'
                                                                ? page.is_active
                                                                    ? 'Aktif'
                                                                    : 'Pasif'
                                                                : page.is_active
                                                                  ? 'Active'
                                                                  : 'Inactive'
                                                        }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <Link :href="route('admin.pages.show', page.id)" class="text-indigo-600 hover:text-indigo-900">
                                                        {{ locale === 'tr' ? 'Görüntüle' : 'View' }}
                                                    </Link>
                                                </div>
                                            </div>
                                        </li>
                                        <li v-if="statistics.recent_pages.length === 0" class="px-4 py-4 text-center text-gray-500 sm:px-6">
                                            {{ locale === 'tr' ? 'Henüz sayfa bulunmamaktadır.' : 'No pages found yet.' }}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Access Section -->
                        <div class="mt-8">
                            <h3 class="text-lg font-medium text-gray-900">
                                {{ locale === 'tr' ? 'Hızlı Erişim' : 'Quick Access' }}
                            </h3>
                            <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
                                <Link
                                    :href="route('admin.pages.create')"
                                    class="relative flex items-center space-x-3 rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 hover:border-gray-400"
                                >
                                    <div class="flex-shrink-0">
                                        <svg
                                            class="h-6 w-6 text-indigo-600"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <span class="absolute inset-0" aria-hidden="true"></span>
                                        <p class="text-sm font-medium text-gray-900">
                                            {{ locale === 'tr' ? 'Yeni Sayfa Ekle' : 'Add New Page' }}
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            {{ locale === 'tr' ? 'Web sitesine yeni bir sayfa ekleyin.' : 'Add a new page to your website.' }}
                                        </p>
                                    </div>
                                </Link>

                                <Link
                                    :href="route('admin.products.create')"
                                    class="relative flex items-center space-x-3 rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 hover:border-gray-400"
                                >
                                    <div class="flex-shrink-0">
                                        <svg
                                            class="h-6 w-6 text-green-600"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <span class="absolute inset-0" aria-hidden="true"></span>
                                        <p class="text-sm font-medium text-gray-900">
                                            {{ locale === 'tr' ? 'Yeni Ürün Ekle' : 'Add New Product' }}
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            {{ locale === 'tr' ? 'Ürün kataloğunuza yeni bir ürün ekleyin.' : 'Add a new product to your catalog.' }}
                                        </p>
                                    </div>
                                </Link>

                                <Link
                                    :href="route('admin.users.create')"
                                    class="relative flex items-center space-x-3 rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 hover:border-gray-400"
                                >
                                    <div class="flex-shrink-0">
                                        <svg
                                            class="h-6 w-6 text-blue-600"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <span class="absolute inset-0" aria-hidden="true"></span>
                                        <p class="text-sm font-medium text-gray-900">
                                            {{ locale === 'tr' ? 'Yeni Kullanıcı Ekle' : 'Add New User' }}
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            {{ locale === 'tr' ? 'Sisteme yeni bir kullanıcı ekleyin.' : 'Add a new user to the system.' }}
                                        </p>
                                    </div>
                                </Link>

                                <Link
                                    :href="route('admin.contacts.index')"
                                    class="relative flex items-center space-x-3 rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 hover:border-gray-400"
                                >
                                    <div class="flex-shrink-0">
                                        <svg
                                            class="h-6 w-6 text-red-600"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                            />
                                        </svg>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <span class="absolute inset-0" aria-hidden="true"></span>
                                        <p class="text-sm font-medium text-gray-900">
                                            {{ locale === 'tr' ? 'İletişim Mesajlarını Görüntüle' : 'View Contact Messages' }}
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            {{
                                                locale === 'tr'
                                                    ? 'Gelen iletişim formlarını görüntüleyin.'
                                                    : 'View incoming contact form submissions.'
                                            }}
                                        </p>
                                    </div>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
