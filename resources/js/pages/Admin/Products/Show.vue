<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { computed } from 'vue';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    product: Object,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const confirmDelete = (id) => {
    if (confirm(locale.value === 'tr' ? 'Bu <PERSON>r<PERSON><PERSON><PERSON> silmek istediğinizden emin misiniz?' : 'Are you sure you want to delete this product?')) {
        window.location.href = route(`admin.users.destroy`, id);
    }
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'Ürün Detayı' : 'Product Details'" />

    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ locale === 'tr' ? '<PERSON><PERSON><PERSON><PERSON>' : 'Product Details' }}: {{ product.name[locale] }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex justify-end mb-6">
                            <Link 
                                :href="route('admin.products.edit', product.id)" 
                                class="inline-flex items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-500 active:bg-yellow-700 focus:outline-none focus:border-yellow-700 focus:ring focus:ring-yellow-300 disabled:opacity-25  mr-2"
                            >
                                {{ locale === 'tr' ? 'Düzenle' : 'Edit' }}
                            </Link>
                            <button 
                                @click="confirmDelete(product.id)" 
                                class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:border-red-700 focus:ring focus:ring-red-300 disabled:opacity-25 "
                            >
                                {{ locale === 'tr' ? 'Sil' : 'Delete' }}
                            </button>
                            <form 
                                :id="`delete-form-${product.id}`" 
                                :action="route('admin.products.destroy', product.id)" 
                                method="POST" 
                                class="hidden"
                            >
                                <input type="hidden" name="_method" value="DELETE">
                                <input type="hidden" name="_token" :value="$page.props.csrf_token">
                            </form>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">
                                    {{ locale === 'tr' ? 'Temel Bilgiler' : 'Basic Information' }}
                                </h3>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        ID
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ product.id }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Ürün Adı (TR)' : 'Product Name (TR)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ product.name.tr }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Ürün Adı (EN)' : 'Product Name (EN)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ product.name.en }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Fiyat' : 'Price' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ product.price }} TL
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        SKU
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ product.sku || '-' }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Stok' : 'Stock' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ product.stock || 0 }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Durum' : 'Status' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        <span 
                                            :class="[
                                                'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                                product.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                            ]"
                                        >
                                            {{ product.is_active ? (locale === 'tr' ? 'Aktif' : 'Active') : (locale === 'tr' ? 'Pasif' : 'Inactive') }}
                                        </span>
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Sıra' : 'Order' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ product.order || 0 }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Ana Grup' : 'Main Group' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ product.main_grup }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        Slug
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ product.slug }}
                                    </dd>
                                </div>
                            </div>

                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">
                                    {{ locale === 'tr' ? 'Açıklama ve Özellikler' : 'Description and Features' }}
                                </h3>
                                <div class="bg-gray-50 px-4 py-5 rounded-lg mb-4">
                                    <dt class="text-sm font-medium text-gray-500 mb-2">
                                        {{ locale === 'tr' ? 'Açıklama (TR)' : 'Description (TR)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        {{ product.description.tr }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 rounded-lg mb-4">
                                    <dt class="text-sm font-medium text-gray-500 mb-2">
                                        {{ locale === 'tr' ? 'Açıklama (EN)' : 'Description (EN)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        {{ product.description.en }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 rounded-lg mb-4">
                                    <dt class="text-sm font-medium text-gray-500 mb-2">
                                        {{ locale === 'tr' ? 'Özellikler (TR)' : 'Features (TR)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        {{ product.features?.tr || '-' }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 rounded-lg mb-4">
                                    <dt class="text-sm font-medium text-gray-500 mb-2">
                                        {{ locale === 'tr' ? 'Özellikler (EN)' : 'Features (EN)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        {{ product.features?.en || '-' }}
                                    </dd>
                                </div>

                                <h3 class="text-lg font-medium text-gray-900 mb-4 mt-8">
                                    {{ locale === 'tr' ? 'SEO Bilgileri' : 'SEO Information' }}
                                </h3>
                                <div class="bg-gray-50 px-4 py-5 rounded-lg mb-4">
                                    <dt class="text-sm font-medium text-gray-500 mb-2">
                                        {{ locale === 'tr' ? 'Meta Açıklama (TR)' : 'Meta Description (TR)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        {{ product.meta_description?.tr || '-' }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 rounded-lg mb-4">
                                    <dt class="text-sm font-medium text-gray-500 mb-2">
                                        {{ locale === 'tr' ? 'Meta Açıklama (EN)' : 'Meta Description (EN)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        {{ product.meta_description?.en || '-' }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 rounded-lg mb-4">
                                    <dt class="text-sm font-medium text-gray-500 mb-2">
                                        {{ locale === 'tr' ? 'Meta Anahtar Kelimeleri (TR)' : 'Meta Keywords (TR)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        {{ product.meta_keywords?.tr || '-' }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 rounded-lg mb-4">
                                    <dt class="text-sm font-medium text-gray-500 mb-2">
                                        {{ locale === 'tr' ? 'Meta Anahtar Kelimeleri (EN)' : 'Meta Keywords (EN)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        {{ product.meta_keywords?.en || '-' }}
                                    </dd>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
