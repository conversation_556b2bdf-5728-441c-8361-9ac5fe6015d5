<script setup>
import { Head, useForm } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { computed, ref } from 'vue';
import { useLocaleStore } from '@/Stores/locale';
import QuillEditor from '@/components/QuillEditor.vue';

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const props = defineProps({
  categories: Array
});

const form = useForm({
  name: { tr: '', en: '' },
  description: { tr: '', en: '' },
  features: { tr: '', en: '' },
  price: '',
  sku: '',
  stock: 0,
  is_active: true,
  order: 0,
  category_id: '',
  meta_description: { tr: '', en: '' },
  meta_keywords: { tr: '', en: '' },
  main_grup: 'products'
});

const submit = () => {
  form.post(route('admin.products.store'));
};

const activeTab = ref('tr');

const editorOptions = {
  theme: 'snow',
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ header: 1 }, { header: 2 }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ direction: 'rtl' }],
      [{ size: ['small', false, 'large', 'huge'] }],
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ color: [] }, { background: [] }],
      [{ font: [] }],
      [{ align: [] }],
      ['clean'],
      ['link', 'image', 'video']
    ]
  }
};
</script>

<template>
  <Head :title="locale === 'tr' ? 'Ürün Oluştur' : 'Create Product'" />

  <AdminLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        {{ locale === 'tr' ? 'Ürün Oluştur' : 'Create Product' }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 bg-white border-b border-gray-200">
            <form @submit.prevent="submit">
              <!-- Kayıt Butonu En Üstte -->
              <div class="flex justify-end mb-4">
                <button
                  type="submit"
                  class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 "
                  :disabled="form.processing"
                >
                  {{ locale === 'tr' ? 'Kaydet' : 'Save' }}
                </button>
              </div>

              <!-- Dil Tabları -->
              <div class="mb-4 border-b border-gray-200">
                <nav class="-mb-px flex">
                  <button
                    type="button"
                    @click="activeTab = 'tr'"
                    :class="activeTab === 'tr' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="w-1/2 py-4 px-1 text-center border-b-2 font-medium text-sm"
                  >
                    Türkçe
                  </button>
                  <button
                    type="button"
                    @click="activeTab = 'en'"
                    :class="activeTab === 'en' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="w-1/2 py-4 px-1 text-center border-b-2 font-medium text-sm"
                  >
                    English
                  </button>

                
                </nav>
              </div>

              <!-- Dil Alanları (Tab İçeriği) -->
              <div v-if="activeTab === 'tr'">
                <!-- Türkçe Ürün Adı -->
                <div class="mb-4">
                  <label for="name_tr" class="block text-sm font-medium text-gray-700">
                    Ürün Adı (Türkçe)
                  </label>
                  <input
                    type="text"
                    id="name_tr"
                    v-model="form.name.tr"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  />
                  <div v-if="form.errors['name.tr']" class="text-red-500 text-sm mt-1">
                    {{ form.errors['name.tr'] }}
                  </div>
                </div>

                <!-- Türkçe Açıklama -->
                <div class="mb-4">
                  <label for="description_tr" class="block text-sm font-medium text-gray-700">
                    Açıklama (Türkçe)
                  </label>
                  <QuillEditor
                    id="description_tr"
                    v-model="form.description.tr"
                    :options="editorOptions"
                    theme="snow"
                    class="mt-1 block w-full"
                    style="min-height: 150px;"
                  />
                  <div v-if="form.errors['description.tr']" class="text-red-500 text-sm mt-1">
                    {{ form.errors['description.tr'] }}
                  </div>
                </div>

                <!-- Türkçe Özellikler -->
                <div class="mb-4">
                  <label for="features_tr" class="block text-sm font-medium text-gray-700">
                    Özellikler (Türkçe)
                  </label>
                  <QuillEditor
                    id="features_tr"
                    v-model="form.features.tr"
                    :options="editorOptions"
                    theme="snow"
                    class="mt-1 block w-full"
                    style="min-height: 150px;"
                  />
                  <div v-if="form.errors['features.tr']" class="text-red-500 text-sm mt-1">
                    {{ form.errors['features.tr'] }}
                  </div>
                </div>

                <!-- Türkçe Meta Açıklama -->
                <div class="mb-4">
                  <label for="meta_description_tr" class="block text-sm font-medium text-gray-700">
                    Meta Açıklama (Türkçe)
                  </label>
                  <textarea
                    id="meta_description_tr"
                    v-model="form.meta_description.tr"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="2"
                  ></textarea>
                  <div v-if="form.errors['meta_description.tr']" class="text-red-500 text-sm mt-1">
                    {{ form.errors['meta_description.tr'] }}
                  </div>
                </div>

                <!-- Türkçe Meta Anahtar Kelimeler -->
                <div class="mb-4">
                  <label for="meta_keywords_tr" class="block text-sm font-medium text-gray-700">
                    Meta Anahtar Kelimeleri (Türkçe)
                  </label>
                  <textarea
                    id="meta_keywords_tr"
                    v-model="form.meta_keywords.tr"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="2"
                  ></textarea>
                  <div v-if="form.errors['meta_keywords.tr']" class="text-red-500 text-sm mt-1">
                    {{ form.errors['meta_keywords.tr'] }}
                  </div>
                </div>
              </div>

              <div v-else-if="activeTab === 'en'">
                <!-- English Product Name -->
                <div class="mb-4">
                  <label for="name_en" class="block text-sm font-medium text-gray-700">
                    Product Name (English)
                  </label>
                  <input
                    type="text"
                    id="name_en"
                    v-model="form.name.en"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  />
                  <div v-if="form.errors['name.en']" class="text-red-500 text-sm mt-1">
                    {{ form.errors['name.en'] }}
                  </div>
                </div>

                <!-- English Description -->
                <div class="mb-4">
                  <label for="description_en" class="block text-sm font-medium text-gray-700">
                    Description (English)
                  </label>
                  <QuillEditor
                    id="description_en"
                    v-model="form.description.en"
                    :options="editorOptions"
                    theme="snow"
                    class="mt-1 block w-full"
                    style="min-height: 150px;"
                  />
                  <div v-if="form.errors['description.en']" class="text-red-500 text-sm mt-1">
                    {{ form.errors['description.en'] }}
                  </div>
                </div>

                <!-- English Features -->
                <div class="mb-4">
                  <label for="features_en" class="block text-sm font-medium text-gray-700">
                    Features (English)
                  </label>
                  <QuillEditor
                    id="features_en"
                    v-model="form.features.en"
                    :options="editorOptions"
                    theme="snow"
                    class="mt-1 block w-full"
                    style="min-height: 150px;"
                  />
                  <div v-if="form.errors['features.en']" class="text-red-500 text-sm mt-1">
                    {{ form.errors['features.en'] }}
                  </div>
                </div>

                <!-- English Meta Description -->
                <div class="mb-4">
                  <label for="meta_description_en" class="block text-sm font-medium text-gray-700">
                    Meta Description (English)
                  </label>
                  <textarea
                    id="meta_description_en"
                    v-model="form.meta_description.en"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="2"
                  ></textarea>
                  <div v-if="form.errors['meta_description.en']" class="text-red-500 text-sm mt-1">
                    {{ form.errors['meta_description.en'] }}
                  </div>
                </div>

                <!-- English Meta Keywords -->
                <div class="mb-4">
                  <label for="meta_keywords_en" class="block text-sm font-medium text-gray-700">
                    Meta Keywords (English)
                  </label>
                  <textarea
                    id="meta_keywords_en"
                    v-model="form.meta_keywords.en"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="2"
                  ></textarea>
                  <div v-if="form.errors['meta_keywords.en']" class="text-red-500 text-sm mt-1">
                    {{ form.errors['meta_keywords.en'] }}
                  </div>
                </div>
              </div>

              <!-- Dil Bağımsız Alanlar -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <!-- Price -->
                <div>
                  <label for="price" class="block text-sm font-medium text-gray-700">
                    {{ locale === 'tr' ? 'Fiyat' : 'Price' }}
                  </label>
                  <input 
                    type="number" 
                    id="price" 
                    v-model="form.price" 
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    step="0.01"
                    required
                  />
                  <div v-if="form.errors.price" class="text-red-500 text-sm mt-1">
                    {{ form.errors.price }}
                  </div>
                </div>

                <!-- SKU -->
                <div>
                  <label for="sku" class="block text-sm font-medium text-gray-700">
                    SKU
                  </label>
                  <input 
                    type="text" 
                    id="sku" 
                    v-model="form.sku" 
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  />
                  <div v-if="form.errors.sku" class="text-red-500 text-sm mt-1">
                    {{ form.errors.sku }}
                  </div>
                </div>

                <!-- Stock -->
                <div>
                  <label for="stock" class="block text-sm font-medium text-gray-700">
                    {{ locale === 'tr' ? 'Stok' : 'Stock' }}
                  </label>
                  <input 
                    type="number" 
                    id="stock" 
                    v-model="form.stock" 
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  />
                  <div v-if="form.errors.stock" class="text-red-500 text-sm mt-1">
                    {{ form.errors.stock }}
                  </div>
                </div>

                <!-- Order -->
                <div>
                  <label for="order" class="block text-sm font-medium text-gray-700">
                    {{ locale === 'tr' ? 'Sıra' : 'Order' }}
                  </label>
                  <input 
                    type="number" 
                    id="order" 
                    v-model="form.order" 
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  />
                  <div v-if="form.errors.order" class="text-red-500 text-sm mt-1">
                    {{ form.errors.order }}
                  </div>
                </div>

                <!-- Is Active -->
                <div>
                  <label for="is_active" class="block text-sm font-medium text-gray-700">
                    {{ locale === 'tr' ? 'Durum' : 'Status' }}
                  </label>
                  <select 
                    id="is_active" 
                    v-model="form.is_active" 
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  >
                    <option :value="true">{{ locale === 'tr' ? 'Aktif' : 'Active' }}</option>
                    <option :value="false">{{ locale === 'tr' ? 'Pasif' : 'Inactive' }}</option>
                  </select>
                  <div v-if="form.errors.is_active" class="text-red-500 text-sm mt-1">
                    {{ form.errors.is_active }}
                  </div>
                </div>

                <!-- Category -->
                <div>
                  <label for="category_id" class="block text-sm font-medium text-gray-700">
                    {{ locale === 'tr' ? 'Kategori' : 'Category' }}
                  </label>
                  <select 
                    id="category_id" 
                    v-model="form.category_id" 
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  >
                    <option value="">{{ locale === 'tr' ? 'Seçiniz' : 'Select' }}</option>
                    <option v-for="category in categories" :key="category.id" :value="category.id">
                      {{ locale === 'tr' ? category.name.tr : category.name.en }}
                    </option>
                  </select>
                  <div v-if="form.errors.category_id" class="text-red-500 text-sm mt-1">
                    {{ form.errors.category_id }}
                  </div>
                </div>

                <!-- Main Group -->
                <div>
                  <label for="main_grup" class="block text-sm font-medium text-gray-700">
                    {{ locale === 'tr' ? 'Ana Grup' : 'Main Group' }}
                  </label>
                  <input 
                    type="text" 
                    id="main_grup" 
                    v-model="form.main_grup" 
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  />
                  <div v-if="form.errors.main_grup" class="text-red-500 text-sm mt-1">
                    {{ form.errors.main_grup }}
                  </div>
                </div>
              </div>

            </form>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>
