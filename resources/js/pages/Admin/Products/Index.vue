<script setup lang="ts">
import AdminLayout from '@/layouts/AdminLayout.vue';
import { useLocaleStore } from '@/Stores/locale';
import { Inertia } from '@inertiajs/inertia';
import { Head, Link } from '@inertiajs/vue3';
import { computed, ref } from 'vue';

const props = defineProps({
    products: {
        type: Object,
        required: true,
        default: () => ({ data: [], links: [] }), // Boş bir varsayılan değ<PERSON> ekledik
    },
});
const confirmDelete = (id) => {
    if (confirm(locale.value === 'tr' ? 'Bu sayfayı silmek istediğinizden emin misiniz?' : 'Are you sure you want to delete this page?')) {
        Inertia.delete(route('admin.products.destroy', id));
    }
};

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
const search = ref('');

// Filtrelenmiş ürünler (arama için)
const filteredProducts = computed(() => {
    console.log(props.products);
    if (!props.products || !props.products.data) return [];

    if (!search.value) return props.products.data;

    return props.products.data.filter((product) => product.name?.[locale.value]?.toLowerCase()?.includes(search.value.toLowerCase()) || (product.sku && product.sku.toLowerCase().includes(search.value.toLowerCase())));
});

// Sayfa değiştirme fonksiyonu
const changePage = (url) => {
    if (url) {
        window.location.href = url;
    }
};

// Pagination label'ını Türkçeleştir
const turkishLabel = (label) => {
    // HTML entity'leri temizle
    const cleanLabel = label.replace(/&[^;]+;/g, '').trim();

    if (cleanLabel.includes('Previous') || label.includes('&laquo;') || label.includes('«')) {
        return 'Önceki';
    } else if (cleanLabel.includes('Next') || label.includes('&raquo;') || label.includes('»')) {
        return 'Sonraki';
    } else {
        // HTML entity'leri decode et
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = label;
        return tempDiv.textContent || tempDiv.innerText || label;
    }
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'Ürünler' : 'Products'" />

    <AdminLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">
                {{ locale === 'tr' ? 'Ürünler' : 'Products' }}
            </h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="border-b border-gray-200 bg-white p-6">
                        <div class="mb-6 flex items-center justify-between">
                            <div class="flex-1">
                                <input
                                    type="text"
                                    v-model="search"
                                    :placeholder="locale === 'tr' ? 'Ürünlerde ara...' : 'Search products...'"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:w-1/3"
                                />
                            </div>
                            <Link
                                :href="route('admin.products.create')"
                                class="inline-flex items-center rounded-md border border-transparent bg-gray-800 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-gray-700 focus:border-gray-900 focus:outline-none focus:ring focus:ring-gray-300 active:bg-gray-900 disabled:opacity-25"
                            >
                                {{ locale === 'tr' ? 'Ürün Oluştur' : 'Create Product' }}
                            </Link>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">ID</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">{{ locale === 'tr' ? 'Ürün Adı' : 'Product Name' }}</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">{{ locale === 'tr' ? 'Kategori' : 'Category' }}</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">{{ locale === 'tr' ? 'Fiyat' : 'Price' }}</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">SKU</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">{{ locale === 'tr' ? 'Durum' : 'Status' }}</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">{{ locale === 'tr' ? 'Sıra' : 'Order' }}</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">{{ locale === 'tr' ? 'İşlemler' : 'Actions' }}</th>
                                    </tr>
                                </thead>
                                <tbody v-if="filteredProducts && filteredProducts.length" class="divide-y divide-gray-200 bg-white">
                                    <tr v-for="product in filteredProducts" :key="product.id">
                                        <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{{ product.id }}</td>
                                        <td class="whitespace-nowrap px-6 py-4">
                                            <div class="text-sm font-medium text-gray-900">{{ product.name[locale] }}</div>
                                            <div class="text-sm text-gray-500">{{ product.slug }}</div>
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                                            {{ product.category ? product.category.name[locale] : locale === 'tr' ? 'Kategori Yok' : 'No Category' }}
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{{ product.price }} TL</td>
                                        <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{{ product.sku || '-' }}</td>
                                        <td class="whitespace-nowrap px-6 py-4">
                                            <span :class="['inline-flex rounded-full px-2 text-xs font-semibold leading-5', product.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800']">
                                                {{ product.is_active ? (locale === 'tr' ? 'Aktif' : 'Active') : locale === 'tr' ? 'Pasif' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{{ product.order }}</td>
                                        <td class="whitespace-nowrap px-6 py-4 text-sm font-medium">
                                            <Link :href="route('admin.products.show', product.id)" class="mr-4 text-blue-600 hover:text-blue-900">👁</Link>
                                            <Link :href="route('admin.products.edit', product.id)" class="mr-4 text-indigo-600 hover:text-indigo-900">✏</Link>
                                            <a href="#" @click.prevent="confirmDelete(product.id)" class="text-red-600 hover:text-red-900">🗑</a>
                                        </td>
                                    </tr>
                                    <tr v-if="filteredProducts.length === 0">
                                        <td colspan="8" class="whitespace-nowrap px-6 py-4 text-center text-sm text-gray-500">
                                            {{ locale === 'tr' ? 'Ürün bulunamadı' : 'No products found' }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Sayfalama -->
                        <div class="mt-6 flex justify-center space-x-2">
                            <button
                                v-for="link in props.products.links"
                                :key="link.label"
                                :disabled="!link.url"
                                @click="changePage(link.url)"
                                class="rounded-md border px-4 py-2"
                                :class="{ 'bg-gray-800 text-white': link.active, 'bg-gray-200 text-gray-700': !link.active }"
                            >
                                {{ turkishLabel(link.label) }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
