<script setup>
import AdminLayout from '@/layouts/AdminLayout.vue';
import { useLocaleStore } from '@/Stores/locale';
import { Head, useForm } from '@inertiajs/vue3';
import { QuillEditor } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import axios from 'axios';
import { computed, onMounted, ref } from 'vue';

// Props: düzenlenecek ürün ve kategori listesi
const props = defineProps({
    product: Object,
    categories: Array,
});

// Locale bilgisini store üzerinden alıyoruz
const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

// Eğer gelen translatable alan nesne de<PERSON>e, düz string ise normalize ediyoruz
function normalizeTranslatable(field) {
    if (field && typeof field === 'object' && field.tr !== undefined && field.en !== undefined) {
        return { tr: String(field.tr), en: String(field.en) };
    }
    return { tr: field ? String(field) : '', en: field ? String(field) : '' };
}

// Formu, gelen ürün verilerine göre oluşturuyoruz ve translatable alanları normalize ediyoruz
const form = useForm({
    name: normalizeTranslatable(props.product.name) ?? { tr: '', en: '' },
    description: normalizeTranslatable(props.product.description) ?? { tr: '', en: '' },
    features: normalizeTranslatable(props.product.features) ?? { tr: '', en: '' },
    price: props.product.price,
    sku: props.product.sku || '',
    stock: props.product.stock || 0,
    is_active: props.product.is_active,
    order: props.product.order || 0,
    meta_description: normalizeTranslatable(props.product.meta_description) ?? { tr: '', en: '' },
    meta_keywords: normalizeTranslatable(props.product.meta_keywords) ?? { tr: '', en: '' },
    main_grup: props.product.main_grup,
    category_id: props.product.category_id,
});

const submit = () => {
    form.put(route('admin.products.update', props.product.id));
};

const editorOptions = {
    theme: 'snow',
    modules: {
        toolbar: [
            ['bold', 'italic', 'underline', 'strike'],
            ['blockquote', 'code-block'],
            [{ header: 1 }, { header: 2 }],
            [{ list: 'ordered' }, { list: 'bullet' }],
            [{ script: 'sub' }, { script: 'super' }],
            [{ indent: '-1' }, { indent: '+1' }],
            [{ direction: 'rtl' }],
            [{ size: ['small', false, 'large', 'huge'] }],
            [{ header: [1, 2, 3, 4, 5, 6, false] }],
            [{ color: [] }, { background: [] }],
            [{ font: [] }],
            [{ align: [] }],
            ['clean'],
            ['link', 'image', 'video'],
        ],
    },
};

// activeTab artık en üstte tanımlandı
const activeTab = ref('tr');

// Resimlerle ilgili veriler
const resimler = ref([]);
const isLoading = ref(false);
const error = ref(null);

const yuklenecekResim = ref(null);
const yuklemeDurumu = ref(false);
const yuklemeHatasi = ref(null);

// Resim seçildiğinde çalışacak fonksiyon
const resimSecildi = (event) => {
    yuklenecekResim.value = event.target.files[0];
};

// Sayfa yüklendiğinde resimleri getir
onMounted(() => {
    fetchResimler();
});

// Resimleri getiren fonksiyon
async function fetchResimler() {
    isLoading.value = true;
    error.value = null;

    try {
        // Backend'de product id ile resimleri getireceğiniz endpoint'i kullanın.
        const response = await axios.get(route('admin.product.getProductImages', props.product.id));
        resimler.value = response.data;
        console.log(resimler.value);
    } catch (err) {
        error.value = err.message || 'Bilinmeyen bir hata oluştu.';
        console.error(err);
    } finally {
        isLoading.value = false;
    }
}

const resmiYukle = async () => {
    if (!yuklenecekResim.value) {
        alert('Lütfen bir resim seçin.');
        return;
    }

    console.log('Starting image upload...', {
        file: yuklenecekResim.value,
        product_id: props.product.id,
    });

    yuklemeDurumu.value = true;
    yuklemeHatasi.value = null;

    try {
        const formData = new FormData();
        formData.append('image', yuklenecekResim.value);
        formData.append('product_id', props.product.id);

        console.log('FormData created:', {
            has_image: formData.has('image'),
            has_product_id: formData.has('product_id'),
            product_id: formData.get('product_id'),
        });

        const response = await axios.post(route('admin.products.imageUpload'), formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });

        console.log('Upload response:', response.data);

        // Yükleme başarılıysa, resim listesini yeniden yükle
        fetchResimler();
        alert('Resim başarıyla yüklendi!');
        yuklenecekResim.value = null;
        // File input'u temizle
        const fileInput = document.getElementById('image');
        if (fileInput) fileInput.value = '';
    } catch (err) {
        console.error('Upload error:', err);
        if (err.response) {
            // Server responded with error status
            const errorMessage = err.response.data?.message || err.response.data?.error || 'Sunucu hatası';
            yuklemeHatasi.value = `Hata ${err.response.status}: ${errorMessage}`;

            // Validation errors
            if (err.response.data?.errors) {
                const validationErrors = Object.values(err.response.data.errors).flat().join(', ');
                yuklemeHatasi.value += ` - ${validationErrors}`;
            }
        } else if (err.request) {
            // Request was made but no response received
            yuklemeHatasi.value = 'Sunucuya ulaşılamıyor. İnternet bağlantınızı kontrol edin.';
        } else {
            // Something else happened
            yuklemeHatasi.value = err.message || 'Resim yüklenirken bir hata oluştu.';
        }
        alert(yuklemeHatasi.value);
    } finally {
        yuklemeDurumu.value = false;
    }
};

const resimUrls = computed(() => {
    return resimler.value.map((resim) => {
        return {
            ...resim,
            imageUrl: '/' + resim.image_path,
        };
    });
});

// Resim silme fonksiyonu
async function resimSil(resimId) {
    isLoading.value = true;
    error.value = null;

    try {
        await axios.delete(route('admin.products.deleteProductImage', resimId));
        fetchResimler();
        alert('Resim başarıyla silindi!');
    } catch (err) {
        error.value = err.message || 'Resim silinirken bir hata oluştu.';
        console.error(err);
    } finally {
        isLoading.value = false;
    }
}

// Her resim için sıralama girişlerini tutacak reaktif nesneler
const orderInputs = ref({});
const orderErrors = ref({});
const isSending = ref(false);
const apiResponses = ref({});
const apiErrors = ref({});

const validateOrderInput = (imageId) => {
    const value = orderInputs.value[imageId];
    if (!Number.isInteger(value)) {
        orderErrors.value[imageId] = 'Lütfen sadece tam sayı girin.';
    } else {
        orderErrors.value[imageId] = '';
    }
};

const saveOrder = async (imageId) => {
    const orderValue = orderInputs.value[imageId];
    if (orderErrors.value[imageId] || !Number.isInteger(orderValue)) {
        alert('Lütfen geçerli bir tam sayı girin.');
        return;
    }

    isSending.value = true;
    apiResponses.value[imageId] = '';
    apiErrors.value[imageId] = '';

    try {
        const response = await axios.post(route('admin.products.imageOrderUpadte'), {
            order: orderValue,
            image_id: imageId,
        });
        apiResponses.value[imageId] = response.data.message || 'Sıra başarıyla güncellendi!';
        alert(apiResponses.value[imageId]);
    } catch (error) {
        apiErrors.value[imageId] = error.message || 'Sıra güncellenirken bir hata oluştu.';
        alert(apiErrors.value[imageId]);
        console.error(error);
    } finally {
        isSending.value = false;
    }
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'Ürün Düzenle' : 'Edit Product'" />

    <AdminLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">{{ locale === 'tr' ? 'Ürün Düzenle' : 'Edit Product' }}: {{ form.name[locale] }}</h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="border-b border-gray-200 bg-white p-6">
                        <form @submit.prevent="submit">
                            <div class="mt-6 flex justify-end">
                                <button
                                    type="submit"
                                    class="inline-flex items-center rounded-md border border-transparent bg-gray-800 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-gray-700 focus:border-gray-900 focus:outline-none focus:ring focus:ring-gray-300 active:bg-gray-900 disabled:opacity-25"
                                    :disabled="form.processing"
                                >
                                    {{ locale === 'tr' ? 'Güncelle' : 'Update' }}
                                </button>
                            </div>

                            <!-- Dil Alanları için Tablar -->
                            <div class="mb-4 border-b border-gray-200">
                                <nav class="-mb-px flex">
                                    <button
                                        type="button"
                                        @click="activeTab = 'tr'"
                                        :class="activeTab === 'tr' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                                        class="w-1/2 border-b-2 px-1 py-4 text-center text-sm font-medium"
                                    >
                                        Türkçe
                                    </button>
                                    <button
                                        type="button"
                                        @click="activeTab = 'en'"
                                        :class="activeTab === 'en' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                                        class="w-1/2 border-b-2 px-1 py-4 text-center text-sm font-medium"
                                    >
                                        English
                                    </button>
                                    <button
                                        type="button"
                                        @click="activeTab = 'resimler'"
                                        :class="activeTab === 'resimler' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                                        class="w-1/2 border-b-2 px-1 py-4 text-center text-sm font-medium"
                                    >
                                        Ürün Resimleri
                                    </button>
                                </nav>
                            </div>

                            <!-- Translatable Alanlar: Aktif sekmeye göre -->
                            <div v-if="activeTab === 'tr'">
                                <!-- Türkçe Ürün Adı -->
                                <div class="mb-4">
                                    <label for="name_tr" class="block text-sm font-medium text-gray-700"> Ürün Adı (Türkçe) </label>
                                    <input type="text" id="name_tr" v-model="form.name.tr" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required />
                                    <div v-if="form.errors['name.tr']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['name.tr'] }}
                                    </div>
                                </div>

                                <!-- Türkçe Açıklama -->
                                <div class="mb-4">
                                    <label for="description_tr" class="block text-sm font-medium text-gray-700"> Açıklama (Türkçe) </label>
                                    <QuillEditor id="description_tr" v-model:content="form.description.tr" contentType="html" :options="editorOptions" theme="snow" class="mt-1 block w-full" style="min-height: 150px" />
                                    <div v-if="form.errors['description.tr']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['description.tr'] }}
                                    </div>
                                </div>

                                <!-- Türkçe Özellikler -->
                                <div class="mb-4">
                                    <label for="features_tr" class="block text-sm font-medium text-gray-700"> Özellikler (Türkçe) </label>
                                    <QuillEditor id="features_tr" v-model:content="form.features.tr" contentType="html" :options="editorOptions" theme="snow" class="mt-1 block w-full" style="min-height: 150px" />
                                    <div v-if="form.errors['features.tr']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['features.tr'] }}
                                    </div>
                                </div>

                                <!-- Türkçe Meta Alanları -->
                                <div class="mb-4">
                                    <label for="meta_description_tr" class="block text-sm font-medium text-gray-700"> Meta Açıklama (Türkçe) </label>
                                    <textarea id="meta_description_tr" v-model="form.meta_description.tr" rows="2" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                                    <div v-if="form.errors['meta_description.tr']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['meta_description.tr'] }}
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="meta_keywords_tr" class="block text-sm font-medium text-gray-700"> Meta Anahtar Kelimeleri (Türkçe) </label>
                                    <textarea id="meta_keywords_tr" v-model="form.meta_keywords.tr" rows="2" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                                    <div v-if="form.errors['meta_keywords.tr']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['meta_keywords.tr'] }}
                                    </div>
                                </div>
                            </div>

                            <div v-if="activeTab === 'en'">
                                <!-- English Product Name -->
                                <div class="mb-4">
                                    <label for="name_en" class="block text-sm font-medium text-gray-700"> Product Name (English) </label>
                                    <input type="text" id="name_en" v-model="form.name.en" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required />
                                    <div v-if="form.errors['name.en']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['name.en'] }}
                                    </div>
                                </div>

                                <!-- English Description -->
                                <div class="mb-4">
                                    <label for="description_en" class="block text-sm font-medium text-gray-700"> Description (English) </label>
                                    <QuillEditor id="description_en" v-model:content="form.description.en" contentType="html" :options="editorOptions" theme="snow" class="mt-1 block w-full" style="min-height: 150px" />
                                    <div v-if="form.errors['description.en']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['description.en'] }}
                                    </div>
                                </div>

                                <!-- English Features -->
                                <div class="mb-4">
                                    <label for="features_en" class="block text-sm font-medium text-gray-700"> Features (English) </label>
                                    <QuillEditor id="features_en" v-model:content="form.features.en" contentType="html" :options="editorOptions" theme="snow" class="mt-1 block w-full" style="min-height: 150px" />
                                    <div v-if="form.errors['features.en']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['features.en'] }}
                                    </div>
                                </div>

                                <!-- English Meta Alanları -->
                                <div class="mb-4">
                                    <label for="meta_description_en" class="block text-sm font-medium text-gray-700"> Meta Description (English) </label>
                                    <textarea id="meta_description_en" v-model="form.meta_description.en" rows="2" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                                    <div v-if="form.errors['meta_description.en']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['meta_description.en'] }}
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="meta_keywords_en" class="block text-sm font-medium text-gray-700"> Meta Keywords (English) </label>
                                    <textarea id="meta_keywords_en" v-model="form.meta_keywords.en" rows="2" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                                    <div v-if="form.errors['meta_keywords.en']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['meta_keywords.en'] }}
                                    </div>
                                </div>
                            </div>

                            <div v-if="activeTab === 'en' || activeTab === 'tr'">
                                <!-- Dil Bağımsız Alanlar (Fiyat, SKU, Stok, Sıra, Durum, Kategori, Ana Grup) -->
                                <div class="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2">
                                    <!-- Price -->
                                    <div>
                                        <label for="price" class="block text-sm font-medium text-gray-700">
                                            {{ locale === 'tr' ? 'Fiyat' : 'Price' }}
                                        </label>
                                        <input type="number" id="price" v-model="form.price" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" step="0.01" required />
                                        <div v-if="form.errors.price" class="mt-1 text-sm text-red-500">
                                            {{ form.errors.price }}
                                        </div>
                                    </div>

                                    <!-- SKU -->
                                    <div>
                                        <label for="sku" class="block text-sm font-medium text-gray-700"> SKU </label>
                                        <input type="text" id="sku" v-model="form.sku" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                                        <div v-if="form.errors.sku" class="mt-1 text-sm text-red-500">
                                            {{ form.errors.sku }}
                                        </div>
                                    </div>

                                    <!-- Stock -->
                                    <div>
                                        <label for="stock" class="block text-sm font-medium text-gray-700">
                                            {{ locale === 'tr' ? 'Stok' : 'Stock' }}
                                        </label>
                                        <input type="number" id="stock" v-model="form.stock" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                                        <div v-if="form.errors.stock" class="mt-1 text-sm text-red-500">
                                            {{ form.errors.stock }}
                                        </div>
                                    </div>

                                    <!-- Order -->
                                    <div>
                                        <label for="order" class="block text-sm font-medium text-gray-700">
                                            {{ locale === 'tr' ? 'Sıra' : 'Order' }}
                                        </label>
                                        <input type="number" id="order" v-model="form.order" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                                        <div v-if="form.errors.order" class="mt-1 text-sm text-red-500">
                                            {{ form.errors.order }}
                                        </div>
                                    </div>

                                    <!-- Is Active -->
                                    <div>
                                        <label for="is_active" class="block text-sm font-medium text-gray-700">
                                            {{ locale === 'tr' ? 'Durum' : 'Status' }}
                                        </label>
                                        <select id="is_active" v-model="form.is_active" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <option :value="true">{{ locale === 'tr' ? 'Aktif' : 'Active' }}</option>
                                            <option :value="false">{{ locale === 'tr' ? 'Pasif' : 'Inactive' }}</option>
                                        </select>
                                        <div v-if="form.errors.is_active" class="mt-1 text-sm text-red-500">
                                            {{ form.errors.is_active }}
                                        </div>
                                    </div>

                                    <!-- Category -->
                                    <div>
                                        <label for="category_id" class="block text-sm font-medium text-gray-700">
                                            {{ locale === 'tr' ? 'Kategori' : 'Category' }}
                                        </label>
                                        <select id="category_id" v-model="form.category_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <option value="">{{ locale === 'tr' ? 'Seçiniz' : 'Select' }}</option>
                                            <option v-for="category in categories" :key="category.id" :value="category.id">
                                                {{ locale === 'tr' ? category.name.tr : category.name.en }}
                                            </option>
                                        </select>
                                        <div v-if="form.errors.category_id" class="mt-1 text-sm text-red-500">
                                            {{ form.errors.category_id }}
                                        </div>
                                    </div>

                                    <!-- Main Group -->
                                    <div>
                                        <label for="main_grup" class="block text-sm font-medium text-gray-700">
                                            {{ locale === 'tr' ? 'Ana Grup' : 'Main Group' }}
                                        </label>
                                        <input type="text" id="main_grup" v-model="form.main_grup" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required />
                                        <div v-if="form.errors.main_grup" class="mt-1 text-sm text-red-500">
                                            {{ form.errors.main_grup }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <div v-if="activeTab === 'resimler'">
                            <div class="mb-4">
                                <label for="image" class="block text-sm font-medium text-gray-700"> Resim Yükle </label>
                                <input type="file" id="image" @change="resimSecildi" accept="image/*" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                                <p class="mt-1 text-sm text-gray-500">Maksimum dosya boyutu: 10MB. Desteklenen formatlar: JPG, PNG, GIF, WebP</p>
                            </div>

                            <!-- Yükleme Butonu -->
                            <button
                                type="button"
                                @click="resmiYukle"
                                :disabled="yuklemeDurumu"
                                class="inline-flex items-center rounded-md border border-transparent bg-green-600 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-green-500 focus:border-green-700 focus:outline-none focus:ring focus:ring-green-300 active:bg-green-700 disabled:opacity-25"
                            >
                                {{ yuklemeDurumu ? 'Yükleniyor...' : 'Resmi Yükle' }}
                            </button>

                            <!-- Hata Mesajı -->
                            <div v-if="yuklemeHatasi" class="mt-4 rounded border border-red-400 bg-red-100 p-4 text-red-700">
                                {{ yuklemeHatasi }}
                            </div>

                            <div v-if="isLoading" class="py-4 text-center text-gray-600">Resimler yükleniyor...</div>
                            <div v-else-if="error" class="py-4 text-center text-red-500">Resimleri yüklerken bir hata oluştu: {{ error }}</div>
                            <div v-else-if="resimler && resimler.length > 0" class="m-5 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                                <div v-for="(resim, index) in resimUrls" :key="index" class="relative">
                                    <img :src="resim.imageUrl" alt="Resim" class="h-auto w-full rounded-lg object-cover shadow-md" />
                                    <button @click="resimSil(resim.id)" class="absolute right-2 top-2 rounded bg-red-500 px-2 py-1 font-bold text-white hover:bg-red-700">Sil</button>
                                    <br />
                                    <!-- Her resim için sıralama inputu -->
                                    <input type="number" v-model.number="orderInputs[resim.id]" @input="validateOrderInput(resim.id)" :placeholder="'Sıra: ' + resim.order" />
                                    <div v-if="orderErrors[resim.id]" class="text-red-500">
                                        {{ orderErrors[resim.id] }}
                                    </div>
                                    <button @click="saveOrder(resim.id)" :disabled="isSending" class="mt-2 w-full rounded bg-slate-600 p-1 text-white shadow-sm">
                                        {{ isSending ? 'Gönderiliyor...' : 'Güncelle' }}
                                    </button>
                                </div>
                            </div>
                            <div v-else class="py-4 text-center italic text-gray-500">Gösterilecek resim bulunmuyor.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
