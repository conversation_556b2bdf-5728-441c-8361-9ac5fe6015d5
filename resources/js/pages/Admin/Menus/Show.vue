<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { computed } from 'vue';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    menu: Object,
    parentMenu: Object,
    childMenus: Array,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const confirmDelete = (id) => {
    if (confirm(locale.value === 'tr' ? 'Bu men<PERSON>yü silmek istediğinizden emin misiniz?' : 'Are you sure you want to delete this menu?')) {
     //  window.location.href = route(`admin.menus.destroy`, id);
       Inertia.delete(route('admin.menus.destroy', id));

    }
};
</script>

<template>
    <Head :title="locale === 'tr' ? '<PERSON><PERSON> Detayı' : '<PERSON><PERSON> Details'" />

    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ locale === 'tr' ? '<PERSON><PERSON>' : '<PERSON>u Details' }}: {{ menu.name[locale] }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex justify-end mb-6">
                            <Link 
                                :href="route('admin.menus.edit', menu.id)" 
                                class="inline-flex items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-500 active:bg-yellow-700 focus:outline-none focus:border-yellow-700 focus:ring focus:ring-yellow-300 disabled:opacity-25  mr-2"
                            >
                                {{ locale === 'tr' ? 'Düzenle' : 'Edit' }}
                            </Link>
                            <button 
                                @click="confirmDelete(menu.id)" 
                                class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:border-red-700 focus:ring focus:ring-red-300 disabled:opacity-25 "
                            >
                                {{ locale === 'tr' ? 'Sil' : 'Delete' }}
                            </button>
                            <form 
                                :id="`delete-form-${menu.id}`" 
                                :action="route('admin.menus.destroy', menu.id)" 
                                method="POST" 
                                class="hidden"
                            >
                                <input type="hidden" name="_method" value="DELETE">
                                <input type="hidden" name="_token" :value="$page.props.csrf_token">
                            </form>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">
                                    {{ locale === 'tr' ? 'Temel Bilgiler' : 'Basic Information' }}
                                </h3>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        ID
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ menu.id }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Menü Adı (TR)' : 'Menu Name (TR)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ menu.name.tr }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Menü Adı (EN)' : 'Menu Name (EN)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ menu.name.en }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        URL
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ menu.url || '-' }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Üst Menü' : 'Parent Menu' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        <span v-if="parentMenu">
                                            <Link :href="route('admin.menus.show', parentMenu.id)" class="text-indigo-600 hover:text-indigo-900">
                                                {{ parentMenu.name[locale] }}
                                            </Link>
                                        </span>
                                        <span v-else>
                                            {{ locale === 'tr' ? 'Ana Menü' : 'Main Menu' }}
                                        </span>
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Durum' : 'Status' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        <span 
                                            :class="[
                                                'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                                menu.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                            ]"
                                        >
                                            {{ menu.is_active ? (locale === 'tr' ? 'Aktif' : 'Active') : (locale === 'tr' ? 'Pasif' : 'Inactive') }}
                                        </span>
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Sıra' : 'Order' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ menu.order || 0 }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Ana Grup' : 'Main Group' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ menu.main_grup }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        Slug
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ menu.slug }}
                                    </dd>
                                </div>
                            </div>

                            <div v-if="childMenus && childMenus.length > 0">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">
                                    {{ locale === 'tr' ? 'Alt Menüler' : 'Child Menus' }}
                                </h3>
                                <div class="overflow-x-auto bg-white shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    ID
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {{ locale === 'tr' ? 'Menü Adı' : 'Menu Name' }}
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {{ locale === 'tr' ? 'Durum' : 'Status' }}
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {{ locale === 'tr' ? 'İşlemler' : 'Actions' }}
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr v-for="childMenu in childMenus" :key="childMenu.id">
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ childMenu.id }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-gray-900">
                                                        {{ childMenu.name[locale] }}
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span 
                                                        :class="[
                                                            'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                                            childMenu.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                                        ]"
                                                    >
                                                        {{ childMenu.is_active ? (locale === 'tr' ? 'Aktif' : 'Active') : (locale === 'tr' ? 'Pasif' : 'Inactive') }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <Link 
                                                        :href="route('admin.menus.show', childMenu.id)" 
                                                        class="text-indigo-600 hover:text-indigo-900 mr-2"
                                                    >
                                                        {{ locale === 'tr' ? 'Görüntüle' : 'View' }}
                                                    </Link>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
