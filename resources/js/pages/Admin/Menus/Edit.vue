<script setup>
import { Head, useForm } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { computed } from 'vue';
import { useLocaleStore } from '@/Stores/locale';
import { FwbToggle } from 'flowbite-vue'
const props = defineProps({
    menu: Object,
    parentMenus: Array,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const form = useForm({
    name: props.menu.name,
    url: props.menu.url || '',
    parent_id: props.menu.parent_id,
    is_active: props.menu.is_active,
    order: props.menu.order || 0,
    main_grup: props.menu.main_grup,
    liste: props.menu.liste || false
});

const submit = () => {
    form.put(route('admin.menus.update', props.menu.id));
};
</script>

<template>
    <Head :title="locale === 'tr' ? '<PERSON><PERSON>' : 'Edit Menu'" />

    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ locale === 'tr' ? 'Menü Düzenle' : 'Edit Menu' }}: {{ menu.name[locale] }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <form @submit.prevent="submit">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Turkish Name -->
                                <div>
                                    <label for="name_tr" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Menü Adı (Türkçe)' : 'Menu Name (Turkish)' }}
                                    </label>
                                    <input 
                                        type="text" 
                                        id="name_tr" 
                                        v-model="form.name.tr" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                        required
                                    />
                                    <div v-if="form.errors['name.tr']" class="text-red-500 text-sm mt-1">
                                        {{ form.errors['name.tr'] }}
                                    </div>
                                </div>

                                <!-- English Name -->
                                <div>
                                    <label for="name_en" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Menü Adı (İngilizce)' : 'Menu Name (English)' }}
                                    </label>
                                    <input 
                                        type="text" 
                                        id="name_en" 
                                        v-model="form.name.en" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                        required
                                    />
                                    <div v-if="form.errors['name.en']" class="text-red-500 text-sm mt-1">
                                        {{ form.errors['name.en'] }}
                                    </div>
                                </div>

                                <!-- URL -->
                                <div>
                                    <label for="url" class="block text-sm font-medium text-gray-700">
                                        URL
                                    </label>
                                    <input 
                                        type="text" 
                                        id="url" 
                                        v-model="form.url" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                    />
                                    <div v-if="form.errors.url" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.url }}
                                    </div>
                                </div>

                                <!-- Parent Menu -->
                                <div>
                                    <label for="parent_id" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Üst Menü' : 'Parent Menu' }}
                                    </label>
                                    <select 
                                        id="parent_id" 
                                        v-model="form.parent_id" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                    >
                                        <option :value="null">{{ locale === 'tr' ? 'Ana Menü' : 'Main Menu' }}</option>
                                        <option v-for="parentMenu in parentMenus" :key="parentMenu.id" :value="parentMenu.id">
                                            {{ parentMenu.name[locale] }}
                                        </option>
                                    </select>
                                    <div v-if="form.errors.parent_id" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.parent_id }}
                                    </div>
                                </div>

                                <!-- Order -->
                                <div>
                                    <label for="order" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Sıra' : 'Order' }}
                                    </label>
                                    <input 
                                        type="number" 
                                        id="order" 
                                        v-model="form.order" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                    />
                                    <div v-if="form.errors.order" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.order }}
                                    </div>
                                </div>

                                <!-- Is Active -->
                                <div>
                                    <div class="flex items-center mt-4">
                                      
                                        <fwb-toggle 
    v-model="form.is_active"
    :label="locale === 'tr' ? 'Aktif' : 'Active'"
/>

                                    </div>
                                    <div v-if="form.errors.is_active" class="text-red-500 mt-2 text-sm">
                                        {{ form.errors.is_active }}
                                    </div>
                                </div>

                                <!-- Liste -->
                                <div>
                                    <div class="flex items-center mt-4">
                                        <input 
                                            id="liste" 
                                            type="checkbox" 
                                            v-model="form.liste" 
                                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                        >
                                        <label for="liste" class="ml-2 block text-sm text-gray-900">
                                            {{ locale === 'tr' ? 'Listede Göster' : 'Show in List' }}
                                        </label>
                                    </div>
                                    <div v-if="form.errors.liste" class="text-red-500 mt-2 text-sm">
                                        {{ form.errors.liste }}
                                    </div>
                                </div>

                                <!-- Main Group -->
                                <div>
                                    <label for="main_grup" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Ana Grup' : 'Main Group' }}
                                    </label>
                                    <input 
                                        type="text" 
                                        id="main_grup" 
                                        v-model="form.main_grup" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                        required
                                    />
                                    <div v-if="form.errors.main_grup" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.main_grup }}
                                    </div>
                                </div>
                            </div>

                            <div class="mt-6 flex justify-end">
                                <button 
                                    type="submit" 
                                    class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 "
                                    :disabled="form.processing"
                                >
                                    {{ locale === 'tr' ? 'Güncelle' : 'Update' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
