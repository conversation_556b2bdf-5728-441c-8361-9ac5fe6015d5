<script setup>
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { ref, computed } from 'vue';
import { EyeIcon, PencilSquareIcon, TrashIcon, CloudArrowDownIcon, ArrowUpIcon, ArrowDownIcon } from '@heroicons/vue/24/outline';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
  menus: Array,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
const search = ref('');
const sortColumn = ref('order');
const sortDirection = ref('asc');

const sortBy = (column) => {
  if (sortColumn.value === column) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortColumn.value = column;
    sortDirection.value = 'asc';
  }
};

const sortIcon = (column) => {
  if (sortColumn.value !== column) return null;
  return sortDirection.value === 'asc' ? ArrowUpIcon : ArrowDownIcon;
};

const filteredMenus = computed(() => {
  let result = props.menus;

  // Arama
  if (search.value) {
    result = result.filter(menu => 
      menu.name[locale.value].toLowerCase().includes(search.value.toLowerCase()) || 
      (menu.url && menu.url.toLowerCase().includes(search.value.toLowerCase()))
    );
  }

  // Sıralama
  result = [...result].sort((a, b) => {
    let compareA, compareB;

    if (sortColumn.value === 'name') {
      compareA = a.name[locale.value].toLowerCase();
      compareB = b.name[locale.value].toLowerCase();
    } else if (sortColumn.value === 'parent_id') {
      compareA = a.parent ? a.parent.name[locale.value].toLowerCase() : '';
      compareB = b.parent ? b.parent.name[locale.value].toLowerCase() : '';
    } else if (sortColumn.value === 'liste') {
      compareA = a.liste ? (locale.value === 'tr' ? 'Evet' : 'Yes') : (locale.value === 'tr' ? 'Hayır' : 'No');
      compareB = b.liste ? (locale.value === 'tr' ? 'Evet' : 'Yes') : (locale.value === 'tr' ? 'Hayır' : 'No');
    } else if (sortColumn.value === 'main_grup') {
      // main_grup alanını string olarak karşılaştırıyoruz
      compareA = a.main_grup ? a.main_grup.toLowerCase() : '';
      compareB = b.main_grup ? b.main_grup.toLowerCase() : '';
    } else {
      compareA = a[sortColumn.value];
      compareB = b[sortColumn.value];
    }

    if (compareA === null) compareA = '';
    if (compareB === null) compareB = '';

    if (sortDirection.value === 'asc') {
      return compareA > compareB ? 1 : -1;
    } else {
      return compareA < compareB ? 1 : -1;
    }
  });

  return result;
});

const confirmDelete = (id) => {
  if (confirm(locale.value === 'tr'
      ? 'Bu menüyü silmek istediğinizden emin misiniz?'
      : 'Are you sure you want to delete this menu?')) {
  //  window.location.href = route('admin.menus.destroy', id);
    Inertia.delete(route('admin.menus.destroy', id));

  }
};
</script>

<template>
  <Head :title="locale === 'tr' ? 'Menüler' : 'Menus'" />

  <AdminLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        {{ locale === 'tr' ? 'Menüler' : 'Menus' }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 bg-white border-b border-gray-200">
            <div class="flex justify-between items-center mb-6">
              <div class="flex-1">
                <input 
                  type="text" 
                  v-model="search" 
                  :placeholder="locale === 'tr' ? 'Menülerde ara...' : 'Search menus...'" 
                  class="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm w-full sm:w-1/3"
                />
              </div>
              <Link 
                :href="route('admin.menus.create')" 
                class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 "
              >
                {{ locale === 'tr' ? 'Menü Oluştur' : 'Create Menu' }}
              </Link>
            </div>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" @click="sortBy('id')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer">
                      ID
                      <component :is="sortIcon('id')" v-if="sortIcon('id')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <th scope="col" @click="sortBy('name')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer">
                      {{ locale === 'tr' ? 'Menü Adı' : 'Menu Name' }}
                      <component :is="sortIcon('name')" v-if="sortIcon('name')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <th scope="col" @click="sortBy('url')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer">
                      URL
                      <component :is="sortIcon('url')" v-if="sortIcon('url')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <th scope="col" @click="sortBy('parent_id')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer">
                      {{ locale === 'tr' ? 'Üst Menü' : 'Parent Menu' }}
                      <component :is="sortIcon('parent_id')" v-if="sortIcon('parent_id')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <!-- Yeni Ana Grup Sütunu -->
                    <th scope="col" @click="sortBy('main_grup')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer">
                      {{ locale === 'tr' ? 'Ana Grup' : 'Main Group' }}
                      <component :is="sortIcon('main_grup')" v-if="sortIcon('main_grup')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <th scope="col" @click="sortBy('is_active')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer">
                      {{ locale === 'tr' ? 'Durum' : 'Status' }}
                      <component :is="sortIcon('is_active')" v-if="sortIcon('is_active')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <th scope="col" @click="sortBy('liste')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer">
                      {{ locale === 'tr' ? 'Liste' : 'List' }}
                      <component :is="sortIcon('liste')" v-if="sortIcon('liste')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <th scope="col" @click="sortBy('order')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer">
                      {{ locale === 'tr' ? 'Sıra' : 'Order' }}
                      <component :is="sortIcon('order')" v-if="sortIcon('order')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {{ locale === 'tr' ? 'İşlemler' : 'Actions' }}
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="menu in filteredMenus" :key="menu.id">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ menu.id }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">
                        {{ menu.name[locale] }}
                      </div>
                    
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ menu.url || '-' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ menu.parent_id || '-' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ menu.main_grup }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span 
                        :class="[
                          'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                          menu.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        ]"
                      >
                        {{ menu.is_active ? (locale === 'tr' ? 'Aktif' : 'Active') : (locale === 'tr' ? 'Pasif' : 'Inactive') }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ menu.liste ? (locale === 'tr' ? 'Evet' : 'Yes') : (locale === 'tr' ? 'Hayır' : 'No') }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ menu.order }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Link 
                        :href="route('admin.menus.show', menu.id)" 
                        class="mr-4 text-blue-600 hover:text-blue-900 inline-block"
                      >
                        <EyeIcon class="w-5 h-5" />
                      </Link>
                      <Link 
                        :href="route('admin.menus.edit', menu.id)" 
                        class="mr-4 text-indigo-600 hover:text-indigo-900 inline-block"
                      >
                        <PencilSquareIcon class="w-5 h-5" />
                      </Link>
                      <a 
                        href="#"
                        class="text-red-600 hover:text-red-900 inline-block"
                        @click.prevent="confirmDelete(menu.id)"
                      >
                        <TrashIcon class="w-5 h-5" />
                      </a>
                    </td>
                  </tr>
                  <tr v-if="filteredMenus.length === 0">
                    <td colspan="9" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                      {{ locale === 'tr' ? 'Menü bulunamadı' : 'No menus found' }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>
