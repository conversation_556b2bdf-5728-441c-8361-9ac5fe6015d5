<script setup>
import { useLocaleStore } from '@/Stores/locale';
import QuillEditor from '@/components/QuillEditor.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const form = useForm({
    title: {
        tr: '',
        en: '',
    },
    content: {
        tr: '',
        en: '',
    },
    meta_description: {
        tr: '',
        en: '',
    },
    meta_keywords: {
        tr: '',
        en: '',
    },
    is_active: true,
    order: 0,
    main_grup: 'pages',
});

const submit = () => {
    form.post(route('admin.pages.store'));
};

const activeTab = ref('tr');
</script>

<template>
    <Head :title="locale === 'tr' ? '<PERSON><PERSON> Oluştur' : 'Create Page'" />

    <AdminLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">
                {{ locale === 'tr' ? 'Sayfa Oluştur' : 'Create Page' }}
            </h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="border-b border-gray-200 bg-white p-6">
                        <form @submit.prevent="submit">
                            <!-- Kayıt Butonu En Üstte -->
                            <div class="mb-4 flex justify-end">
                                <button
                                    type="submit"
                                    class="inline-flex items-center rounded-md border border-transparent bg-gray-800 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-gray-700 focus:border-gray-900 focus:outline-none focus:ring focus:ring-gray-300 active:bg-gray-900 disabled:opacity-25"
                                    :disabled="form.processing"
                                >
                                    {{ locale === 'tr' ? 'Kaydet' : 'Save' }}
                                </button>
                            </div>

                            <!-- Dil Tabları -->
                            <div class="mb-4 border-b border-gray-200">
                                <nav class="-mb-px flex">
                                    <button
                                        type="button"
                                        @click="activeTab = 'tr'"
                                        :class="activeTab === 'tr' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                                        class="w-1/2 border-b-2 px-1 py-4 text-center text-sm font-medium"
                                    >
                                        Türkçe
                                    </button>
                                    <button
                                        type="button"
                                        @click="activeTab = 'en'"
                                        :class="activeTab === 'en' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                                        class="w-1/2 border-b-2 px-1 py-4 text-center text-sm font-medium"
                                    >
                                        English
                                    </button>
                                </nav>
                            </div>

                            <!-- Dil Alanları (Tab İçeriği) -->
                            <div v-if="activeTab === 'tr'">
                                <!-- Türkçe Başlık -->
                                <div class="mb-4">
                                    <label for="title_tr" class="block text-sm font-medium text-gray-700"> Başlık (Türkçe) </label>
                                    <input type="text" id="title_tr" v-model="form.title.tr" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required />
                                    <div v-if="form.errors['title.tr']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['title.tr'] }}
                                    </div>
                                </div>

                                <!-- Türkçe İçerik -->
                                <div class="mb-4">
                                    <label for="content_tr" class="block text-sm font-medium text-gray-700"> İçerik (Türkçe) </label>
                                    <QuillEditor v-model="form.content.tr" />
                                    <div v-if="form.errors['content.tr']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['content.tr'] }}
                                    </div>
                                </div>

                                <!-- Türkçe Meta Açıklama -->
                                <div class="mb-4">
                                    <label for="meta_description_tr" class="block text-sm font-medium text-gray-700"> Meta Açıklama (Türkçe) </label>
                                    <textarea id="meta_description_tr" v-model="form.meta_description.tr" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" rows="3" />
                                    <div v-if="form.errors['meta_description.tr']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['meta_description.tr'] }}
                                    </div>
                                </div>

                                <!-- Türkçe Meta Anahtar Kelimeler -->
                                <div class="mb-4">
                                    <label for="meta_keywords_tr" class="block text-sm font-medium text-gray-700"> Meta Anahtar Kelimeleri (Türkçe) </label>
                                    <textarea id="meta_keywords_tr" v-model="form.meta_keywords.tr" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" rows="2" />
                                    <div v-if="form.errors['meta_keywords.tr']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['meta_keywords.tr'] }}
                                    </div>
                                </div>
                            </div>

                            <div v-else-if="activeTab === 'en'">
                                <!-- İngilizce Başlık -->
                                <div class="mb-4">
                                    <label for="title_en" class="block text-sm font-medium text-gray-700"> Title (English) </label>
                                    <input type="text" id="title_en" v-model="form.title.en" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required />
                                    <div v-if="form.errors['title.en']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['title.en'] }}
                                    </div>
                                </div>

                                <!-- İngilizce İçerik -->
                                <div class="mb-4">
                                    <label for="content_en" class="block text-sm font-medium text-gray-700"> Content (English) </label>
                                    <QuillEditor v-model="form.content.en" />
                                    <div v-if="form.errors['content.en']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['content.en'] }}
                                    </div>
                                </div>

                                <!-- İngilizce Meta Açıklama -->
                                <div class="mb-4">
                                    <label for="meta_description_en" class="block text-sm font-medium text-gray-700"> Meta Description (English) </label>
                                    <textarea id="meta_description_en" v-model="form.meta_description.en" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" rows="3" />
                                    <div v-if="form.errors['meta_description.en']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['meta_description.en'] }}
                                    </div>
                                </div>

                                <!-- İngilizce Meta Anahtar Kelimeler -->
                                <div class="mb-4">
                                    <label for="meta_keywords_en" class="block text-sm font-medium text-gray-700"> Meta Keywords (English) </label>
                                    <textarea id="meta_keywords_en" v-model="form.meta_keywords.en" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" rows="2" />
                                    <div v-if="form.errors['meta_keywords.en']" class="mt-1 text-sm text-red-500">
                                        {{ form.errors['meta_keywords.en'] }}
                                    </div>
                                </div>
                            </div>

                            <!-- Dil Bağımsız Alanlar -->
                            <div class="mt-6 grid grid-cols-1 gap-6 md:grid-cols-3">
                                <!-- Order -->
                                <div>
                                    <label for="order" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Sıra' : 'Order' }}
                                    </label>
                                    <input type="number" id="order" v-model="form.order" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                                    <div v-if="form.errors.order" class="mt-1 text-sm text-red-500">
                                        {{ form.errors.order }}
                                    </div>
                                </div>

                                <!-- Is Active -->
                                <div>
                                    <label for="is_active" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Durum' : 'Status' }}
                                    </label>
                                    <select id="is_active" v-model="form.is_active" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option :value="true">{{ locale === 'tr' ? 'Aktif' : 'Active' }}</option>
                                        <option :value="false">{{ locale === 'tr' ? 'Pasif' : 'Inactive' }}</option>
                                    </select>
                                    <div v-if="form.errors.is_active" class="mt-1 text-sm text-red-500">
                                        {{ form.errors.is_active }}
                                    </div>
                                </div>

                                <!-- Main Group -->
                                <div>
                                    <label for="main_grup" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Ana Grup' : 'Main Group' }}
                                    </label>
                                    <input type="text" id="main_grup" v-model="form.main_grup" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                                    <div v-if="form.errors.main_grup" class="mt-1 text-sm text-red-500">
                                        {{ form.errors.main_grup }}
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
