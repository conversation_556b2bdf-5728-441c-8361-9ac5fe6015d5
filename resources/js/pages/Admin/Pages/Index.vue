<script setup>
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { ref, computed } from 'vue';
import { EyeIcon, PencilSquareIcon, TrashIcon, ArrowUpIcon, ArrowDownIcon } from '@heroicons/vue/24/outline';
import { useLocaleStore } from '@/Stores/locale';
import { Inertia } from '@inertiajs/inertia';

import Swal from 'sweetalert2';
const props = defineProps({
  pages: Array,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
const search = ref('');

// Sıralama için değişkenler
const sortColumn = ref('order'); // Varsayılan olarak 'order' sütunu
const sortDirection = ref('asc');

const sortBy = (column) => {
  if (sortColumn.value === column) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortColumn.value = column;
    sortDirection.value = 'asc';
  }
};

const sortIcon = (column) => {
  if (sortColumn.value !== column) return null;
  return sortDirection.value === 'asc' ? ArrowUpIcon : ArrowDownIcon;
};

const filteredPages = computed(() => {
  let result = props.pages;
  
  // Filtreleme: title.tr veya title.en içinde arama
  if (search.value) {
    result = props.pages.filter(page => 
      page.title.tr.toLowerCase().includes(search.value.toLowerCase()) ||
      page.title.en.toLowerCase().includes(search.value.toLowerCase())
    );
  }
  
  // Sıralama
  result = [...result].sort((a, b) => {
    let compareA, compareB;
    
    if (sortColumn.value === 'title') {
      compareA = a.title[locale.value].toLowerCase();
      compareB = b.title[locale.value].toLowerCase();
    } else if (sortColumn.value === 'main_grup') {
      compareA = a.main_grup ? a.main_grup.toLowerCase() : '';
      compareB = b.main_grup ? b.main_grup.toLowerCase() : '';
    } else {
      compareA = a[sortColumn.value];
      compareB = b[sortColumn.value];
    }
    
    if (compareA === null) compareA = '';
    if (compareB === null) compareB = '';
    
    if (compareA < compareB) return sortDirection.value === 'asc' ? -1 : 1;
    if (compareA > compareB) return sortDirection.value === 'asc' ? 1 : -1;
    return 0;
  });
  
  return result;
});

 

const confirmDelete = (id, event) => {
  console.log("ok");
  event.preventDefault();
  Swal.fire({
    title: locale.value === 'tr' ? 'Emin misiniz?' : 'Are you sure?',
    text: locale.value === 'tr'
      ? 'Bu sayfayı silmek istediğinizden emin misiniz...?'
      : 'Are you sure you want to delete this page?',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: locale.value === 'tr' ? 'Evet, sil!' : 'Yes, delete it!',
    cancelButtonText: locale.value === 'tr' ? 'İptal' : 'Cancel'
  }).then((result) => {
    if (result.isConfirmed) {
      Inertia.delete(route('admin.pages.destroy', id));
    }
  });
};


</script>

<template>
  <Head :title="locale === 'tr' ? 'Sayfalar' : 'Pages'" />

  <AdminLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        {{ locale === 'tr' ? 'Sayfalar' : 'Pages' }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 bg-white border-b border-gray-200">
            <div class="flex justify-between items-center mb-6">
              <div class="flex-1">
                <input 
                  type="text" 
                  v-model="search" 
                  :placeholder="locale === 'tr' ? 'Sayfalarda ara...' : 'Search pages...'" 
                  class="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm w-full sm:w-1/3"
                />
              </div>
              <Link 
                :href="route('admin.pages.create')" 
                class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 "
              >
                {{ locale === 'tr' ? 'Sayfa Oluştur' : 'Create Page' }}
              </Link>
            </div>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th @click="sortBy('id')" class="cursor-pointer px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ID
                      <component :is="sortIcon('id')" v-if="sortIcon('id')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <th @click="sortBy('title')" class="cursor-pointer px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {{ locale === 'tr' ? 'Başlık (TR)' : 'Title (TR)' }}
                      <component :is="sortIcon('title')" v-if="sortIcon('title')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {{ locale === 'tr' ? 'Başlık (EN)' : 'Title (EN)' }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Slug
                    </th>
                    <th @click="sortBy('main_grup')" class="cursor-pointer px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {{ locale === 'tr' ? 'Ana Grup' : 'Main Group' }}
                      <component :is="sortIcon('main_grup')" v-if="sortIcon('main_grup')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <th @click="sortBy('is_active')" class="cursor-pointer px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {{ locale === 'tr' ? 'Durum' : 'Status' }}
                      <component :is="sortIcon('is_active')" v-if="sortIcon('is_active')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <th @click="sortBy('order')" class="cursor-pointer px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {{ locale === 'tr' ? 'Sıra' : 'Order' }}
                      <component :is="sortIcon('order')" v-if="sortIcon('order')" class="inline-block w-4 h-4 ml-1" />
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {{ locale === 'tr' ? 'İşlemler' : 'Actions' }}
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="page in filteredPages" :key="page.id">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ page.id }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ page.title.tr }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ page.title.en }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ page.slug }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ page.main_grup }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span v-if="page.is_active" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        {{ locale === 'tr' ? 'Aktif' : 'Active' }}
                      </span>
                      <span v-else class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                        {{ locale === 'tr' ? 'Pasif' : 'Inactive' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ page.order }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Link :href="route('admin.pages.show', page.id)" class="mr-4 text-blue-600 hover:text-blue-900 inline-block">
                        <EyeIcon class="w-5 h-5" />
                      </Link>
                      <Link :href="route('admin.pages.edit', page.id)" class="mr-4 text-indigo-600 hover:text-indigo-900 inline-block">
                        <PencilSquareIcon class="w-5 h-5" />
                      </Link>
                      <a 
                        href="#"
                        class="text-red-600 hover:text-red-900 inline-block"
                         @click.prevent="confirmDelete(page.id, $event)"
                         :key="page.id"
                      >
                        <TrashIcon class="w-5 h-5" />
                      </a>
                      <form 
                        :id="`delete-form-${page.id}`" 
                        :action="route('admin.pages.destroy', page.id)" 
                        method="POST" 
                        class="hidden"
                      >
                        <input type="hidden" name="_method" value="DELETE">
                        <input type="hidden" name="_token" :value="$page.props.csrf_token">
                      </form>
                    </td>
                  </tr>
                  <tr v-if="filteredPages.length === 0">
                    <td colspan="8" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                      {{ locale === 'tr' ? 'Sayfa bulunamadı.' : 'No pages found.' }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>
