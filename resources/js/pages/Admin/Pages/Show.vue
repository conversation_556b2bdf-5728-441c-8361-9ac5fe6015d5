<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { computed } from 'vue';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    page: Object,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const confirmDelete = (id) => {
    if (confirm(locale.value === 'tr' ? 'Bu say<PERSON><PERSON><PERSON> silmek istediğinizden emin misiniz?' : 'Are you sure you want to delete this page?')) {
        window.location.href = route(`admin.users.destroy`, id);
    }
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'Sayfa Detayı' : 'Page Details'" />

    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ locale === 'tr' ? '<PERSON><PERSON> Detayı' : 'Page Details' }}: {{ page.title[locale] }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex justify-end mb-6">
                            <Link 
                                :href="route('admin.pages.edit', page.id)" 
                                class="inline-flex items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-500 active:bg-yellow-700 focus:outline-none focus:border-yellow-700 focus:ring focus:ring-yellow-300 disabled:opacity-25  mr-2"
                            >
                                {{ locale === 'tr' ? 'Düzenle' : 'Edit' }}
                            </Link>
                            <button 
                                @click="confirmDelete(page.id)" 
                                class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:border-red-700 focus:ring focus:ring-red-300 disabled:opacity-25 "
                            >
                                {{ locale === 'tr' ? 'Sil' : 'Delete' }}
                            </button>
                            <form 
                                :id="`delete-form-${page.id}`" 
                                :action="route('admin.pages.destroy', page.id)" 
                                method="POST" 
                                class="hidden"
                            >
                                <input type="hidden" name="_method" value="DELETE">
                                <input type="hidden" name="_token" :value="$page.props.csrf_token">
                            </form>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">
                                    {{ locale === 'tr' ? 'Temel Bilgiler' : 'Basic Information' }}
                                </h3>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        ID
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ page.id }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Başlık (TR)' : 'Title (TR)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ page.title.tr }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Başlık (EN)' : 'Title (EN)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ page.title.en }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Durum' : 'Status' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        <span 
                                            :class="[
                                                'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                                page.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                            ]"
                                        >
                                            {{ page.is_active ? (locale === 'tr' ? 'Aktif' : 'Active') : (locale === 'tr' ? 'Pasif' : 'Inactive') }}
                                        </span>
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Sıra' : 'Order' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ page.order || 0 }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Ana Grup' : 'Main Group' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ page.main_grup }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        Slug
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ page.slug }}
                                    </dd>
                                </div>
                            </div>

                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">
                                    {{ locale === 'tr' ? 'SEO Bilgileri' : 'SEO Information' }}
                                </h3>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Meta Açıklama (TR)' : 'Meta Description (TR)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ page.meta_description?.tr || '-' }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Meta Açıklama (EN)' : 'Meta Description (EN)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ page.meta_description?.en || '-' }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Meta Anahtar Kelimeleri (TR)' : 'Meta Keywords (TR)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ page.meta_keywords?.tr || '-' }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Meta Anahtar Kelimeleri (EN)' : 'Meta Keywords (EN)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ page.meta_keywords?.en || '-' }}
                                    </dd>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">
                                {{ locale === 'tr' ? 'İçerik' : 'Content' }}
                            </h3>
                            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                                <div class="px-4 py-5 sm:px-6 bg-gray-50">
                                    <h4 class="text-md font-medium text-gray-900">
                                        {{ locale === 'tr' ? 'Türkçe İçerik' : 'Turkish Content' }}
                                    </h4>
                                </div>
                                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                                    <div class="prose max-w-none" v-html="page.content.tr"></div>
                                </div>
                                <div class="px-4 py-5 sm:px-6 bg-gray-50">
                                    <h4 class="text-md font-medium text-gray-900">
                                        {{ locale === 'tr' ? 'İngilizce İçerik' : 'English Content' }}
                                    </h4>
                                </div>
                                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                                    <div class="prose max-w-none" v-html="page.content.en"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
