<script setup lang="ts">
import { Head, Link, router } from '@inertiajs/vue3'
import { ref } from 'vue'
import AdminLayout from '@/layouts/AdminLayout.vue'
import { formatBytes, formatDate } from '@/lib/utils'

defineProps<{
  backups: Array<{
    name: string
    size: number
    last_modified: number
    path: string
    download_url: string
  }>
}>()

const createBackup = () => {
  router.post(route('admin.backups.create'))
}

const deleteBackup = (filename: string) => {
  if (confirm('Bu ye<PERSON> dosyasını silmek istediğinizden emin misiniz?')) {
    router.delete(route('admin.backups.destroy', filename))
  }
}
</script>

<template>
  <AdminLayout>
    <Head title="Yedekleme Yönetimi" />

    <div class="container mx-auto py-6">
      <div class="mb-6 flex items-center justify-between">
        <h1 class="text-2xl font-semibold"><PERSON><PERSON><PERSON><PERSON></h1>
        <button
          @click="createBackup"
          class="rounded-md bg-primary-600 px-4 py-2 text-white hover:bg-primary-700"
        >
          <PERSON><PERSON>luştur
        </button>
      </div>

      <div class="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Dosya Adı
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Boyut
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Oluşturulma Tarihi
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  İşlemler
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="backup in backups" :key="backup.path">
                <td class="whitespace-nowrap px-6 py-4">
                  {{ backup.name }}
                </td>
                <td class="whitespace-nowrap px-6 py-4">
                  {{ formatBytes(backup.size) }}
                </td>
                <td class="whitespace-nowrap px-6 py-4">
                  {{ formatDate(backup.last_modified) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-4">
                    <a :href="backup.download_url" class="text-green-600 hover:text-green-900 inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                        <path fill-rule="evenodd" d="M10.5 3.75a6 6 0 0 0-5.98 6.496A5.25 5.25 0 0 0 6.75 20.25H18a4.5 4.5 0 0 0 2.206-8.423 3.75 3.75 0 0 0-4.133-4.303A6.001 6.001 0 0 0 10.5 3.75Zm2.03 5.47a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 1 0 1.06 1.06l1.72-1.72v4.94a.75.75 0 0 0 1.5 0v-4.94l1.72 1.72a.75.75 0 1 0 1.06-1.06l-3-3Z" clip-rule="evenodd" />
                      </svg>
                    </a>
                    <a href="#" @click.prevent="deleteBackup(backup.name)" class="text-red-600 hover:text-red-900 inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                        <path fill-rule="evenodd" d="M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z" clip-rule="evenodd" />
                      </svg>
                    </a>
                  </div>
                </td>
              </tr>
              <tr v-if="backups.length === 0">
                <td colspan="4" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  Henüz yedek oluşturulmamış.
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>
