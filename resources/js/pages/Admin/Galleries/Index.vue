<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { ref, computed } from 'vue';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    galleries: Array,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
const search = ref('');

const filteredGalleries = computed(() => {
    if (!search.value) return props.galleries;
    
    return props.galleries.filter(gallery => 
        gallery.title[locale.value].toLowerCase().includes(search.value.toLowerCase()) || 
        (gallery.description && gallery.description[locale.value].toLowerCase().includes(search.value.toLowerCase()))
    );
});

const confirmDelete = (id) => {
    if (confirm(locale.value === 'tr' ? '<PERSON>u galeriyi silmek istediğinizden emin misiniz?' : 'Are you sure you want to delete this gallery?')) {
        window.location.href = route(`admin.users.destroy`, id);
    }
};

const fixPath = (path) => {
    // Eğer path '/' ile başlamıyorsa başına ekle
    return path.charAt(0) === '/' ? path : '/' + path;
};

</script>

<template>
    <Head :title="locale === 'tr' ? 'Galeriler' : 'Galleries'" />

    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ locale === 'tr' ? 'Galeriler' : 'Galleries' }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex justify-between items-center mb-6">
                            <div class="flex-1">
                                <input 
                                    type="text" 
                                    v-model="search" 
                                    :placeholder="locale === 'tr' ? 'Galerilerde ara...' : 'Search galleries...'" 
                                    class="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm w-full sm:w-1/3"
                                />
                            </div>
                            <Link 
                                :href="route('admin.gallery.create')" 
                                class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 "
                            >
                                {{ locale === 'tr' ? 'Galeri Oluştur' : 'Create Gallery' }}
                            </Link>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            ID
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ locale === 'tr' ? 'Görsel' : 'Image' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ locale === 'tr' ? 'Başlık' : 'Title' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ locale === 'tr' ? 'Durum' : 'Status' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ locale === 'tr' ? 'Sıra' : 'Order' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ locale === 'tr' ? 'Resimler' : 'Images' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ locale === 'tr' ? 'İşlemler' : 'Actions' }}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="gallery in filteredGalleries" :key="gallery.id">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ gallery.id }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <img 
                                                    v-if="gallery.thumbnail_path" 
                                                    :src="fixPath(gallery.thumbnail_path)" 
                                                    :alt="gallery.title[locale]" 
                                                    class="h-10 w-10 rounded-full object-cover"
                                                />
                                                <div v-else class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                                    <span class="text-gray-500 text-xs">{{ locale === 'tr' ? 'Görsel Yok' : 'No Image' }}</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ gallery.title[locale] }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ gallery.slug }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span 
                                                :class="[
                                                    'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                                    gallery.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                                ]"
                                            >
                                                {{ gallery.is_active ? (locale === 'tr' ? 'Aktif' : 'Active') : (locale === 'tr' ? 'Pasif' : 'Inactive') }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ gallery.order }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <Link :href="route('admin.gallery_images.show', gallery.id)" class="mr-4 text-blue-600 hover:text-blue-900 inline-block">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                                                    <path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" />
                                                    <path fill-rule="evenodd" d="M1.323 11.447C2.811 6.976 7.028 3.75 12.001 3.75c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113-1.487 4.471-5.705 7.697-10.677 7.697-4.97 0-9.186-3.223-10.675-7.69a1.762 1.762 0 0 1 0-1.113ZM17.25 12a5.25 5.25 0 1 1-10.5 0 5.25 5.25 0 0 1 10.5 0Z" clip-rule="evenodd" />
                                                </svg>
                                            </Link>

                                            /
                                            <Link :href="route('admin.gallery_images.create', gallery.id)" class="mr-4 text-blue-600 hover:text-blue-900 inline-block">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
        <path d="M12 4v16m8-8H4" />
    </svg>
</Link>


                                        </td>

                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <Link :href="route('admin.gallery.show', gallery.id)" class="mr-4 text-blue-600 hover:text-blue-900 inline-block">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                                                  <path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" />
                                                  <path fill-rule="evenodd" d="M1.323 11.447C2.811 6.976 7.028 3.75 12.001 3.75c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113-1.487 4.471-5.705 7.697-10.677 7.697-4.97 0-9.186-3.223-10.675-7.69a1.762 1.762 0 0 1 0-1.113ZM17.25 12a5.25 5.25 0 1 1-10.5 0 5.25 5.25 0 0 1 10.5 0Z" clip-rule="evenodd" />
                                                </svg>
                                            </Link>
                                            <Link :href="route('admin.gallery.edit', gallery.id)" class="mr-4 text-yellow-600 hover:text-yellow-900 inline-block">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                                                  <path d="M21.731 2.269a2.625 2.625 0 0 0-3.712 0l-1.157 1.157 3.712 3.712 1.157-1.157a2.625 2.625 0 0 0 0-3.712ZM19.513 8.199l-3.712-3.712-8.4 8.4a5.25 5.25 0 0 0-1.32 2.214l-.8 2.685a.75.75 0 0 0 .933.933l2.685-.8a5.25 5.25 0 0 0 2.214-1.32l8.4-8.4Z" />
                                                  <path d="M5.25 5.25a3 3 0 0 0-3 3v10.5a3 3 0 0 0 3 3h10.5a3 3 0 0 0 3-3V13.5a.75.75 0 0 0-1.5 0v5.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5V8.25a1.5 1.5 0 0 1 1.5-1.5h5.25a.75.75 0 0 0 0-1.5H5.25Z" />
                                                </svg>
                                            </Link>
                                            <a href="#" @click.prevent="confirmDelete(gallery.id)" class="text-red-600 hover:text-red-900 inline-block">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                                                  <path fill-rule="evenodd" d="M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z" clip-rule="evenodd" />
                                                </svg>
                                            </a>
                                            <form 
                                                :id="`delete-form-${gallery.id}`" 
                                                :action="route('admin.gallery.destroy', gallery.id)" 
                                                method="POST" 
                                                class="hidden"
                                            >
                                                <input type="hidden" name="_method" value="DELETE">
                                                <input type="hidden" name="_token" :value="$page.props.csrf_token">
                                            </form>
                                        </td>
                                    </tr>
                                    <tr v-if="filteredGalleries.length === 0">
                                        <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                            {{ locale === 'tr' ? 'Galeri bulunamadı' : 'No galleries found' }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
