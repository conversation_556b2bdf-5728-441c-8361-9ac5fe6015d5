<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { computed } from 'vue';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    gallery: Object,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const confirmDelete = (id) => {
    if (confirm(locale.value === 'tr' ? 'Bu galeriyi silmek istediğinizden emin misiniz?' : 'Are you sure you want to delete this gallery?')) {
        window.location.href = route(`admin.users.destroy`, id);
    }
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'Galeri <PERSON>ayı' : 'Gallery Details'" />

    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ locale === 'tr' ? '<PERSON><PERSON>' : 'Gallery Details' }}: {{ gallery.title[locale] }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex justify-end mb-6">
                            <Link 
                                :href="route('admin.gallery.edit', gallery.id)" 
                                class="inline-flex items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-500 active:bg-yellow-700 focus:outline-none focus:border-yellow-700 focus:ring focus:ring-yellow-300 disabled:opacity-25  mr-2"
                            >
                                {{ locale === 'tr' ? 'Düzenle' : 'Edit' }}
                            </Link>
                            <button 
                                @click="confirmDelete(gallery.id)" 
                                class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:border-red-700 focus:ring focus:ring-red-300 disabled:opacity-25 "
                            >
                                {{ locale === 'tr' ? 'Sil' : 'Delete' }}
                            </button>
                            <form 
                                :id="`delete-form-${gallery.id}`" 
                                :action="route('admin.gallery.destroy', gallery.id)" 
                                method="POST" 
                                class="hidden"
                            >
                                <input type="hidden" name="_method" value="DELETE">
                                <input type="hidden" name="_token" :value="$page.props.csrf_token">
                            </form>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">
                                    {{ locale === 'tr' ? 'Temel Bilgiler' : 'Basic Information' }}
                                </h3>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        ID
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ gallery.id }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Başlık (TR)' : 'Title (TR)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ gallery.title.tr }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Başlık (EN)' : 'Title (EN)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ gallery.title.en }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Açıklama (TR)' : 'Description (TR)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ gallery.description?.tr || '-' }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Açıklama (EN)' : 'Description (EN)' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ gallery.description?.en || '-' }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Durum' : 'Status' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        <span 
                                            :class="[
                                                'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                                gallery.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                            ]"
                                        >
                                            {{ gallery.is_active ? (locale === 'tr' ? 'Aktif' : 'Active') : (locale === 'tr' ? 'Pasif' : 'Inactive') }}
                                        </span>
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Sıra' : 'Order' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ gallery.order || 0 }}
                                    </dd>
                                </div>
                                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">
                                        {{ locale === 'tr' ? 'Ana Grup' : 'Main Group' }}
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ gallery.main_grup }}
                                    </dd>
                                </div>
                                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-lg">
                                    <dt class="text-sm font-medium text-gray-500">
                                        Slug
                                    </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                        {{ gallery.slug }}
                                    </dd>
                                </div>
                            </div>

                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">
                                    {{ locale === 'tr' ? 'Görsel' : 'Image' }}
                                </h3>
                                <div class="bg-white p-4 rounded-lg border border-gray-200">
                                    <div v-if="gallery.image_path" class="flex justify-center">
                                        <img 
                                            :src="gallery.image_path" 
                                            :alt="gallery.title[locale]" 
                                            class="max-w-full h-auto rounded-lg shadow-md"
                                        />
                                    </div>
                                    <div v-else class="flex justify-center items-center h-64 bg-gray-100 rounded-lg">
                                        <p class="text-gray-500">
                                            {{ locale === 'tr' ? 'Görsel bulunamadı' : 'No image found' }}
                                        </p>
                                    </div>
                                    
                                    <div class="mt-4">
                                        <h4 class="text-sm font-medium text-gray-700 mb-2">
                                            {{ locale === 'tr' ? 'Görsel Yolları' : 'Image Paths' }}
                                        </h4>
                                        <div class="bg-gray-50 p-3 rounded-md">
                                            <p class="text-xs text-gray-700 mb-1">
                                                <span class="font-medium">{{ locale === 'tr' ? 'Orijinal Görsel:' : 'Original Image:' }}</span> 
                                                {{ gallery.image_path || '-' }}
                                            </p>
                                            <p class="text-xs text-gray-700">
                                                <span class="font-medium">{{ locale === 'tr' ? 'Küçük Resim:' : 'Thumbnail:' }}</span> 
                                                {{ gallery.thumbnail_path || '-' }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
