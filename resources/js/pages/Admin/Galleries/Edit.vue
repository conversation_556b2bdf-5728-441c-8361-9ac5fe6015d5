<script setup>
import { Head, useForm } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { computed, ref } from 'vue';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    gallery: Object,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
const imagePreview = ref(props.gallery.thumbnail_path || null);

const form = useForm({
    title: props.gallery.title,
    description: props.gallery.description || { tr: '', en: '' },
    image: null,
    is_active: props.gallery.is_active,
    order: props.gallery.order || 0,
    main_grup: props.gallery.main_grup,
    _method: 'PUT',
});

const handleImageUpload = (e) => {
    const file = e.target.files[0];
    form.image = file;
    
    if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            imagePreview.value = e.target.result;
        };
        reader.readAsDataURL(file);
    }
};

const submit = () => {
    form.post(route('admin.gallery.update', props.gallery.id), {
        forceFormData: true,
    });
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'Galeri Düzenle' : 'Edit Gallery'" />

    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ locale === 'tr' ? 'Galeri Düzenle' : 'Edit Gallery' }}: {{ gallery.title[locale] }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <form @submit.prevent="submit" enctype="multipart/form-data">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Turkish Title -->
                                <div>
                                    <label for="title_tr" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Başlık (Türkçe)' : 'Title (Turkish)' }}
                                    </label>
                                    <input 
                                        type="text" 
                                        id="title_tr" 
                                        v-model="form.title.tr" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                        required
                                    />
                                    <div v-if="form.errors['title.tr']" class="text-red-500 text-sm mt-1">
                                        {{ form.errors['title.tr'] }}
                                    </div>
                                </div>

                                <!-- English Title -->
                                <div>
                                    <label for="title_en" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Başlık (İngilizce)' : 'Title (English)' }}
                                    </label>
                                    <input 
                                        type="text" 
                                        id="title_en" 
                                        v-model="form.title.en" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                        required
                                    />
                                    <div v-if="form.errors['title.en']" class="text-red-500 text-sm mt-1">
                                        {{ form.errors['title.en'] }}
                                    </div>
                                </div>

                                <!-- Turkish Description -->
                                <div>
                                    <label for="description_tr" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Açıklama (Türkçe)' : 'Description (Turkish)' }}
                                    </label>
                                    <textarea 
                                        id="description_tr" 
                                        v-model="form.description.tr" 
                                        rows="3" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                    ></textarea>
                                    <div v-if="form.errors['description.tr']" class="text-red-500 text-sm mt-1">
                                        {{ form.errors['description.tr'] }}
                                    </div>
                                </div>

                                <!-- English Description -->
                                <div>
                                    <label for="description_en" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Açıklama (İngilizce)' : 'Description (English)' }}
                                    </label>
                                    <textarea 
                                        id="description_en" 
                                        v-model="form.description.en" 
                                        rows="3" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                    ></textarea>
                                    <div v-if="form.errors['description.en']" class="text-red-500 text-sm mt-1">
                                        {{ form.errors['description.en'] }}
                                    </div>
                                </div>

                                <!-- Order -->
                                <div>
                                    <label for="order" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Sıra' : 'Order' }}
                                    </label>
                                    <input 
                                        type="number" 
                                        id="order" 
                                        v-model="form.order" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                    />
                                    <div v-if="form.errors.order" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.order }}
                                    </div>
                                </div>

                                <!-- Is Active -->
                                <div>
                                    <label for="is_active" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Durum' : 'Status' }}
                                    </label>
                                    <select 
                                        id="is_active" 
                                        v-model="form.is_active" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                    >
                                        <option :value="true">{{ locale === 'tr' ? 'Aktif' : 'Active' }}</option>
                                        <option :value="false">{{ locale === 'tr' ? 'Pasif' : 'Inactive' }}</option>
                                    </select>
                                    <div v-if="form.errors.is_active" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.is_active }}
                                    </div>
                                </div>

                                <!-- Main Group -->
                                <div>
                                    <label for="main_grup" class="block text-sm font-medium text-gray-700">
                                        {{ locale === 'tr' ? 'Ana Grup' : 'Main Group' }}
                                    </label>
                                    <input 
                                        type="text" 
                                        id="main_grup" 
                                        v-model="form.main_grup" 
                                        class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                        required
                                    />
                                    <div v-if="form.errors.main_grup" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.main_grup }}
                                    </div>
                                </div>
                            </div>

                            <!-- Image Upload -->
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700">
                                    {{ locale === 'tr' ? 'Görsel' : 'Image' }}
                                </label>
                                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                    <div class="space-y-1 text-center">
                                        <div v-if="imagePreview" class="mb-4">
                                            <img :src="imagePreview" alt="Preview" class="mx-auto h-32 w-auto">
                                        </div>
                                        <div v-else class="flex text-sm text-gray-600">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                        </div>
                                        <div class="flex text-sm text-gray-600">
                                            <label for="image" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                                                <span>{{ locale === 'tr' ? 'Dosya yükle' : 'Upload a file' }}</span>
                                                <input id="image" name="image" type="file" class="sr-only" @change="handleImageUpload" accept="image/*">
                                            </label>
                                            <p class="pl-1">{{ locale === 'tr' ? 'veya sürükleyip bırakın' : 'or drag and drop' }}</p>
                                        </div>
                                        <p class="text-xs text-gray-500">
                                            PNG, JPG, GIF {{ locale === 'tr' ? 'en fazla 10MB' : 'up to 10MB' }}
                                        </p>
                                    </div>
                                </div>
                                <div v-if="form.errors.image" class="text-red-500 text-sm mt-1">
                                    {{ form.errors.image }}
                                </div>
                            </div>

                            <div class="mt-6 flex justify-end">
                                <button 
                                    type="submit" 
                                    class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 "
                                    :disabled="form.processing"
                                >
                                    {{ locale === 'tr' ? 'Güncelle' : 'Update' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
