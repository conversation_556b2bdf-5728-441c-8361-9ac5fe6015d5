<script setup lang="ts">
import Pagination from '@/components/Pagination.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { debounce } from 'lodash';
import { ref, watch } from 'vue';

const props = defineProps({
    orders: Object,
    stats: Object,
    filters: Object,
    statuses: Object,
});

// Reactive filters
const search = ref(props.filters.search || '');
const status = ref(props.filters.status || '');
const dateFrom = ref(props.filters.date_from || '');
const dateTo = ref(props.filters.date_to || '');
const sort = ref(props.filters.sort || 'created_at');
const direction = ref(props.filters.direction || 'desc');

// Currency formatter
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY',
        minimumFractionDigits: 2,
    }).format(amount);
};

// Date formatter
const formatDate = (date) => {
    return new Date(date).toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

// Status badge colors
const getStatusColor = (status) => {
    const colors = {
        pending: 'bg-theme-accent/30 text-theme-general',
        payment_pending: 'bg-theme-accent/50 text-theme-general',
        paid: 'bg-theme-secondary/50 text-white',
        payment_failed: 'bg-red-100 text-red-800',
        processing: 'bg-theme-primary/30 text-theme-general',
        shipped: 'bg-theme-secondary/30 text-theme-general',
        delivered: 'bg-theme-secondary/50 text-white',
        completed: 'bg-theme-secondary/50 text-white',
        cancelled: 'bg-red-100 text-red-800',
        refunded: 'bg-gray-100 text-gray-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
};

// Debounced search
const debouncedSearch = debounce(() => {
    applyFilters();
}, 300);

// Watch for search changes
watch(search, () => {
    debouncedSearch();
});

// Apply filters
const applyFilters = () => {
    router.get(
        route('admin.orders.index'),
        {
            search: search.value,
            status: status.value,
            date_from: dateFrom.value,
            date_to: dateTo.value,
            sort: sort.value,
            direction: direction.value,
        },
        {
            preserveState: true,
            replace: true,
        },
    );
};

// Clear filters
const clearFilters = () => {
    search.value = '';
    status.value = '';
    dateFrom.value = '';
    dateTo.value = '';
    sort.value = 'created_at';
    direction.value = 'desc';
    applyFilters();
};

// Sort by column
const sortBy = (column) => {
    if (sort.value === column) {
        direction.value = direction.value === 'asc' ? 'desc' : 'asc';
    } else {
        sort.value = column;
        direction.value = 'asc';
    }
    applyFilters();
};

// Export orders
const exportOrders = () => {
    window.open(
        route('admin.orders.export', {
            search: search.value,
            status: status.value,
            date_from: dateFrom.value,
            date_to: dateTo.value,
        }),
    );
};
</script>

<template>
    <Head title="Siparişler" />

    <AdminLayout>
        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Page Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">Siparişler</h1>
                    <p class="mt-2 text-gray-600">Tüm siparişleri görüntüleyin ve yönetin</p>
                </div>

                <!-- Statistics Cards -->
                <div class="mb-8 grid grid-cols-1 gap-6 md:grid-cols-5">
                    <div class="overflow-hidden rounded-lg bg-white shadow">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="truncate text-sm font-medium text-gray-500">Toplam Sipariş</dt>
                                        <dd class="text-lg font-medium text-gray-900">{{ stats.total_orders }}</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-hidden rounded-lg bg-white shadow">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="truncate text-sm font-medium text-gray-500">Bekleyen</dt>
                                        <dd class="text-lg font-medium text-gray-900">{{ stats.pending_orders }}</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-hidden rounded-lg bg-white shadow">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="truncate text-sm font-medium text-gray-500">Tamamlanan</dt>
                                        <dd class="text-lg font-medium text-gray-900">{{ stats.completed_orders }}</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-hidden rounded-lg bg-white shadow">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="truncate text-sm font-medium text-gray-500">İptal Edilen</dt>
                                        <dd class="text-lg font-medium text-gray-900">{{ stats.cancelled_orders }}</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-hidden rounded-lg bg-white shadow">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="truncate text-sm font-medium text-gray-500">Toplam Gelir</dt>
                                        <dd class="text-lg font-medium text-gray-900">{{ formatCurrency(stats.total_revenue) }}</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="mb-8 rounded-lg bg-white shadow">
                    <div class="border-b border-gray-200 px-6 py-4">
                        <h3 class="text-lg font-medium text-gray-900">Filtreler</h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <!-- Search -->
                            <div>
                                <label class="mb-1 block text-sm font-medium text-gray-700">Arama</label>
                                <input v-model="search" type="text" placeholder="Sipariş no, müşteri adı..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                            </div>

                            <!-- Status Filter -->
                            <div>
                                <label class="mb-1 block text-sm font-medium text-gray-700">Durum</label>
                                <select v-model="status" @change="applyFilters" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">Tüm Durumlar</option>
                                    <option v-for="(label, key) in statuses" :key="key" :value="key">{{ label }}</option>
                                </select>
                            </div>

                            <!-- Date From -->
                            <div>
                                <label class="mb-1 block text-sm font-medium text-gray-700">Başlangıç Tarihi</label>
                                <input v-model="dateFrom" @change="applyFilters" type="date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                            </div>

                            <!-- Date To -->
                            <div>
                                <label class="mb-1 block text-sm font-medium text-gray-700">Bitiş Tarihi</label>
                                <input v-model="dateTo" @change="applyFilters" type="date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
                            </div>
                        </div>

                        <div class="mt-4 flex justify-between">
                            <button @click="clearFilters" class="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Filtreleri Temizle</button>
                            <button @click="exportOrders" class="rounded-md border border-transparent bg-green-600 px-4 py-2 text-sm font-medium text-white hover:bg-green-700">CSV Olarak Dışa Aktar</button>
                        </div>
                    </div>
                </div>

                <!-- Orders Table -->
                <div class="overflow-hidden bg-white shadow sm:rounded-lg">
                    <div class="border-b border-gray-200 px-6 py-4">
                        <h3 class="text-lg font-medium text-gray-900">Siparişler ({{ orders.total }} adet)</h3>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th @click="sortBy('order_number')" class="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:bg-gray-100">
                                        Sipariş No
                                        <span v-if="sort === 'order_number'" class="ml-1">
                                            {{ direction === 'asc' ? '↑' : '↓' }}
                                        </span>
                                    </th>
                                    <th @click="sortBy('customer_name')" class="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:bg-gray-100">
                                        Müşteri
                                        <span v-if="sort === 'customer_name'" class="ml-1">
                                            {{ direction === 'asc' ? '↑' : '↓' }}
                                        </span>
                                    </th>
                                    <th @click="sortBy('created_at')" class="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:bg-gray-100">
                                        Tarih
                                        <span v-if="sort === 'created_at'" class="ml-1">
                                            {{ direction === 'asc' ? '↑' : '↓' }}
                                        </span>
                                    </th>
                                    <th @click="sortBy('total_amount')" class="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:bg-gray-100">
                                        Toplam
                                        <span v-if="sort === 'total_amount'" class="ml-1">
                                            {{ direction === 'asc' ? '↑' : '↓' }}
                                        </span>
                                    </th>
                                    <th @click="sortBy('status')" class="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:bg-gray-100">
                                        Durum
                                        <span v-if="sort === 'status'" class="ml-1">
                                            {{ direction === 'asc' ? '↑' : '↓' }}
                                        </span>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Ürün Sayısı</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">İşlemler</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                <tr v-for="order in orders.data" :key="order.id" class="hover:bg-gray-50">
                                    <td class="whitespace-nowrap px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">{{ order.order_number }}</div>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">{{ order.customer_name }}</div>
                                        <div class="text-sm text-gray-500">{{ order.customer_email }}</div>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4">
                                        <div class="text-sm text-gray-900">{{ formatDate(order.created_at) }}</div>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">{{ formatCurrency(order.total_amount) }}</div>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4">
                                        <span :class="getStatusColor(order.status)" class="inline-flex rounded-full px-2 py-1 text-xs font-semibold">
                                            {{ statuses[order.status] }}
                                        </span>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4">
                                        <div class="text-sm text-gray-900">{{ order.order_items.length }} ürün</div>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                                        <Link :href="route('admin.orders.show', order.id)" class="mr-3 text-indigo-600 hover:text-indigo-900"> Görüntüle </Link>
                                        <a :href="route('admin.orders.pdf', order.id)" target="_blank" class="mr-3 text-green-600 hover:text-green-900" title="PDF İndir">
                                            <svg class="inline h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                                />
                                            </svg>
                                        </a>
                                        <a :href="route('admin.orders.print', order.id)" target="_blank" class="text-blue-600 hover:text-blue-900" title="Yazdır">
                                            <svg class="inline h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"
                                                />
                                            </svg>
                                        </a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Empty State -->
                    <div v-if="orders.data.length === 0" class="py-12 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">Sipariş bulunamadı</h3>
                        <p class="mt-1 text-sm text-gray-500">Arama kriterlerinize uygun sipariş bulunamadı.</p>
                    </div>

                    <!-- Pagination -->
                    <div v-if="orders.data.length > 0" class="border-t border-gray-200 px-6 py-4">
                        <Pagination :paginator="orders" />
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
