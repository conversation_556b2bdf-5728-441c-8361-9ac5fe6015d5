<script setup>
import DangerButton from '@/components/DangerButton.vue';
import InputError from '@/components/InputError.vue';
import InputLabel from '@/components/InputLabel.vue';
import PrimaryButton from '@/components/PrimaryButton.vue';
import SecondaryButton from '@/components/SecondaryButton.vue';
import TextArea from '@/components/TextArea.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps({
    order: Object,
    statuses: Object,
});

const showStatusModal = ref(false);
const showNotesModal = ref(false);
const showDeleteModal = ref(false);

// Status update form
const statusForm = useForm({
    status: props.order.status,
});

// Notes update form
const notesForm = useForm({
    admin_notes: props.order.admin_notes || '',
});

// Currency formatter
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY',
        minimumFractionDigits: 2,
    }).format(amount);
};

// Date formatter
const formatDate = (date) => {
    return new Date(date).toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

// Status badge colors
const getStatusColor = (status) => {
    const colors = {
        pending: 'bg-yellow-100 text-yellow-800',
        processing: 'bg-blue-100 text-blue-800',
        shipped: 'bg-purple-100 text-purple-800',
        delivered: 'bg-green-100 text-green-800',
        completed: 'bg-green-100 text-green-800',
        cancelled: 'bg-red-100 text-red-800',
        refunded: 'bg-gray-100 text-gray-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
};

// Update status
const updateStatus = () => {
    statusForm.patch(route('admin.orders.update-status', props.order.id), {
        onSuccess: () => {
            showStatusModal.value = false;
        },
    });
};

// Update notes
const updateNotes = () => {
    notesForm.patch(route('admin.orders.update-notes', props.order.id), {
        onSuccess: () => {
            showNotesModal.value = false;
        },
    });
};

// Delete order
const deleteOrder = () => {
    if (confirm('Bu siparişi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.')) {
        useForm().delete(route('admin.orders.destroy', props.order.id));
    }
};

// Print order
const printOrder = () => {
    window.open(route('admin.orders.print', props.order.id), '_blank');
};

// Download PDF
const downloadPDF = () => {
    window.open(route('admin.orders.pdf', props.order.id), '_blank');
};
</script>

<template>
    <Head :title="`Sipariş ${order.order_number}`" />

    <AdminLayout>
        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Page Header -->
                <div class="mb-8 flex items-start justify-between">
                    <div>
                        <div class="flex items-center space-x-3">
                            <Link :href="route('admin.orders.index')" class="text-gray-500 hover:text-gray-700">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                </svg>
                            </Link>
                            <h1 class="text-3xl font-bold text-gray-900">Sipariş {{ order.order_number }}</h1>
                            <span :class="getStatusColor(order.status)" class="inline-flex rounded-full px-3 py-1 text-sm font-semibold">
                                {{ statuses[order.status] }}
                            </span>
                        </div>
                        <p class="mt-2 text-gray-600">{{ formatDate(order.created_at) }} tarihinde oluşturuldu</p>
                    </div>

                    <div class="flex space-x-3">
                        <SecondaryButton @click="printOrder">
                            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"
                                />
                            </svg>
                            Yazdır
                        </SecondaryButton>
                        <SecondaryButton @click="downloadPDF">
                            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            PDF İndir
                        </SecondaryButton>
                        <PrimaryButton @click="showStatusModal = true"> Durumu Güncelle </PrimaryButton>
                        <SecondaryButton @click="showNotesModal = true"> Not Ekle </SecondaryButton>
                        <DangerButton v-if="order.status === 'cancelled'" @click="deleteOrder"> Sil </DangerButton>
                    </div>
                </div>

                <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
                    <!-- Order Details -->
                    <div class="space-y-8 lg:col-span-2">
                        <!-- Customer Information -->
                        <div class="rounded-lg bg-white shadow">
                            <div class="border-b border-gray-200 px-6 py-4">
                                <h3 class="text-lg font-medium text-gray-900">Müşteri Bilgileri</h3>
                            </div>
                            <div class="p-6">
                                <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Ad Soyad</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ order.customer_name }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">E-posta</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ order.customer_email }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Telefon</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ order.customer_phone }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Ödeme Yöntemi</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            {{ order.payment_method === 'bank_transfer' ? 'Banka Havalesi' : order.payment_method }}
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>

                        <!-- Addresses -->
                        <div class="rounded-lg bg-white shadow">
                            <div class="border-b border-gray-200 px-6 py-4">
                                <h3 class="text-lg font-medium text-gray-900">Adres Bilgileri</h3>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                    <!-- Billing Address -->
                                    <div>
                                        <h4 class="mb-3 text-sm font-medium text-gray-900">Fatura Adresi</h4>
                                        <div class="space-y-1 text-sm text-gray-600">
                                            <div>{{ order.billing_address.street }}</div>
                                            <div>{{ order.billing_address.city }}, {{ order.billing_address.postal_code }}</div>
                                            <div>{{ order.billing_address.country }}</div>
                                        </div>
                                    </div>

                                    <!-- Shipping Address -->
                                    <div>
                                        <h4 class="mb-3 text-sm font-medium text-gray-900">Teslimat Adresi</h4>
                                        <div class="space-y-1 text-sm text-gray-600">
                                            <div>{{ order.shipping_address.street }}</div>
                                            <div>{{ order.shipping_address.city }}, {{ order.shipping_address.postal_code }}</div>
                                            <div>{{ order.shipping_address.country }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items -->
                        <div class="rounded-lg bg-white shadow">
                            <div class="border-b border-gray-200 px-6 py-4">
                                <h3 class="text-lg font-medium text-gray-900">Sipariş Ürünleri</h3>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Ürün</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Birim Fiyat</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Adet</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Toplam</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 bg-white">
                                        <tr v-for="item in order.order_items" :key="item.id">
                                            <td class="whitespace-nowrap px-6 py-4">
                                                <div class="text-sm font-medium text-gray-900">{{ item.product_name }}</div>
                                                <div v-if="item.product_sku" class="text-sm text-gray-500">SKU: {{ item.product_sku }}</div>
                                            </td>
                                            <td class="whitespace-nowrap px-6 py-4">
                                                <div class="text-sm text-gray-900">{{ formatCurrency(item.unit_price) }}</div>
                                            </td>
                                            <td class="whitespace-nowrap px-6 py-4">
                                                <div class="text-sm text-gray-900">{{ item.quantity }}</div>
                                            </td>
                                            <td class="whitespace-nowrap px-6 py-4">
                                                <div class="text-sm font-medium text-gray-900">{{ formatCurrency(item.total_price) }}</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div v-if="order.notes || order.admin_notes" class="rounded-lg bg-white shadow">
                            <div class="border-b border-gray-200 px-6 py-4">
                                <h3 class="text-lg font-medium text-gray-900">Notlar</h3>
                            </div>
                            <div class="space-y-4 p-6">
                                <div v-if="order.notes">
                                    <h4 class="text-sm font-medium text-gray-900">Müşteri Notu</h4>
                                    <p class="mt-1 text-sm text-gray-600">{{ order.notes }}</p>
                                </div>
                                <div v-if="order.admin_notes">
                                    <h4 class="text-sm font-medium text-gray-900">Admin Notu</h4>
                                    <p class="mt-1 text-sm text-gray-600">{{ order.admin_notes }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="lg:col-span-1">
                        <div class="sticky top-8 rounded-lg bg-white shadow">
                            <div class="border-b border-gray-200 px-6 py-4">
                                <h3 class="text-lg font-medium text-gray-900">Sipariş Özeti</h3>
                            </div>
                            <div class="p-6">
                                <dl class="space-y-4">
                                    <div class="flex justify-between">
                                        <dt class="text-sm text-gray-600">Ara Toplam</dt>
                                        <dd class="text-sm font-medium text-gray-900">{{ formatCurrency(order.subtotal) }}</dd>
                                    </div>
                                    <div class="flex justify-between">
                                        <dt class="text-sm text-gray-600">KDV</dt>
                                        <dd class="text-sm font-medium text-gray-900">{{ formatCurrency(order.tax_amount) }}</dd>
                                    </div>
                                    <div class="border-t border-gray-200 pt-4">
                                        <div class="flex justify-between">
                                            <dt class="text-base font-medium text-gray-900">Toplam</dt>
                                            <dd class="text-base font-medium text-gray-900">{{ formatCurrency(order.total_amount) }}</dd>
                                        </div>
                                    </div>
                                </dl>

                                <div class="mt-6 border-t border-gray-200 pt-6">
                                    <dl class="space-y-3">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Sipariş Numarası</dt>
                                            <dd class="mt-1 font-mono text-sm text-gray-900">{{ order.order_number }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Sipariş Tarihi</dt>
                                            <dd class="mt-1 text-sm text-gray-900">{{ formatDate(order.created_at) }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Durum</dt>
                                            <dd class="mt-1">
                                                <span :class="getStatusColor(order.status)" class="inline-flex rounded-full px-2 py-1 text-xs font-semibold">
                                                    {{ statuses[order.status] }}
                                                </span>
                                            </dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Ürün Sayısı</dt>
                                            <dd class="mt-1 text-sm text-gray-900">{{ order.order_items.length }} ürün</dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Update Modal -->
                <div v-if="showStatusModal" class="fixed inset-0 z-50 h-full w-full overflow-y-auto bg-gray-600 bg-opacity-50">
                    <div class="relative top-20 mx-auto w-96 rounded-md border bg-white p-5 shadow-lg">
                        <div class="mt-3">
                            <h3 class="mb-4 text-lg font-medium text-gray-900">Sipariş Durumunu Güncelle</h3>
                            <form @submit.prevent="updateStatus">
                                <div class="mb-4">
                                    <InputLabel for="status" value="Durum" />
                                    <select id="status" v-model="statusForm.status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option v-for="(label, key) in statuses" :key="key" :value="key">{{ label }}</option>
                                    </select>
                                    <InputError :message="statusForm.errors.status" class="mt-2" />
                                </div>
                                <div class="flex justify-end space-x-3">
                                    <SecondaryButton @click="showStatusModal = false">İptal</SecondaryButton>
                                    <PrimaryButton type="submit" :disabled="statusForm.processing">
                                        {{ statusForm.processing ? 'Güncelleniyor...' : 'Güncelle' }}
                                    </PrimaryButton>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Notes Update Modal -->
                <div v-if="showNotesModal" class="fixed inset-0 z-50 h-full w-full overflow-y-auto bg-gray-600 bg-opacity-50">
                    <div class="relative top-20 mx-auto w-96 rounded-md border bg-white p-5 shadow-lg">
                        <div class="mt-3">
                            <h3 class="mb-4 text-lg font-medium text-gray-900">Admin Notu Ekle/Düzenle</h3>
                            <form @submit.prevent="updateNotes">
                                <div class="mb-4">
                                    <InputLabel for="admin_notes" value="Admin Notu" />
                                    <TextArea id="admin_notes" v-model="notesForm.admin_notes" rows="4" class="mt-1 block w-full" placeholder="Sipariş hakkında notlarınızı buraya yazın..." />
                                    <InputError :message="notesForm.errors.admin_notes" class="mt-2" />
                                </div>
                                <div class="flex justify-end space-x-3">
                                    <SecondaryButton @click="showNotesModal = false">İptal</SecondaryButton>
                                    <PrimaryButton type="submit" :disabled="notesForm.processing">
                                        {{ notesForm.processing ? 'Kaydediliyor...' : 'Kaydet' }}
                                    </PrimaryButton>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<style>
@media print {
    .no-print {
        display: none !important;
    }
}
</style>
