<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { computed } from 'vue';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    contact: Object,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const confirmDelete = (id) => {
    if (confirm(locale.value === 'tr' ? 'Bu iletişim mesajını silmek istediğinizden emin misiniz?' : 'Are you sure you want to delete this contact message?')) {
        window.location.href = route(`admin.users.destroy`, id);
    }
};

const markAsRead = (id) => {
    document.getElementById(`mark-read-form-${id}`).submit();
};
</script>

<template>
    <Head :title="locale === 'tr' ? '<PERSON><PERSON><PERSON><PERSON><PERSON>' : 'Contact Message Details'" />

    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ locale === 'tr' ? 'İletişim Mesajı Detayı' : 'Contact Message Details' }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex justify-end mb-6">
                            <button 
                                v-if="!contact.is_read"
                                @click="markAsRead(contact.id)" 
                                class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-500 active:bg-green-700 focus:outline-none focus:border-green-700 focus:ring focus:ring-green-300 disabled:opacity-25  mr-2"
                            >
                                {{ locale === 'tr' ? 'Okundu İşaretle' : 'Mark as Read' }}
                            </button>
                            <button 
                                @click="confirmDelete(contact.id)" 
                                class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:border-red-700 focus:ring focus:ring-red-300 disabled:opacity-25 "
                            >
                                {{ locale === 'tr' ? 'Sil' : 'Delete' }}
                            </button>
                            <form 
                                :id="`delete-form-${contact.id}`" 
                                :action="route('admin.contacts.destroy', contact.id)" 
                                method="POST" 
                                class="hidden"
                            >
                                <input type="hidden" name="_method" value="DELETE">
                                <input type="hidden" name="_token" :value="$page.props.csrf_token">
                            </form>
                            <form 
                                v-if="!contact.is_read"
                                :id="`mark-read-form-${contact.id}`" 
                                :action="route('admin.contacts.mark-as-read', contact.id)" 
                                method="POST" 
                                class="hidden"
                            >
                                <input type="hidden" name="_method" value="PATCH">
                                <input type="hidden" name="_token" :value="$page.props.csrf_token">
                            </form>
                        </div>

                        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                                <div>
                                    <h3 class="text-lg leading-6 font-medium text-gray-900">
                                        {{ locale === 'tr' ? 'İletişim Mesajı Bilgileri' : 'Contact Message Information' }}
                                    </h3>
                                    <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                        {{ locale === 'tr' ? 'Mesaj detayları ve gönderen bilgileri' : 'Message details and sender information' }}
                                    </p>
                                </div>
                                <span 
                                    :class="[
                                        'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                        contact.is_read ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                                    ]"
                                >
                                    {{ contact.is_read ? (locale === 'tr' ? 'Okundu' : 'Read') : (locale === 'tr' ? 'Okunmadı' : 'Unread') }}
                                </span>
                            </div>
                            <div class="border-t border-gray-200">
                                <dl>
                                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500">
                                            {{ locale === 'tr' ? 'Ad Soyad' : 'Full name' }}
                                        </dt>
                                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            {{ contact.name }}
                                        </dd>
                                    </div>
                                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500">
                                            {{ locale === 'tr' ? 'E-posta adresi' : 'Email address' }}
                                        </dt>
                                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            <a :href="`mailto:${contact.email}`" class="text-indigo-600 hover:text-indigo-900">
                                                {{ contact.email }}
                                            </a>
                                        </dd>
                                    </div>
                                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500">
                                            {{ locale === 'tr' ? 'Telefon' : 'Phone' }}
                                        </dt>
                                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            <a v-if="contact.phone" :href="`tel:${contact.phone}`" class="text-indigo-600 hover:text-indigo-900">
                                                {{ contact.phone }}
                                            </a>
                                            <span v-else>-</span>
                                        </dd>
                                    </div>
                                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500">
                                            {{ locale === 'tr' ? 'Konu' : 'Subject' }}
                                        </dt>
                                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            {{ contact.subject }}
                                        </dd>
                                    </div>
                                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500">
                                            {{ locale === 'tr' ? 'Tarih' : 'Date' }}
                                        </dt>
                                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            {{ new Date(contact.created_at).toLocaleString() }}
                                        </dd>
                                    </div>
                                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500">
                                            {{ locale === 'tr' ? 'Mesaj' : 'Message' }}
                                        </dt>
                                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                            <div class="prose max-w-none">
                                                <p class="whitespace-pre-line">{{ contact.message }}</p>
                                            </div>
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>

                        <div class="mt-6">
                            <Link 
                                :href="route('admin.contacts.index')" 
                                class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 "
                            >
                                {{ locale === 'tr' ? 'Tüm Mesajlara Dön' : 'Back to All Messages' }}
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
