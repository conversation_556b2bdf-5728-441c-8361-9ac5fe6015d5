<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { ref, computed } from 'vue';
import { EyeIcon, PencilSquareIcon, TrashIcon, CloudArrowDownIcon } from '@heroicons/vue/24/outline';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    contacts: Array,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
const search = ref('');

const filteredContacts = computed(() => {
    if (!search.value) return props.contacts;
    
    const searchTerm = search.value.toLowerCase();
    return props.contacts.filter(contact => 
        contact.name.toLowerCase().includes(searchTerm) || 
        contact.email.toLowerCase().includes(searchTerm) || 
        contact.subject.toLowerCase().includes(searchTerm) || 
        contact.message.toLowerCase().includes(searchTerm)
    );
});

const confirmDelete = (id) => {
    if (confirm(locale.value === 'tr' ? 'Bu iletişim mesajını silmek istediğinizden emin misiniz?' : 'Are you sure you want to delete this contact message?')) {
        window.location.href = route(`admin.users.destroy`, id);
    }
};

const markAsRead = (id) => {
    document.getElementById(`mark-read-form-${id}`).submit();
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'İletişim Mesajları' : 'Contact Messages'" />

    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ locale === 'tr' ? 'İletişim Mesajları' : 'Contact Messages' }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex justify-between items-center mb-6">
                            <div class="flex-1">
                                <input 
                                    type="text" 
                                    v-model="search" 
                                    :placeholder="locale === 'tr' ? 'Mesajlarda ara...' : 'Search messages...'" 
                                    class="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm w-full sm:w-1/3"
                                />
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            ID
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ locale === 'tr' ? 'Ad Soyad' : 'Name' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ locale === 'tr' ? 'E-posta' : 'Email' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ locale === 'tr' ? 'Konu' : 'Subject' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ locale === 'tr' ? 'Durum' : 'Status' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ locale === 'tr' ? 'Tarih' : 'Date' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ locale === 'tr' ? 'İşlemler' : 'Actions' }}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="contact in filteredContacts" :key="contact.id" :class="{ 'bg-gray-50': !contact.is_read }">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ contact.id }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900" :class="{ 'font-bold': !contact.is_read }">
                                                {{ contact.name }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500" :class="{ 'font-semibold': !contact.is_read }">
                                                {{ contact.email }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900" :class="{ 'font-semibold': !contact.is_read }">
                                                {{ contact.subject }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span 
                                                :class="[
                                                    'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                                    contact.is_read ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                                                ]"
                                            >
                                                {{ contact.is_read ? (locale === 'tr' ? 'Okundu' : 'Read') : (locale === 'tr' ? 'Okunmadı' : 'Unread') }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ new Date(contact.created_at).toLocaleDateString() }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <Link 
                                                :href="route('admin.contacts.show', contact.id)" 
                                                class="text-indigo-600 hover:text-indigo-900 mr-2"
                                            >
                                                {{ locale === 'tr' ? 'Görüntüle' : 'View' }}
                                            </Link>
                                            <a 
                                                v-if="!contact.is_read"
                                                href="#" 
                                                class="text-green-600 hover:text-green-900 mr-2"
                                                @click.prevent="markAsRead(contact.id)"
                                            >
                                                {{ locale === 'tr' ? 'Okundu İşaretle' : 'Mark as Read' }}
                                            </a>
                                            <a 
                                                href="#" 
                                                class="text-red-600 hover:text-red-900"
                                                @click.prevent="confirmDelete(contact.id)"
                                            >
                                                {{ locale === 'tr' ? 'Sil' : 'Delete' }}
                                            </a>
                                            <form 
                                                :id="`delete-form-${contact.id}`" 
                                                :action="route('admin.contacts.destroy', contact.id)" 
                                                method="POST" 
                                                class="hidden"
                                            >
                                                <input type="hidden" name="_method" value="DELETE">
                                                <input type="hidden" name="_token" :value="$page.props.csrf_token">
                                            </form>
                                            <form 
                                                v-if="!contact.is_read"
                                                :id="`mark-read-form-${contact.id}`" 
                                                :action="route('admin.contacts.mark-as-read', contact.id)" 
                                                method="POST" 
                                                class="hidden"
                                            >
                                                <input type="hidden" name="_method" value="PATCH">
                                                <input type="hidden" name="_token" :value="$page.props.csrf_token">
                                            </form>
                                        </td>
                                    </tr>
                                    <tr v-if="filteredContacts.length === 0">
                                        <td colspan="7" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                            {{ locale === 'tr' ? 'İletişim mesajı bulunamadı' : 'No contact messages found' }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
