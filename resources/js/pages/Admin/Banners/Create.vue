<script setup>
import Breadcrumbs from '@/components/Breadcrumbs.vue';
import Checkbox from '@/components/Checkbox.vue';
import FileManager from '@/components/FileManager.vue';
import InputError from '@/components/InputError.vue';
import InputLabel from '@/components/InputLabel.vue';
import PrimaryButton from '@/components/PrimaryButton.vue';
import SecondaryButton from '@/components/SecondaryButton.vue';
import TextInput from '@/components/TextInput.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { useLocaleStore } from '@/Stores/locale';
import { Head, Link, useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { computed, ref } from 'vue';
const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const fileManagerVisible = ref(false);
const currentField = ref(null);
const tempImagePreview = ref(null);
const uploadedImage = ref(null);

const bannerTypes = [
    { id: 'main', name: { en: 'Main Banner', tr: 'Ana Banner' } },
    { id: 'banner1', name: { en: 'Banner 1', tr: 'Banner 1' } },
    { id: 'banner2', name: { en: 'Banner 2', tr: 'Banner 2' } },
    { id: 'banner3', name: { en: 'Banner 3', tr: 'Banner 3' } },
    { id: 'banner4', name: { en: 'Banner 4', tr: 'Banner 4' } },
];

const form = useForm({
    title: { tr: '', en: '' },
    image: '',
    link: '',
    button_text: { tr: '', en: '' },
    type: 'main',
    order: 0,
    is_active: true,
    main_grup: '',
});

const openFileManager = (field) => {
    currentField.value = field;
    fileManagerVisible.value = true;
    console.log('openFileManager tetiklendi, fileManagerVisible:', fileManagerVisible.value);
};
const selectFile = (file) => {
    if (!file || !file.file_path) {
        console.error('Dosya seçilmedi veya dosya yolu yok.');
        return;
    }

    console.log('Seçilen dosya:', file);

    if (currentField.value === 'banner') {
        form.image = `/storage/${file.file_path}`;
    }

    fileManagerVisible.value = false;
};

const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            tempImagePreview.value = e.target.result;
            uploadedImage.value = file;

            // Create a FormData object to send the file to the server
            const formData = new FormData();
            formData.append('file', file);
            formData.append('folder', 'banners');

            // Send the file to the server
            axios
                .post(route('admin.files.store'), formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                })
                .then((response) => {
                    // Update the form with the uploaded file path
                    if (response.data && response.data.file && response.data.file.file_path) {
                        form.image = `/storage/${response.data.file.file_path}`;
                    } else {
                        console.error('Invalid response format:', response.data);
                    }
                })
                .catch((error) => {
                    console.error('Upload error:', error);
                });
        };
        reader.readAsDataURL(file);
    }
};

const clearImage = () => {
    form.image = '';
    tempImagePreview.value = null;
    uploadedImage.value = null;
};

const submit = () => {
    form.post(route('admin.banners.store'), {
        onSuccess: () => {
            form.reset();
        },
    });
};

const activeTab = ref('tr');
</script>

<template>
    <Head :title="locale === 'en' ? 'Create Banner' : 'Banner Oluştur'" />

    <AdminLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">
                {{ locale === 'en' ? 'Create Banner' : 'Banner Oluştur' }}
            </h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <Breadcrumbs
                    :items="[
                        { title: { en: 'Dashboard', tr: 'Panel' }, url: route('admin.dashboard') },
                        { title: { en: 'Banners', tr: 'Banner\'lar' }, url: route('admin.banners.index') },
                        { title: { en: 'Create', tr: 'Oluştur' }, url: '#' },
                    ]"
                />

                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="border-b border-gray-200 bg-white p-6">
                        <form @submit.prevent="submit">
                            <!-- Save/Cancel Buttons at Top -->
                            <div class="mb-4 flex justify-end">
                                <Link
                                    :href="route('admin.banners.index')"
                                    class="mr-2 inline-flex items-center rounded-md border border-transparent bg-gray-300 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-gray-800 hover:bg-gray-400 focus:border-gray-500 focus:outline-none focus:ring focus:ring-gray-300 active:bg-gray-500 disabled:opacity-25"
                                >
                                    {{ locale === 'en' ? 'Cancel' : 'İptal' }}
                                </Link>
                                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                                    {{ locale === 'en' ? 'Create' : 'Oluştur' }}
                                </PrimaryButton>
                            </div>

                            <!-- Language Tabs -->
                            <div class="mb-4 border-b border-gray-200">
                                <nav class="-mb-px flex">
                                    <button
                                        type="button"
                                        @click="activeTab = 'tr'"
                                        :class="activeTab === 'tr' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                                        class="w-1/2 border-b-2 px-1 py-4 text-center text-sm font-medium"
                                    >
                                        Türkçe
                                    </button>
                                    <button
                                        type="button"
                                        @click="activeTab = 'en'"
                                        :class="activeTab === 'en' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                                        class="w-1/2 border-b-2 px-1 py-4 text-center text-sm font-medium"
                                    >
                                        English
                                    </button>
                                </nav>
                            </div>

                            <!-- Language Content Tabs -->
                            <div v-if="activeTab === 'tr'">
                                <!-- Turkish Title -->
                                <div class="mb-4">
                                    <InputLabel for="title_tr" value="Banner Başlığı (Türkçe)" />
                                    <TextInput id="title_tr" v-model="form.title.tr" type="text" class="mt-1 block w-full" required autofocus />
                                    <InputError :message="form.errors['title.tr']" class="mt-2" />
                                </div>

                                <!-- Turkish Button Text -->
                                <div class="mb-4">
                                    <InputLabel for="button_text_tr" value="Buton Metni (Türkçe)" />
                                    <TextInput id="button_text_tr" v-model="form.button_text.tr" type="text" class="mt-1 block w-full" />
                                    <InputError :message="form.errors['button_text.tr']" class="mt-2" />
                                </div>
                            </div>

                            <div v-else-if="activeTab === 'en'">
                                <!-- English Title -->
                                <div class="mb-4">
                                    <InputLabel for="title_en" value="Banner Title (English)" />
                                    <TextInput id="title_en" v-model="form.title.en" type="text" class="mt-1 block w-full" required />
                                    <InputError :message="form.errors['title.en']" class="mt-2" />
                                </div>

                                <!-- English Button Text -->
                                <div class="mb-4">
                                    <InputLabel for="button_text_en" value="Button Text (English)" />
                                    <TextInput id="button_text_en" v-model="form.button_text.en" type="text" class="mt-1 block w-full" />
                                    <InputError :message="form.errors['button_text.en']" class="mt-2" />
                                </div>
                            </div>

                            <!-- Language Independent Fields -->

                            <div class="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
                                <!-- Banner Image -->
                                <div>
                                    <InputLabel for="image" :value="locale === 'en' ? 'Banner Image' : 'Banner Resmi'" />

                                    <!-- Direct Upload Option -->
                                    <div class="mt-1 flex items-center">
                                        <label class="block w-full">
                                            <span class="sr-only">Choose file</span>
                                            <input
                                                type="file"
                                                accept="image/*"
                                                class="block w-full text-sm text-gray-500 file:mr-4 file:rounded-md file:border-0 file:bg-indigo-50 file:px-4 file:py-2 file:text-sm file:font-semibold file:text-indigo-700 hover:file:bg-indigo-100"
                                                @change="handleImageUpload"
                                            />
                                        </label>
                                    </div>

                                    <!-- OR divider -->
                                    <div class="relative my-4 flex items-center">
                                        <div class="flex-grow border-t border-gray-300"></div>
                                        <span class="mx-4 flex-shrink text-gray-600">{{ locale === 'en' ? 'or' : 'veya' }}</span>
                                        <div class="flex-grow border-t border-gray-300"></div>
                                    </div>

                                    <!-- File Manager Option -->
                                    <div class="flex items-center">
                                        <TextInput id="image" v-model="form.image" type="text" class="block w-full" readonly />
                                        <PrimaryButton type="button" @click="openFileManager('banner')" class="ml-2 whitespace-nowrap">
                                            {{ locale === 'en' ? 'Select Image' : 'Resim Seç' }}
                                        </PrimaryButton>
                                    </div>

                                    <InputError :message="form.errors.image" class="mt-2" />

                                    <!-- Image Preview -->
                                    <div v-if="form.image || tempImagePreview" class="mt-4">
                                        <div class="mb-2 flex items-center justify-between">
                                            <h4 class="text-sm font-medium text-gray-700">{{ locale === 'en' ? 'Image Preview' : 'Resim Önizleme' }}</h4>
                                            <button type="button" @click="clearImage" class="text-xs text-red-600 hover:text-red-900">
                                                {{ locale === 'en' ? 'Clear' : 'Temizle' }}
                                            </button>
                                        </div>
                                        <img :src="tempImagePreview || form.image" alt="Banner Preview" class="h-32 rounded object-cover" />
                                    </div>
                                </div>

                                <!-- Banner Type -->
                                <div>
                                    <InputLabel for="type" :value="locale === 'en' ? 'Banner Type' : 'Banner Tipi'" />
                                    <select id="type" v-model="form.type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        <option v-for="type in bannerTypes" :key="type.id" :value="type.id">
                                            {{ type.name[locale] }}
                                        </option>
                                    </select>
                                    <InputError :message="form.errors.type" class="mt-2" />
                                </div>
                            </div>

                            <div class="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
                                <!-- Link -->
                                <div>
                                    <InputLabel for="link" :value="locale === 'en' ? 'Link URL' : 'Bağlantı URL'" />
                                    <TextInput id="link" v-model="form.link" type="text" class="mt-1 block w-full" />
                                    <InputError :message="form.errors.link" class="mt-2" />
                                </div>

                                <!-- Order -->
                                <div>
                                    <InputLabel for="order" :value="locale === 'en' ? 'Order' : 'Sıralama'" />
                                    <TextInput id="order" v-model="form.order" type="number" class="mt-1 block w-full" />
                                    <InputError :message="form.errors.order" class="mt-2" />
                                </div>
                            </div>

                            <div class="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
                                <!-- Group -->
                                <div>
                                    <InputLabel for="main_grup" :value="locale === 'en' ? 'Group' : 'Grup'" />
                                    <TextInput id="main_grup" v-model="form.main_grup" type="text" class="mt-1 block w-full" required />
                                    <InputError :message="form.errors.main_grup" class="mt-2" />
                                </div>

                                <!-- Status -->
                                <div class="mt-6 flex items-center">
                                    <Checkbox id="is_active" v-model:checked="form.is_active" />
                                    <InputLabel for="is_active" :value="locale === 'en' ? 'Active' : 'Aktif'" class="ml-2" />
                                </div>
                            </div>

                            <div class="mt-6 flex items-center justify-end">
                                <Link :href="route('admin.banners.index')" class="mr-3">
                                    <SecondaryButton type="button">
                                        {{ locale === 'en' ? 'Cancel' : 'İptal' }}
                                    </SecondaryButton>
                                </Link>
                                <PrimaryButton class="ml-3" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                                    {{ locale === 'en' ? 'Create' : 'Oluştur' }}
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Manager Modal -->
        <FileManager :show="fileManagerVisible" @close="fileManagerVisible = false" @select="selectFile" />
    </AdminLayout>
</template>
