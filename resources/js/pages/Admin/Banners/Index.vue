<script setup>
import Breadcrumbs from '@/components/Breadcrumbs.vue';
import DangerButton from '@/components/DangerButton.vue';
import Modal from '@/components/Modal.vue';
import PrimaryButton from '@/components/PrimaryButton.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { useLocaleStore } from '@/Stores/locale';
import { Head, Link, router } from '@inertiajs/vue3';
import { computed, ref } from 'vue';

const props = defineProps({
    banners: Array,
    currentType: String,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

// Filter options
const bannerTypes = [
    { id: 'all', name: { en: 'All Banners', tr: "Tüm Banner'lar" } },
    { id: 'main', name: { en: 'Main', tr: 'Ana' } },
    { id: 'banner1', name: { en: 'Banner 1', tr: 'Banner 1' } },
    { id: 'banner2', name: { en: 'Banner 2', tr: 'Banner 2' } },
    { id: 'banner3', name: { en: 'Banner 3', tr: 'Banner 3' } },
    { id: 'banner4', name: { en: 'Banner 4', tr: 'Banner 4' } },
];

// Delete confirmation
const deleteModal = ref(false);
const bannerToDelete = ref(null);

const confirmDelete = (id) => {
    if (confirm(locale.value === 'tr' ? "Bu banner'ı silmek istediğinizden emin misiniz?" : 'Are you sure you want to delete this banner?')) {
        router.delete(route('admin.banners.destroy', id));
    }
};

const showDeleteModal = (banner) => {
    bannerToDelete.value = banner;
    deleteModal.value = true;
};

const deleteBanner = () => {
    if (bannerToDelete.value) {
        router.delete(route('admin.banners.destroy', bannerToDelete.value.id));
        deleteModal.value = false;
        bannerToDelete.value = null;
    }
};

const cancelDelete = () => {
    deleteModal.value = false;
    bannerToDelete.value = null;
};
</script>

<template>
    <Head :title="locale === 'en' ? 'Banner Management' : 'Banner Yönetimi'" />

    <AdminLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">
                {{ locale === 'en' ? 'Banner Management' : 'Banner Yönetimi' }}
            </h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <Breadcrumbs
                    :items="[
                        { title: { en: 'Dashboard', tr: 'Panel' }, url: route('admin.dashboard') },
                        { title: { en: 'Banners', tr: 'Banner\'lar' }, url: route('admin.banners.index') },
                    ]"
                />

                <div class="mb-6 overflow-hidden bg-white p-6 shadow-sm sm:rounded-lg">
                    <div class="mb-4">
                        <h3 class="mb-2 text-lg font-medium">{{ locale === 'en' ? 'Filter by Type' : 'Tipe Göre Filtrele' }}</h3>
                        <div class="flex flex-wrap gap-2">
                            <Link
                                v-for="type in bannerTypes"
                                :key="type.id"
                                :href="route('admin.banners.index', { type: type.id })"
                                :class="['rounded-md px-3 py-1 text-sm', currentType === type.id ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300']"
                            >
                                {{ type.name[locale] }}
                            </Link>
                        </div>
                    </div>
                </div>

                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="border-b border-gray-200 bg-white p-6">
                        <!-- Header with Create Button -->
                        <div class="mb-6 flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900">
                                {{ locale === 'en' ? 'All Banners' : "Tüm Banner'lar" }}
                            </h3>
                            <Link
                                :href="route('admin.banners.create')"
                                class="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-blue-700 focus:border-blue-900 focus:outline-none focus:ring focus:ring-blue-300 active:bg-blue-900 disabled:opacity-25"
                            >
                                {{ locale === 'en' ? 'New Banner' : 'Yeni Banner' }}
                            </Link>
                        </div>
                        <div v-if="banners.length > 0" class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                                            {{ locale === 'en' ? 'Image' : 'Resim' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                                            {{ locale === 'en' ? 'Title' : 'Başlık' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                                            {{ locale === 'en' ? 'Type' : 'Tip' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                                            {{ locale === 'en' ? 'Order' : 'Sıra' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                                            {{ locale === 'en' ? 'Status' : 'Durum' }}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">
                                            {{ locale === 'en' ? 'Actions' : 'İşlemler' }}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 bg-white">
                                    <tr v-for="banner in banners" :key="banner.id">
                                        <td class="whitespace-nowrap px-6 py-4">
                                            <img :src="banner.image" alt="Banner" class="h-16 w-20 rounded object-cover" />
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ banner.title[locale] }}
                                            </div>
                                            <div v-if="banner.link" class="max-w-xs truncate text-xs text-gray-500">
                                                {{ banner.link }}
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4">
                                            <span class="inline-flex rounded-full bg-blue-100 px-2 text-xs font-semibold leading-5 text-blue-800">
                                                {{ banner.type }}
                                            </span>
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                                            {{ banner.order }}
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4">
                                            <span :class="`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${banner.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`">
                                                {{ banner.is_active ? (locale === 'en' ? 'Active' : 'Aktif') : locale === 'en' ? 'Inactive' : 'Pasif' }}
                                            </span>
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                                            <Link :href="route('admin.banners.edit', banner.id)" class="mr-3 text-indigo-600 hover:text-indigo-900">
                                                {{ locale === 'en' ? 'Edit' : 'Düzenle' }}
                                            </Link>
                                            <button @click="confirmDelete(banner.id)" class="text-red-600 hover:text-red-900">
                                                {{ locale === 'en' ? 'Delete' : 'Sil' }}
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div v-else class="py-12 text-center text-gray-500">
                            {{ locale === 'en' ? 'No banners found.' : 'Banner bulunamadı.' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <Modal :show="deleteModal" @close="cancelDelete">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    {{ locale === 'en' ? 'Delete Banner' : "Banner'ı Sil" }}
                </h2>
                <p class="mt-1 text-sm text-gray-600">
                    {{ locale === 'en' ? 'Are you sure you want to delete this banner?' : "Bu banner'ı silmek istediğinizden emin misiniz?" }}
                    <span v-if="bannerToDelete" class="font-semibold">{{ bannerToDelete.title[locale] }}</span
                    >?
                </p>
                <div class="mt-6 flex justify-end space-x-3">
                    <PrimaryButton @click="cancelDelete">
                        {{ locale === 'en' ? 'Cancel' : 'İptal' }}
                    </PrimaryButton>
                    <DangerButton @click="deleteBanner">
                        {{ locale === 'en' ? 'Delete' : 'Sil' }}
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>
