<script setup>
import { useLocaleStore } from '@/Stores/locale';
import Checkbox from '@/components/Checkbox.vue';
import InputError from '@/components/InputError.vue';
import InputLabel from '@/components/InputLabel.vue';
import PrimaryButton from '@/components/PrimaryButton.vue';
import QuillEditor from '@/components/QuillEditor.vue';
import TextInput from '@/components/TextInput.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { computed, ref } from 'vue';

const props = defineProps({
    categories: Array,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const form = useForm({
    name: {
        tr: '',
        en: '',
    },
    description: {
        tr: '',
        en: '',
    },
    parent_id: null,
    is_active: true,
    order: 0,
    meta_description: {
        tr: '',
        en: '',
    },
    meta_keywords: {
        tr: '',
        en: '',
    },
    main_grup: 'default',
});

const submit = () => {
    form.post(route('admin.categories.store'));
};

const activeTab = ref('tr');
</script>

<template>
    <Head :title="locale === 'tr' ? 'Yeni Kategori' : 'New Category'" />

    <AdminLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">
                {{ locale === 'tr' ? 'Yeni Kategori' : 'New Category' }}
            </h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="border-b border-gray-200 bg-white p-6">
                        <form @submit.prevent="submit" class="space-y-6">
                            <!-- Kayıt Butonu En Üstte -->
                            <div class="mb-4 flex justify-end">
                                <Link
                                    :href="route('admin.categories.index')"
                                    class="mr-2 inline-flex items-center rounded-md border border-transparent bg-gray-300 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-gray-800 hover:bg-gray-400 focus:border-gray-500 focus:outline-none focus:ring focus:ring-gray-300 active:bg-gray-500 disabled:opacity-25"
                                >
                                    {{ locale === 'tr' ? 'İptal' : 'Cancel' }}
                                </Link>
                                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                                    {{ locale === 'tr' ? 'Kaydet' : 'Save' }}
                                </PrimaryButton>
                            </div>

                            <!-- Dil Tabları -->
                            <div class="mb-4 border-b border-gray-200">
                                <nav class="-mb-px flex">
                                    <button
                                        type="button"
                                        @click="activeTab = 'tr'"
                                        :class="activeTab === 'tr' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                                        class="w-1/2 border-b-2 px-1 py-4 text-center text-sm font-medium"
                                    >
                                        Türkçe
                                    </button>
                                    <button
                                        type="button"
                                        @click="activeTab = 'en'"
                                        :class="activeTab === 'en' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                                        class="w-1/2 border-b-2 px-1 py-4 text-center text-sm font-medium"
                                    >
                                        English
                                    </button>
                                </nav>
                            </div>

                            <!-- Dil Alanları (Tab İçeriği) -->
                            <div v-if="activeTab === 'tr'">
                                <!-- Türkçe Kategori Adı -->
                                <div class="mb-4">
                                    <InputLabel for="name_tr" value="Kategori Adı (Türkçe)" />
                                    <TextInput id="name_tr" type="text" class="mt-1 block w-full" v-model="form.name.tr" required autofocus />
                                    <InputError class="mt-2" :message="form.errors['name.tr']" />
                                </div>

                                <!-- Türkçe Açıklama -->
                                <div class="mb-4">
                                    <InputLabel for="description_tr" value="Açıklama (Türkçe)" />
                                    <QuillEditor id="description_tr" class="mt-1 block w-full" v-model="form.description.tr" />
                                    <InputError class="mt-2" :message="form.errors['description.tr']" />
                                </div>

                                <!-- Türkçe Meta Açıklama -->
                                <div class="mb-4">
                                    <InputLabel for="meta_description_tr" value="Meta Açıklama (Türkçe)" />
                                    <textarea id="meta_description_tr" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" v-model="form.meta_description.tr" rows="3" />
                                    <InputError class="mt-2" :message="form.errors['meta_description.tr']" />
                                </div>

                                <!-- Türkçe Meta Anahtar Kelimeler -->
                                <div class="mb-4">
                                    <InputLabel for="meta_keywords_tr" value="Meta Anahtar Kelimeler (Türkçe)" />
                                    <textarea id="meta_keywords_tr" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" v-model="form.meta_keywords.tr" rows="2" />
                                    <InputError class="mt-2" :message="form.errors['meta_keywords.tr']" />
                                </div>
                            </div>

                            <div v-else-if="activeTab === 'en'">
                                <!-- İngilizce Kategori Adı -->
                                <div class="mb-4">
                                    <InputLabel for="name_en" value="Category Name (English)" />
                                    <TextInput id="name_en" type="text" class="mt-1 block w-full" v-model="form.name.en" required />
                                    <InputError class="mt-2" :message="form.errors['name.en']" />
                                </div>

                                <!-- İngilizce Açıklama -->
                                <div class="mb-4">
                                    <InputLabel for="description_en" value="Description (English)" />
                                    <QuillEditor id="description_en" class="mt-1 block w-full" v-model="form.description.en" />
                                    <InputError class="mt-2" :message="form.errors['description.en']" />
                                </div>

                                <!-- İngilizce Meta Açıklama -->
                                <div class="mb-4">
                                    <InputLabel for="meta_description_en" value="Meta Description (English)" />
                                    <textarea id="meta_description_en" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" v-model="form.meta_description.en" rows="3" />
                                    <InputError class="mt-2" :message="form.errors['meta_description.en']" />
                                </div>

                                <!-- İngilizce Meta Anahtar Kelimeler -->
                                <div class="mb-4">
                                    <InputLabel for="meta_keywords_en" value="Meta Keywords (English)" />
                                    <textarea id="meta_keywords_en" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" v-model="form.meta_keywords.en" rows="2" />
                                    <InputError class="mt-2" :message="form.errors['meta_keywords.en']" />
                                </div>
                            </div>

                            <!-- Dil Bağımsız Alanlar -->
                            <div class="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2">
                                <!-- Parent Category -->
                                <div>
                                    <InputLabel for="parent_id" :value="locale === 'tr' ? 'Üst Kategori' : 'Parent Category'" />
                                    <select id="parent_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" v-model="form.parent_id">
                                        <option :value="null">{{ locale === 'tr' ? 'Ana Kategori' : 'Root Category' }}</option>
                                        <option v-for="category in categories" :key="category.id" :value="category.id">
                                            {{ category.name[locale] }}
                                        </option>
                                    </select>
                                    <InputError class="mt-2" :message="form.errors.parent_id" />
                                </div>

                                <!-- Order -->
                                <div>
                                    <InputLabel for="order" :value="locale === 'tr' ? 'Sıra' : 'Order'" />
                                    <TextInput id="order" type="number" class="mt-1 block w-full" v-model="form.order" required />
                                    <InputError class="mt-2" :message="form.errors.order" />
                                </div>

                                <!-- Main Group -->
                                <div>
                                    <InputLabel for="main_grup" :value="locale === 'tr' ? 'Ana Grup' : 'Main Group'" />
                                    <TextInput id="main_grup" type="text" class="mt-1 block w-full" v-model="form.main_grup" required />
                                    <InputError class="mt-2" :message="form.errors.main_grup" />
                                </div>

                                <!-- Is Active -->
                                <div class="flex items-center">
                                    <Checkbox id="is_active" v-model:checked="form.is_active" />
                                    <InputLabel for="is_active" :value="locale === 'tr' ? 'Aktif' : 'Active'" class="ml-2" />
                                    <InputError class="mt-2" :message="form.errors.is_active" />
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
