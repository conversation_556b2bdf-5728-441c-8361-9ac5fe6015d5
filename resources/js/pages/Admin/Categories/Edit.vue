<script setup>
import { useLocaleStore } from '@/Stores/locale';
import Checkbox from '@/components/Checkbox.vue';
import InputError from '@/components/InputError.vue';
import InputLabel from '@/components/InputLabel.vue';
import PrimaryButton from '@/components/PrimaryButton.vue';
import TextArea from '@/components/TextArea.vue';
import TextInput from '@/components/TextInput.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { QuillEditor } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import { computed, ref } from 'vue';

const props = defineProps({
    category: Object,
    categories: Array,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const form = useForm({
    name: props.category.name,
    description: props.category.description || { tr: '', en: '' },
    parent_id: props.category.parent_id,
    is_active: props.category.is_active,
    order: props.category.order,
    meta_description: props.category.meta_description || { tr: '', en: '' },
    meta_keywords: props.category.meta_keywords || { tr: '', en: '' },
    main_grup: props.category.main_grup,
});

const submit = () => {
    form.put(route('admin.categories.update', props.category.id));
};

const activeTab = ref('tr');

const editorOptions = {
    theme: 'snow',
    modules: {
        toolbar: [
            ['bold', 'italic', 'underline', 'strike'],
            ['blockquote', 'code-block'],
            [{ header: 1 }, { header: 2 }],
            [{ list: 'ordered' }, { list: 'bullet' }],
            [{ script: 'sub' }, { script: 'super' }],
            [{ indent: '-1' }, { indent: '+1' }],
            [{ direction: 'rtl' }],
            [{ size: ['small', false, 'large', 'huge'] }],
            [{ header: [1, 2, 3, 4, 5, 6, false] }],
            [{ color: [] }, { background: [] }],
            [{ font: [] }],
            [{ align: [] }],
            ['clean'],
            ['link', 'image', 'video'],
        ],
    },
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'Kategori Düzenle' : 'Edit Category'" />

    <AdminLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">
                {{ locale === 'tr' ? 'Kategori Düzenle' : 'Edit Category' }}
            </h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="border-b border-gray-200 bg-white p-6">
                        <form @submit.prevent="submit" class="space-y-6">
                            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <!-- Turkish Name -->
                                <div>
                                    <InputLabel for="name_tr" :value="locale === 'tr' ? 'Kategori Adı (Türkçe)' : 'Category Name (Turkish)'" />
                                    <TextInput id="name_tr" type="text" class="mt-1 block w-full" v-model="form.name.tr" required autofocus />
                                    <InputError class="mt-2" :message="form.errors['name.tr']" />
                                </div>

                                <!-- English Name -->
                                <div>
                                    <InputLabel for="name_en" :value="locale === 'tr' ? 'Kategori Adı (İngilizce)' : 'Category Name (English)'" />
                                    <TextInput id="name_en" type="text" class="mt-1 block w-full" v-model="form.name.en" required />
                                    <InputError class="mt-2" :message="form.errors['name.en']" />
                                </div>

                                <!-- Parent Category -->
                                <div>
                                    <InputLabel for="parent_id" :value="locale === 'tr' ? 'Üst Kategori' : 'Parent Category'" />
                                    <select id="parent_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" v-model="form.parent_id">
                                        <option :value="null">{{ locale === 'tr' ? 'Ana Kategori' : 'Root Category' }}</option>
                                        <option v-for="category in categories" :key="category.id" :value="category.id">
                                            {{ category.name[locale] }}
                                        </option>
                                    </select>
                                    <InputError class="mt-2" :message="form.errors.parent_id" />
                                </div>

                                <!-- Order -->
                                <div>
                                    <InputLabel for="order" :value="locale === 'tr' ? 'Sıra' : 'Order'" />
                                    <TextInput id="order" type="number" class="mt-1 block w-full" v-model="form.order" required />
                                    <InputError class="mt-2" :message="form.errors.order" />
                                </div>

                                <!-- Main Group -->
                                <div>
                                    <InputLabel for="main_grup" :value="locale === 'tr' ? 'Ana Grup' : 'Main Group'" />
                                    <TextInput id="main_grup" type="text" class="mt-1 block w-full" v-model="form.main_grup" required />
                                    <InputError class="mt-2" :message="form.errors.main_grup" />
                                </div>

                                <!-- Is Active -->
                                <div class="flex items-center">
                                    <Checkbox id="is_active" v-model:checked="form.is_active" />
                                    <InputLabel for="is_active" :value="locale === 'tr' ? 'Aktif' : 'Active'" class="ml-2" />
                                    <InputError class="mt-2" :message="form.errors.is_active" />
                                </div>
                            </div>

                            <!-- Turkish Description -->
                            <div>
                                <InputLabel for="description_tr" :value="locale === 'tr' ? 'Açıklama (Türkçe)' : 'Description (Turkish)'" />
                                <QuillEditor id="description_tr" class="mt-1 block w-full" v-model:content="form.description.tr" contentType="html" :options="editorOptions" theme="snow" style="min-height: 150px" />
                                <InputError class="mt-2" :message="form.errors['description.tr']" />
                            </div>

                            <!-- English Description -->
                            <div>
                                <InputLabel for="description_en" :value="locale === 'tr' ? 'Açıklama (İngilizce)' : 'Description (English)'" />
                                <QuillEditor id="description_en" class="mt-1 block w-full" v-model:content="form.description.en" contentType="html" :options="editorOptions" theme="snow" style="min-height: 150px" />
                                <InputError class="mt-2" :message="form.errors['description.en']" />
                            </div>

                            <!-- Meta Description -->
                            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div>
                                    <InputLabel for="meta_description_tr" :value="locale === 'tr' ? 'Meta Açıklama (Türkçe)' : 'Meta Description (Turkish)'" />
                                    <TextArea id="meta_description_tr" class="mt-1 block w-full" v-model="form.meta_description.tr" rows="2" />
                                    <InputError class="mt-2" :message="form.errors['meta_description.tr']" />
                                </div>

                                <div>
                                    <InputLabel for="meta_description_en" :value="locale === 'tr' ? 'Meta Açıklama (İngilizce)' : 'Meta Description (English)'" />
                                    <TextArea id="meta_description_en" class="mt-1 block w-full" v-model="form.meta_description.en" rows="2" />
                                    <InputError class="mt-2" :message="form.errors['meta_description.en']" />
                                </div>
                            </div>

                            <!-- Meta Keywords -->
                            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div>
                                    <InputLabel for="meta_keywords_tr" :value="locale === 'tr' ? 'Meta Anahtar Kelimeler (Türkçe)' : 'Meta Keywords (Turkish)'" />
                                    <TextArea id="meta_keywords_tr" class="mt-1 block w-full" v-model="form.meta_keywords.tr" rows="2" placeholder="keyword1, keyword2, keyword3" />
                                    <InputError class="mt-2" :message="form.errors['meta_keywords.tr']" />
                                </div>

                                <div>
                                    <InputLabel for="meta_keywords_en" :value="locale === 'tr' ? 'Meta Anahtar Kelimeler (İngilizce)' : 'Meta Keywords (English)'" />
                                    <TextArea id="meta_keywords_en" class="mt-1 block w-full" v-model="form.meta_keywords.en" rows="2" placeholder="keyword1, keyword2, keyword3" />
                                    <InputError class="mt-2" :message="form.errors['meta_keywords.en']" />
                                </div>
                            </div>

                            <div class="mt-4 flex items-center justify-end">
                                <Link
                                    :href="route('admin.categories.index')"
                                    class="mr-2 inline-flex items-center rounded-md border border-transparent bg-gray-300 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-gray-800 hover:bg-gray-400 focus:border-gray-500 focus:outline-none focus:ring focus:ring-gray-300 active:bg-gray-500 disabled:opacity-25"
                                >
                                    {{ locale === 'tr' ? 'İptal' : 'Cancel' }}
                                </Link>
                                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                                    {{ locale === 'tr' ? 'Güncelle' : 'Update' }}
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
