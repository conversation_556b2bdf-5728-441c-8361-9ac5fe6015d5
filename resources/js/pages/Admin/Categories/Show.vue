<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { computed } from 'vue';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    category: Object,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const confirmDelete = (id) => {
    if (confirm(locale.value === 'tr' ? 'Bu kategoriyi silmek istediğinizden emin misiniz?' : 'Are you sure you want to delete this category?')) {
        window.location.href = route(`admin.users.destroy`, id);
    }
};

const getCategoryName = (category) => {
    if (!category) return '-';
    return category.name[locale.value];
};
</script>

<template>
    <Head :title="locale === 'tr' ? '<PERSON><PERSON>i <PERSON>ayları' : 'Category Details'" />

    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ locale === 'tr' ? 'Kategori Detayları' : 'Category Details' }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-medium text-gray-900">
                                {{ category.name[locale] }}
                            </h3>
                            <div class="flex space-x-2">
                                <Link
                                    :href="route('admin.categories.edit', category.id)"
                                    class="inline-flex items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-500 active:bg-yellow-700 focus:outline-none focus:border-yellow-700 focus:ring focus:ring-yellow-300 disabled:opacity-25 "
                                >
                                    {{ locale === 'tr' ? 'Düzenle' : 'Edit' }}
                                </Link>
                                <button
                                    @click="confirmDelete(category.id)"
                                    class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:border-red-700 focus:ring focus:ring-red-300 disabled:opacity-25 "
                                >
                                    {{ locale === 'tr' ? 'Sil' : 'Delete' }}
                                </button>
                                <form
                                    :id="`delete-form-${category.id}`"
                                    :action="route('admin.categories.destroy', category.id)"
                                    method="POST"
                                    class="hidden"
                                >
                                    <input type="hidden" name="_method" value="DELETE">
                                    <input type="hidden" name="_token" :value="$page.props.csrf_token">
                                </form>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-2">{{ locale === 'tr' ? 'Kategori Bilgileri' : 'Category Information' }}</h4>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <div class="mb-4">
                                        <p class="text-sm text-gray-500">{{ locale === 'tr' ? 'ID' : 'ID' }}</p>
                                        <p class="text-sm font-medium text-gray-900">{{ category.id }}</p>
                                    </div>
                                    <div class="mb-4">
                                        <p class="text-sm text-gray-500">{{ locale === 'tr' ? 'Slug' : 'Slug' }}</p>
                                        <p class="text-sm font-medium text-gray-900">{{ category.slug }}</p>
                                    </div>
                                    <div class="mb-4">
                                        <p class="text-sm text-gray-500">{{ locale === 'tr' ? 'Üst Kategori' : 'Parent Category' }}</p>
                                        <p class="text-sm font-medium text-gray-900">
                                            {{ category.parent ? getCategoryName(category.parent) : (locale === 'tr' ? 'Ana Kategori' : 'Root Category') }}
                                        </p>
                                    </div>
                                    <div class="mb-4">
                                        <p class="text-sm text-gray-500">{{ locale === 'tr' ? 'Durum' : 'Status' }}</p>
                                        <span 
                                            :class="[
                                                'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                                category.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                            ]"
                                        >
                                            {{ category.is_active ? (locale === 'tr' ? 'Aktif' : 'Active') : (locale === 'tr' ? 'Pasif' : 'Inactive') }}
                                        </span>
                                    </div>
                                    <div class="mb-4">
                                        <p class="text-sm text-gray-500">{{ locale === 'tr' ? 'Sıra' : 'Order' }}</p>
                                        <p class="text-sm font-medium text-gray-900">{{ category.order }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">{{ locale === 'tr' ? 'Ana Grup' : 'Main Group' }}</p>
                                        <p class="text-sm font-medium text-gray-900">{{ category.main_grup }}</p>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-2">{{ locale === 'tr' ? 'Açıklama' : 'Description' }}</h4>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <div class="mb-4">
                                        <p class="text-sm text-gray-500">{{ locale === 'tr' ? 'Türkçe' : 'Turkish' }}</p>
                                        <p class="text-sm text-gray-900">{{ category.description?.tr || '-' }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">{{ locale === 'tr' ? 'İngilizce' : 'English' }}</p>
                                        <p class="text-sm text-gray-900">{{ category.description?.en || '-' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-500 mb-2">{{ locale === 'tr' ? 'Meta Bilgileri' : 'Meta Information' }}</h4>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <p class="text-sm text-gray-500">{{ locale === 'tr' ? 'Meta Açıklama (Türkçe)' : 'Meta Description (Turkish)' }}</p>
                                        <p class="text-sm text-gray-900">{{ category.meta_description?.tr || '-' }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">{{ locale === 'tr' ? 'Meta Açıklama (İngilizce)' : 'Meta Description (English)' }}</p>
                                        <p class="text-sm text-gray-900">{{ category.meta_description?.en || '-' }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">{{ locale === 'tr' ? 'Meta Anahtar Kelimeler (Türkçe)' : 'Meta Keywords (Turkish)' }}</p>
                                        <p class="text-sm text-gray-900">{{ category.meta_keywords?.tr || '-' }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">{{ locale === 'tr' ? 'Meta Anahtar Kelimeler (İngilizce)' : 'Meta Keywords (English)' }}</p>
                                        <p class="text-sm text-gray-900">{{ category.meta_keywords?.en || '-' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-if="category.children && category.children.length > 0" class="mb-6">
                            <h4 class="text-sm font-medium text-gray-500 mb-2">{{ locale === 'tr' ? 'Alt Kategoriler' : 'Child Categories' }}</h4>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <ul class="divide-y divide-gray-200">
                                    <li v-for="child in category.children" :key="child.id" class="py-2">
                                        <Link :href="route('admin.categories.show', child.id)" class="text-indigo-600 hover:text-indigo-900">
                                            {{ child.name[locale] }}
                                        </Link>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div v-if="category.products && category.products.length > 0">
                            <h4 class="text-sm font-medium text-gray-500 mb-2">{{ locale === 'tr' ? 'Ürünler' : 'Products' }}</h4>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <ul class="divide-y divide-gray-200">
                                    <li v-for="product in category.products" :key="product.id" class="py-2">
                                        <Link :href="route('admin.products.show', product.id)" class="text-indigo-600 hover:text-indigo-900">
                                            {{ product.name[locale] }}
                                        </Link>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="mt-6">
                            <Link
                                :href="route('admin.categories.index')"
                                class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 "
                            >
                                {{ locale === 'tr' ? 'Kategorilere Dön' : 'Back to Categories' }}
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
