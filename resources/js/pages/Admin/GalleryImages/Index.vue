<template>
    <div>
        <Head :title="`Gallery Images - ${gallery.title}`" />
        <div class="mb-4 flex items-center justify-between">
            <h1 class="text-2xl font-bold">Gallery Images for {{ gallery.title }}</h1>
            <Link :href="route('gallery_images.create', gallery.id)" class="inline-flex items-center rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"> Create Image </Link>
        </div>
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Image</th>
                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Thumbnail</th>
                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Active</th>
                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Order</th>
                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Actions</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
                <tr v-for="image in images" :key="image.id">
                    <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{{ image.id }}</td>
                    <td class="whitespace-nowrap px-6 py-4">
                        <img :src="image.image_url" alt="Image" class="h-20 w-20 object-cover" />
                    </td>
                    <td class="whitespace-nowrap px-6 py-4">
                        <img :src="image.thumbnail_url" alt="Thumbnail" class="h-20 w-20 object-cover" />
                    </td>
                    <td class="whitespace-nowrap px-6 py-4">
                        <span :class="image.is_active ? 'text-theme-secondary' : 'text-red-600'">
                            {{ image.is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </td>
                    <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{{ image.order }}</td>
                    <td class="whitespace-nowrap px-6 py-4 text-sm font-medium">
                        <Link :href="route('gallery_images.edit', [gallery.id, image.id])" class="mr-2 text-theme-primary hover:text-theme-primary/80"> Edit </Link>
                        <button @click="deleteImage(image.id)" class="text-red-600 hover:text-red-900">Delete</button>
                    </td>
                </tr>
                <tr v-if="images.length === 0">
                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">No images found.</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup>
import { Inertia } from '@inertiajs/inertia';
import { Head, Link } from '@inertiajs/vue3';

const props = defineProps({
    gallery: Object,
    images: Array,
});

const deleteImage = (id) => {
    if (confirm('Are you sure you want to delete this image?')) {
        Inertia.delete(route('gallery_images.destroy', [props.gallery.id, id]));
    }
};
</script>
