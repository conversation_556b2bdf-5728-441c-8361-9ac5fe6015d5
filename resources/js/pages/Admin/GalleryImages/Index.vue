<template>
    <div>
      <Head :title="`Gallery Images - ${gallery.title}`" />
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold">Gallery Images for {{ gallery.title }}</h1>
        <Link
          :href="route('gallery_images.create', gallery.id)"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Create Image
        </Link>
      </div>
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thumbnail</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="image in images" :key="image.id">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ image.id }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <img :src="image.image_url" alt="Image" class="w-20 h-20 object-cover" />
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <img :src="image.thumbnail_url" alt="Thumbnail" class="w-20 h-20 object-cover" />
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="image.is_active ? 'text-green-600' : 'text-red-600'">
                {{ image.is_active ? 'Active' : 'Inactive' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ image.order }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <Link
                :href="route('gallery_images.edit', [gallery.id, image.id])"
                class="text-indigo-600 hover:text-indigo-900 mr-2"
              >
                Edit
              </Link>
              <button @click="deleteImage(image.id)" class="text-red-600 hover:text-red-900">
                Delete
              </button>
            </td>
          </tr>
          <tr v-if="images.length === 0">
            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
              No images found.
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </template>
  
  <script setup>
  import { Head, Link } from '@inertiajs/vue3';
  import { Inertia } from '@inertiajs/inertia';
  
  const props = defineProps({
    gallery: Object,
    images: Array,
  });
  
  const deleteImage = (id) => {
    if (confirm('Are you sure you want to delete this image?')) {
      Inertia.delete(route('gallery_images.destroy', [props.gallery.id, id]));
    }
  };
  </script>
  