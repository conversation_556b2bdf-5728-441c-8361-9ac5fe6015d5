<script setup>
import { Head } from '@inertiajs/vue3';
import { useForm } from '@inertiajs/vue3';
import { computed } from 'vue';
import { useLocaleStore } from '@/Stores/locale';
import AdminLayout from '@/layouts/AdminLayout.vue';

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const props = defineProps({
  gallery: Object,
});

// useForm ile formu oluşturuyoruz; controller'da gallery_id set edilecektir.
const form = useForm({
  image_path: null,        // dosya için null başlatıyoruz
  thumbnail_path: null,
  order: 0,
  is_active: true,
});

const submit = () => {
  form.post(route('admin.gallery_images.store', props.gallery.id), {
    forceFormData: true,
  });
};




</script>

<template>
  <Head :title="locale === 'tr' ? '<PERSON><PERSON>smi <PERSON>' : 'Create Gallery Image'" />

  <AdminLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        {{ locale === 'tr' ? 'Galeri Resmi Oluştur' : 'Create Gallery Image' }}
      </h2>
    </template>
    <div class="max-w-3xl mx-auto p-6 bg-white rounded shadow">
      <h1 class="text-2xl font-bold mb-4">Create Gallery Image</h1>
      <form @submit.prevent="submit" enctype="multipart/form-data">
        <div class="mb-4">
          <label for="image_path" class="block text-sm font-medium text-gray-700">Image File</label>
          <input
            type="file"
            id="image_path"
            @change="e => form.image_path = e.target.files[0]"
            class="mt-1 block w-full border-gray-300 rounded"
            required
          />
          <div v-if="form.errors.image_path" class="text-red-600 text-sm mt-1">
            {{ form.errors.image_path }}
          </div>
        </div>
  
        <div class="mb-4">
          <label for="thumbnail_path" class="block text-sm font-medium text-gray-700">
            Thumbnail File <span class="text-xs text-gray-500">(optional, leave empty to auto-generate)</span>
          </label>
          <input
            type="file"
            id="thumbnail_path"
            @change="e => form.thumbnail_path = e.target.files[0]"
            class="mt-1 block w-full border-gray-300 rounded"
          />
          <div v-if="form.errors.thumbnail_path" class="text-red-600 text-sm mt-1">
            {{ form.errors.thumbnail_path }}
          </div>
        </div>
  
        <div class="mb-4">
          <label for="order" class="block text-sm font-medium text-gray-700">Order</label>
          <input
            type="number"
            id="order"
            v-model="form.order"
            class="mt-1 block w-full border-gray-300 rounded"
            required
          />
          <div v-if="form.errors.order" class="text-red-600 text-sm mt-1">
            {{ form.errors.order }}
          </div>
        </div>
  
        <div class="mb-4">
          <label for="is_active" class="block text-sm font-medium text-gray-700">Active</label>
          <select id="is_active" v-model="form.is_active" class="mt-1 block w-full border-gray-300 rounded">
            <option :value="true">Active</option>
            <option :value="false">Inactive</option>
          </select>
          <div v-if="form.errors.is_active" class="text-red-600 text-sm mt-1">
            {{ form.errors.is_active }}
          </div>
        </div>
  
        <div class="flex justify-end">
          <button
            type="submit"
            :disabled="form.processing"
            class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Create
          </button>
        </div>
      </form>
    </div>
  </AdminLayout>
</template>
