<template>
  <AdminLayout title="Create Role">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Create New Role
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 bg-white border-b border-gray-200">
            <form @submit.prevent="submit">
              <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                <input
                  id="name"
                  v-model="form.name"
                  type="text"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                  placeholder="Role name (e.g. admin, editor)"
                />
                <div v-if="form.errors.name" class="text-red-500 text-sm mt-1">{{ form.errors.name }}</div>
              </div>

              <div class="mb-4">
                <label for="display_name" class="block text-sm font-medium text-gray-700">Display Name</label>
                <input
                  id="display_name"
                  v-model="form.display_name"
                  type="text"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="Human-readable name (e.g. Administrator, Editor)"
                />
                <div v-if="form.errors.display_name" class="text-red-500 text-sm mt-1">{{ form.errors.display_name }}</div>
              </div>

              <div class="mb-4">
                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                <textarea
                  id="description"
                  v-model="form.description"
                  rows="3"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="Role description"
                ></textarea>
                <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
              </div>

              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Permissions</label>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <!-- Group permissions by module -->
                  <div v-for="(modulePermissions, module) in groupedPermissions" :key="module" class="border rounded-md p-4">
                    <h3 class="font-medium text-gray-900 mb-2 capitalize">{{ module }}</h3>
                    <div v-for="permission in modulePermissions" :key="permission.id" class="flex items-start">
                      <div class="flex items-center h-5">
                        <input
                          :id="`permission-${permission.id}`"
                          v-model="form.permissions"
                          :value="permission.id"
                          type="checkbox"
                          class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                        />
                      </div>
                      <div class="ml-3 text-sm">
                        <label :for="`permission-${permission.id}`" class="font-medium text-gray-700">{{ permission.display_name }}</label>
                        <p class="text-gray-500">{{ permission.description }}</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div v-if="form.errors.permissions" class="text-red-500 text-sm mt-1">{{ form.errors.permissions }}</div>
              </div>

              <div class="flex items-center justify-end mt-4">
                <Link
                  :href="route('admin.roles.index')"
                  class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-400 active:bg-gray-500 focus:outline-none focus:border-gray-500 focus:ring focus:ring-gray-300 disabled:opacity-25  mr-2"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 "
                  :disabled="form.processing"
                >
                  Create Role
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script>
import AdminLayout from '@/layouts/AdminLayout.vue';
import { Link, useForm } from '@inertiajs/vue3';

export default {
  components: {
    AdminLayout,
    Link,
  },
  props: {
    permissions: Array,
  },
  data() {
    return {
      form: useForm({
        name: '',
        display_name: '',
        description: '',
        permissions: [],
      }),
    };
  },
  computed: {
    groupedPermissions() {
      const grouped = {};
      
      this.permissions.forEach(permission => {
        const module = permission.module || 'other';
        if (!grouped[module]) {
          grouped[module] = [];
        }
        grouped[module].push(permission);
      });
      
      return grouped;
    },
  },
  methods: {
    submit() {
      this.form.post(route('admin.roles.store'));
    },
  },
};
</script>
