<template>
    <AdminLayout :title="`Role: ${role.display_name || role.name}`">
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">Role Details: {{ role.display_name || role.name }}</h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="border-b border-gray-200 bg-white p-6">
                        <div class="mb-6 flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900">Role Information</h3>
                            <div class="flex space-x-2">
                                <Link
                                    :href="route('admin.roles.edit', role.id)"
                                    class="inline-flex items-center rounded-md border border-transparent bg-[#56509a] px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-[#56509a] focus:border-indigo-900 focus:outline-none focus:ring focus:ring-indigo-300 active:bg-indigo-900 disabled:opacity-25"
                                >
                                    Edit Role
                                </Link>
                                <Link
                                    :href="route('admin.roles.index')"
                                    class="inline-flex items-center rounded-md border border-transparent bg-gray-300 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-gray-800 hover:bg-gray-400 focus:border-gray-500 focus:outline-none focus:ring focus:ring-gray-300 active:bg-gray-500 disabled:opacity-25"
                                >
                                    Back to Roles
                                </Link>
                            </div>
                        </div>

                        <div class="rounded-t-md bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Name</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ role.name }}</dd>
                        </div>
                        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Display Name</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ role.display_name }}</dd>
                        </div>
                        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Description</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ role.description }}</dd>
                        </div>
                        <div class="rounded-b-md bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Permissions</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                <div class="flex flex-wrap gap-2">
                                    <span
                                        v-for="permission in role.permissions"
                                        :key="permission.id"
                                        class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800"
                                    >
                                        {{ permission.display_name }}
                                    </span>
                                </div>
                            </dd>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script>
import AdminLayout from '@/layouts/AdminLayout.vue';
import { Link } from '@inertiajs/vue3';

export default {
    components: {
        AdminLayout,
        Link,
    },
    props: {
        role: Object,
    },
};
</script>
