<template>
    <AdminLayout title="Upload Files">
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">Upload Files</h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="border-b border-gray-200 bg-white p-6">
                        <div class="mb-6">
                            <Link :href="route('admin.files.index')" class="text-indigo-600 hover:text-indigo-900">
                                &larr; Back to File Manager
                            </Link>
                        </div>

                        <div class="mb-6">
                            <label for="folder" class="block text-sm font-medium text-gray-700">Folder</label>
                            <select
                                id="folder"
                                v-model="form.folder"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            >
                                <option value="uploads">Uploads</option>
                                <option value="products">Products</option>
                                <option value="pages">Pages</option>
                                <option value="gallery">Gallery</option>
                            </select>
                            <div v-if="form.errors.folder" class="mt-1 text-sm text-red-500">{{ form.errors.folder }}</div>
                        </div>

                        <!-- Single File Upload -->
                        <div class="mb-6">
                            <h3 class="mb-4 text-lg font-medium text-gray-900">Single File Upload</h3>
                            <div
                                class="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pb-6 pt-5"
                                :class="{ 'border-indigo-500': isDraggingOver }"
                                @dragover.prevent="isDraggingOver = true"
                                @dragleave.prevent="isDraggingOver = false"
                                @drop.prevent="handleFileDrop"
                            >
                                <div class="space-y-1 text-center">
                                    <svg
                                        class="mx-auto h-12 w-12 text-gray-400"
                                        stroke="currentColor"
                                        fill="none"
                                        viewBox="0 0 48 48"
                                        aria-hidden="true"
                                    >
                                        <path
                                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                        />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label
                                            for="file-upload"
                                            class="relative cursor-pointer rounded-md bg-white font-medium text-indigo-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 hover:text-indigo-500"
                                        >
                                            <span>Upload a file</span>
                                            <input id="file-upload" name="file-upload" type="file" class="sr-only" @change="handleFileChange" />
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG, GIF, PDF, DOCX up to 10MB</p>
                                </div>
                            </div>
                            <div v-if="form.errors.file" class="mt-1 text-sm text-red-500">{{ form.errors.file }}</div>

                            <div v-if="selectedFile" class="mt-4 flex items-center space-x-2">
                                <div class="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-md bg-gray-100">
                                    <svg
                                        v-if="isImage(selectedFile)"
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-6 w-6 text-gray-400"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                        />
                                    </svg>
                                    <svg
                                        v-else
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-6 w-6 text-gray-400"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                        />
                                    </svg>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="truncate text-sm font-medium text-gray-900">
                                        {{ selectedFile.name }}
                                    </p>
                                    <p class="text-sm text-gray-500">
                                        {{ formatFileSize(selectedFile.size) }}
                                    </p>
                                </div>
                                <button
                                    type="button"
                                    class="inline-flex items-center rounded-full border border-transparent bg-red-600 p-1 text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                                    @click="removeSelectedFile"
                                >
                                    <svg
                                        class="h-5 w-5"
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                        aria-hidden="true"
                                    >
                                        <path
                                            fill-rule="evenodd"
                                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                            clip-rule="evenodd"
                                        />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Multiple File Upload -->
                        <div class="mb-6">
                            <h3 class="mb-4 text-lg font-medium text-gray-900">Multiple Files Upload</h3>
                            <div
                                class="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pb-6 pt-5"
                                :class="{ 'border-indigo-500': isMultipleDraggingOver }"
                                @dragover.prevent="isMultipleDraggingOver = true"
                                @dragleave.prevent="isMultipleDraggingOver = false"
                                @drop.prevent="handleMultipleFileDrop"
                            >
                                <div class="space-y-1 text-center">
                                    <svg
                                        class="mx-auto h-12 w-12 text-gray-400"
                                        stroke="currentColor"
                                        fill="none"
                                        viewBox="0 0 48 48"
                                        aria-hidden="true"
                                    >
                                        <path
                                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                        />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label
                                            for="multiple-files-upload"
                                            class="relative cursor-pointer rounded-md bg-white font-medium text-indigo-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 hover:text-indigo-500"
                                        >
                                            <span>Upload multiple files</span>
                                            <input
                                                id="multiple-files-upload"
                                                name="multiple-files-upload"
                                                type="file"
                                                multiple
                                                class="sr-only"
                                                @change="handleMultipleFileChange"
                                            />
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG, GIF, PDF, DOCX up to 10MB each</p>
                                </div>
                            </div>
                            <div v-if="multipleForm.errors.files" class="mt-1 text-sm text-red-500">{{ multipleForm.errors.files }}</div>

                            <div v-if="selectedFiles.length > 0" class="mt-4 space-y-2">
                                <div v-for="(file, index) in selectedFiles" :key="index" class="flex items-center space-x-2">
                                    <div class="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-md bg-gray-100">
                                        <svg
                                            v-if="isImage(file)"
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-6 w-6 text-gray-400"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                            />
                                        </svg>
                                        <svg
                                            v-else
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-6 w-6 text-gray-400"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                            />
                                        </svg>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <p class="truncate text-sm font-medium text-gray-900">
                                            {{ file.name }}
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            {{ formatFileSize(file.size) }}
                                        </p>
                                    </div>
                                    <button
                                        type="button"
                                        class="inline-flex items-center rounded-full border border-transparent bg-red-600 p-1 text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                                        @click="removeSelectedFileAt(index)"
                                    >
                                        <svg
                                            class="h-5 w-5"
                                            xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20"
                                            fill="currentColor"
                                            aria-hidden="true"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                                clip-rule="evenodd"
                                            />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3">
                            <Link
                                :href="route('admin.files.index')"
                                class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-xs font-semibold uppercase tracking-widest text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25"
                            >
                                Cancel
                            </Link>
                            <button
                                type="button"
                                @click="uploadSingleFile"
                                :disabled="!selectedFile || form.processing"
                                class="inline-flex items-center rounded-md border border-transparent bg-gray-800 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-gray-700 focus:border-gray-900 focus:outline-none focus:ring focus:ring-gray-300 active:bg-gray-900 disabled:opacity-25"
                            >
                                <span v-if="form.processing">Uploading...</span>
                                <span v-else>Upload File</span>
                            </button>
                            <button
                                type="button"
                                @click="uploadMultipleFiles"
                                :disabled="selectedFiles.length === 0 || multipleForm.processing"
                                class="inline-flex items-center rounded-md border border-transparent bg-[#56509a] px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-[#56509a] focus:border-indigo-900 focus:outline-none focus:ring focus:ring-indigo-300 active:bg-indigo-900 disabled:opacity-25"
                            >
                                <span v-if="multipleForm.processing">Uploading...</span>
                                <span v-else>Upload {{ selectedFiles.length }} Files</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script>
import AdminLayout from '@/layouts/AdminLayout.vue';
import { Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

export default {
    components: {
        AdminLayout,
        Link,
    },
    setup() {
        // Single file upload
        const selectedFile = ref(null);
        const isDraggingOver = ref(false);
        const form = useForm({
            file: null,
            folder: 'uploads',
        });

        // Multiple files upload
        const selectedFiles = ref([]);
        const isMultipleDraggingOver = ref(false);
        const multipleForm = useForm({
            files: [],
            folder: 'uploads',
        });

        const handleFileChange = (e) => {
            const file = e.target.files[0];
            if (file) {
                selectedFile.value = file;
                form.file = file;
            }
        };

        const handleFileDrop = (e) => {
            isDraggingOver.value = false;
            const file = e.dataTransfer.files[0];
            if (file) {
                selectedFile.value = file;
                form.file = file;
            }
        };

        const removeSelectedFile = () => {
            selectedFile.value = null;
            form.file = null;
            // Reset the file input
            const fileInput = document.getElementById('file-upload');
            if (fileInput) {
                fileInput.value = '';
            }
        };

        const handleMultipleFileChange = (e) => {
            const files = Array.from(e.target.files);
            if (files.length > 0) {
                selectedFiles.value = files;
                multipleForm.files = files;
            }
        };

        const handleMultipleFileDrop = (e) => {
            isMultipleDraggingOver.value = false;
            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                selectedFiles.value = files;
                multipleForm.files = files;
            }
        };

        const removeSelectedFileAt = (index) => {
            selectedFiles.value.splice(index, 1);
            multipleForm.files = [...selectedFiles.value];

            // If all files are removed, reset the file input
            if (selectedFiles.value.length === 0) {
                const fileInput = document.getElementById('multiple-files-upload');
                if (fileInput) {
                    fileInput.value = '';
                }
            }
        };

        const isImage = (file) => {
            const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/svg+xml', 'image/webp'];
            return imageTypes.includes(file.type);
        };

        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };

        const uploadSingleFile = () => {
            form.folder = multipleForm.folder; // Keep folders in sync

            form.post(route('admin.files.store'), {
                preserveScroll: true,
                onSuccess: () => {
                    removeSelectedFile();
                },
                onError: (errors) => {},
            });
        };

        const uploadMultipleFiles = () => {
            multipleForm.folder = form.folder; // Keep folders in sync

            multipleForm.post(route('admin.files.bulk-upload'), {
                preserveScroll: true,
                onSuccess: () => {
                    selectedFiles.value = [];
                    multipleForm.files = [];
                    // Reset the file input
                    const fileInput = document.getElementById('multiple-files-upload');
                    if (fileInput) {
                        fileInput.value = '';
                    }
                },
                onError: (errors) => {},
            });
        };

        return {
            // Single file upload
            selectedFile,
            isDraggingOver,
            form,
            handleFileChange,
            handleFileDrop,
            removeSelectedFile,

            // Multiple files upload
            selectedFiles,
            isMultipleDraggingOver,
            multipleForm,
            handleMultipleFileChange,
            handleMultipleFileDrop,
            removeSelectedFileAt,

            // Shared
            isImage,
            formatFileSize,
            uploadSingleFile,
            uploadMultipleFiles,
        };
    },
};
</script>
