<template>
    <AdminLayout title="File Manager">
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">File Manager</h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="border-b border-gray-200 bg-white p-6">
                        <!-- Search and Filters -->
                        <div class="mb-6 flex flex-col justify-between space-y-4 md:flex-row md:space-y-0">
                            <div class="flex w-full flex-col space-y-4 md:w-3/4 md:flex-row md:space-x-4 md:space-y-0">
                                <!-- Search -->
                                <div class="w-full md:w-1/2">
                                    <input
                                        v-model="search"
                                        type="text"
                                        placeholder="Search files..."
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                        @keyup.enter="searchFiles"
                                    />
                                </div>

                                <!-- Type Filter -->
                                <div class="w-full md:w-1/4">
                                    <select
                                        v-model="filters.type"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                        @change="searchFiles"
                                    >
                                        <option value="">All Types</option>
                                        <option value="image">Images</option>
                                        <option value="document">Documents</option>
                                    </select>
                                </div>

                                <!-- Folder Filter -->
                                <div class="w-full md:w-1/4">
                                    <select
                                        v-model="filters.folder"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                        @change="searchFiles"
                                    >
                                        <option value="">All Folders</option>
                                        <option value="uploads">Uploads</option>
                                        <option value="products">Products</option>
                                        <option value="pages">Pages</option>
                                        <option value="gallery">Gallery</option>
                                    </select>
                                </div>
                            </div>

                            <div class="flex space-x-2">
                                <Link
                                    :href="route('admin.files.create')"
                                    class="inline-flex items-center rounded-md border border-transparent bg-gray-800 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-gray-700 focus:border-gray-900 focus:outline-none focus:ring focus:ring-gray-300 active:bg-gray-900 disabled:opacity-25"
                                >
                                    Upload File
                                </Link>
                            </div>
                        </div>

                        <!-- Files Grid -->
                        <div v-if="files.data.length > 0" class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                            <div
                                v-for="file in files.data"
                                :key="file.id"
                                class="-shadow overflow-hidden rounded-lg border shadow-sm duration-200 hover:shadow-md"
                            >
                                <!-- Preview -->
                                <div class="flex h-40 items-center justify-center bg-gray-100 p-2">
                                    <img
                                        v-if="isImage(file)"
                                        :src="file.url"
                                        :alt="file.original_filename"
                                        class="max-h-full max-w-full object-contain"
                                    />
                                    <div v-else class="flex flex-col items-center justify-center">
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-16 w-16 text-gray-400"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                            />
                                        </svg>
                                        <span class="mt-2 text-xs text-gray-500">{{ file.extension.toUpperCase() }}</span>
                                    </div>
                                </div>

                                <!-- File Info -->
                                <div class="p-4">
                                    <h3 class="truncate text-sm font-medium text-gray-900" :title="file.original_filename">
                                        {{ file.original_filename }}
                                    </h3>
                                    <p class="mt-1 text-xs text-gray-500">{{ formatFileSize(file.size) }} • {{ formatDate(file.created_at) }}</p>
                                    <p class="mt-1 text-xs text-gray-500">Folder: {{ file.folder }}</p>
                                </div>

                                <!-- Actions -->
                                <div class="flex justify-between bg-gray-50 px-4 py-3">
                                    <Link :href="route('admin.files.show', file.id)" class="text-xs text-indigo-600 hover:text-indigo-900">
                                        View
                                    </Link>
                                    <Link :href="route('admin.files.edit', file.id)" class="text-xs text-indigo-600 hover:text-indigo-900">
                                        Edit
                                    </Link>
                                    <button @click="confirmDelete(file)" class="text-xs text-red-600 hover:text-red-900">Delete</button>
                                </div>
                            </div>
                        </div>

                        <!-- Empty State -->
                        <div v-else class="py-12 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="mx-auto h-12 w-12 text-gray-400"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"
                                />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No files found</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by uploading a new file.</p>
                            <div class="mt-6">
                                <Link
                                    :href="route('admin.files.create')"
                                    class="inline-flex items-center rounded-md border border-transparent bg-[#56509a] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#56509a] focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="-ml-1 mr-2 h-5 w-5"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                    </svg>
                                    Upload File
                                </Link>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div v-if="files.data.length > 0" class="mt-6">
                            <Pagination :links="files.links" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <Modal :show="showDeleteModal" @close="closeModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">Delete File</h2>
                <p class="mt-1 text-sm text-gray-600">Are you sure you want to delete this file? This action cannot be undone.</p>
                <div class="mt-6 flex justify-end space-x-3">
                    <button
                        type="button"
                        class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-xs font-semibold uppercase tracking-widest text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25"
                        @click="closeModal"
                    >
                        Cancel
                    </button>
                    <button
                        type="button"
                        class="inline-flex items-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 active:bg-red-700"
                        @click="deleteFile"
                    >
                        Delete
                    </button>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>

<script>
import Modal from '@/components/ui/Modal.vue';
import Pagination from '@/components/ui/Pagination.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { Link, router } from '@inertiajs/vue3';
import { ref, watch } from 'vue';

export default {
    components: {
        AdminLayout,
        Link,
        Pagination,
        Modal,
    },
    props: {
        files: Object,
        filters: Object,
    },
    setup(props) {
        const search = ref(props.filters.search || '');
        const showDeleteModal = ref(false);
        const fileToDelete = ref(null);

        watch(
            () => props.filters,
            (newFilters) => {
                search.value = newFilters.search || '';
            },
        );

        const searchFiles = () => {
            router.get(
                route('admin.files.index'),
                {
                    search: search.value,
                    type: props.filters.type,
                    folder: props.filters.folder,
                },
                {
                    preserveState: true,
                    replace: true,
                },
            );
        };

        const isImage = (file) => {
            const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/svg+xml', 'image/webp'];
            return imageTypes.includes(file.mime_type);
        };

        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };

        const formatDate = (dateString) => {
            const date = new Date(dateString);
            return date.toLocaleDateString();
        };

        const confirmDelete = (file) => {
            fileToDelete.value = file;
            showDeleteModal.value = true;
        };

        const closeModal = () => {
            showDeleteModal.value = false;
            fileToDelete.value = null;
        };

        const deleteFile = () => {
            router.delete(route('admin.files.destroy', fileToDelete.value.id), {
                onSuccess: () => {
                    closeModal();
                },
            });
        };

        return {
            search,
            showDeleteModal,
            fileToDelete,
            searchFiles,
            isImage,
            formatFileSize,
            formatDate,
            confirmDelete,
            closeModal,
            deleteFile,
        };
    },
};
</script>
