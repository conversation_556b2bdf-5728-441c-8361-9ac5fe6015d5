<template>
    <AdminLayout :title="`File: ${file.original_filename}`">
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">File Details</h2>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                    <div class="border-b border-gray-200 bg-white p-6">
                        <div class="mb-6 flex items-center justify-between">
                            <div>
                                <Link :href="route('admin.files.index')" class="text-indigo-600 hover:text-indigo-900">
                                    &larr; Back to File Manager
                                </Link>
                            </div>
                            <div class="flex space-x-2">
                                <Link
                                    :href="route('admin.files.edit', file.id)"
                                    class="inline-flex items-center rounded-md border border-transparent bg-[#56509a] px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-[#56509a] focus:border-indigo-900 focus:outline-none focus:ring focus:ring-indigo-300 active:bg-indigo-900 disabled:opacity-25"
                                >
                                    Edit File
                                </Link>
                                <button
                                    @click="confirmDelete"
                                    class="inline-flex items-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-red-500 focus:border-red-700 focus:outline-none focus:ring focus:ring-red-300 active:bg-red-700 disabled:opacity-25"
                                >
                                    Delete File
                                </button>
                            </div>
                        </div>

                        <!-- File Preview -->
                        <div class="mb-6">
                            <h3 class="mb-4 text-lg font-medium text-gray-900">File Preview</h3>
                            <div class="flex justify-center rounded-md bg-gray-100 p-4">
                                <img v-if="isImage(file)" :src="file.url" :alt="file.original_filename" class="max-h-96 max-w-full object-contain" />
                                <div v-else class="flex flex-col items-center justify-center p-8">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-24 w-24 text-gray-400"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                        />
                                    </svg>
                                    <span class="mt-2 text-xl text-gray-500">{{ file.extension.toUpperCase() }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- File Information -->
                        <div class="mb-6">
                            <h3 class="mb-4 text-lg font-medium text-gray-900">File Information</h3>
                            <div class="rounded-t-md bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Display Name</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ file.original_filename }}</dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">System Name</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ file.filename }}</dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">File Type</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ file.mime_type }}</dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">File Size</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ formatFileSize(file.size) }}</dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">File Path</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ file.file_path }}</dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Folder</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ file.folder }}</dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Storage Disk</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ file.disk }}</dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Uploaded By</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ file.user ? file.user.name : 'Unknown' }}</dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Upload Date</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ formatDate(file.created_at) }}</dd>
                            </div>
                            <div class="rounded-b-md bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ formatDate(file.updated_at) }}</dd>
                            </div>
                        </div>

                        <!-- File URL -->
                        <div class="mb-6">
                            <h3 class="mb-4 text-lg font-medium text-gray-900">File URL</h3>
                            <div class="flex items-center">
                                <input
                                    type="text"
                                    readonly
                                    :value="file.url"
                                    class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                />
                                <button
                                    @click="copyToClipboard(file.url)"
                                    class="ml-2 inline-flex items-center rounded-md border border-transparent bg-gray-800 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-gray-700 focus:border-gray-900 focus:outline-none focus:ring focus:ring-gray-300 active:bg-gray-900 disabled:opacity-25"
                                >
                                    Copy
                                </button>
                            </div>
                        </div>

                        <!-- Download Button -->
                        <div class="flex justify-center">
                            <a
                                :href="file.url"
                                download
                                class="inline-flex items-center rounded-md border border-transparent bg-green-600 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-green-500 focus:border-green-700 focus:outline-none focus:ring focus:ring-green-300 active:bg-green-700 disabled:opacity-25"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                                    />
                                </svg>
                                Download File
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <Modal :show="showDeleteModal" @close="closeModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">Delete File</h2>
                <p class="mt-1 text-sm text-gray-600">Are you sure you want to delete this file? This action cannot be undone.</p>
                <div class="mt-6 flex justify-end space-x-3">
                    <button
                        type="button"
                        class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-xs font-semibold uppercase tracking-widest text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25"
                        @click="closeModal"
                    >
                        Cancel
                    </button>
                    <button
                        type="button"
                        class="inline-flex items-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 active:bg-red-700"
                        @click="deleteFile"
                    >
                        Delete
                    </button>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>

<script>
import Modal from '@/components/ui/Modal.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { Link, router } from '@inertiajs/vue3';
import { ref } from 'vue';

export default {
    components: {
        AdminLayout,
        Link,
        Modal,
    },
    props: {
        file: Object,
    },
    setup(props) {
        const showDeleteModal = ref(false);

        const isImage = (file) => {
            const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/svg+xml', 'image/webp'];
            return imageTypes.includes(file.mime_type);
        };

        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };

        const formatDate = (dateString) => {
            const date = new Date(dateString);
            return date.toLocaleString();
        };

        const copyToClipboard = (text) => {
            navigator.clipboard
                .writeText(text)
                .then(() => {})
                .catch(() => {});
        };

        const confirmDelete = () => {
            showDeleteModal.value = true;
        };

        const closeModal = () => {
            showDeleteModal.value = false;
        };

        const deleteFile = () => {
            router.delete(route('admin.files.destroy', props.file.id), {
                onSuccess: () => {},
            });
        };

        return {
            isImage,
            formatFileSize,
            formatDate,
            copyToClipboard,
            showDeleteModal,
            confirmDelete,
            closeModal,
            deleteFile,
        };
    },
};
</script>
