<template>
  <AdminLayout title="Edit File">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Edit File
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 bg-white border-b border-gray-200">
            <div class="mb-6">
              <Link
                :href="route('admin.files.index')"
                class="text-indigo-600 hover:text-indigo-900"
              >
                &larr; Back to File Manager
              </Link>
            </div>

            <!-- File Preview -->
            <div class="mb-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">File Preview</h3>
              <div class="flex justify-center p-4 bg-gray-100 rounded-md">
                <img
                  v-if="isImage(file)"
                  :src="file.url"
                  :alt="file.original_filename"
                  class="max-h-64 max-w-full object-contain"
                />
                <div v-else class="flex flex-col items-center justify-center p-8">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <span class="text-lg text-gray-500 mt-2">{{ file.extension.toUpperCase() }}</span>
                </div>
              </div>
            </div>

            <!-- File Information -->
            <div class="mb-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">File Information</h3>
              <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-t-md">
                <dt class="text-sm font-medium text-gray-500">File Name</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ file.filename }}</dd>
              </div>
              <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">File Type</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ file.mime_type }}</dd>
              </div>
              <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">File Size</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ formatFileSize(file.size) }}</dd>
              </div>
              <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Uploaded By</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ file.user ? file.user.name : 'Unknown' }}</dd>
              </div>
              <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 rounded-b-md">
                <dt class="text-sm font-medium text-gray-500">Upload Date</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ formatDate(file.created_at) }}</dd>
              </div>
            </div>

            <!-- Edit Form -->
            <form @submit.prevent="submit">
              <div class="mb-4">
                <label for="original_filename" class="block text-sm font-medium text-gray-700">Display Name</label>
                <input
                  id="original_filename"
                  v-model="form.original_filename"
                  type="text"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                <div v-if="form.errors.original_filename" class="text-red-500 text-sm mt-1">{{ form.errors.original_filename }}</div>
              </div>

              <div class="mb-4">
                <label for="folder" class="block text-sm font-medium text-gray-700">Folder</label>
                <select
                  id="folder"
                  v-model="form.folder"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="uploads">Uploads</option>
                  <option value="products">Products</option>
                  <option value="pages">Pages</option>
                  <option value="gallery">Gallery</option>
                </select>
                <div v-if="form.errors.folder" class="text-red-500 text-sm mt-1">{{ form.errors.folder }}</div>
              </div>

              <div class="flex justify-end space-x-3">
                <Link
                  :href="route('admin.files.index')"
                  class="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 "
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  :disabled="form.processing"
                  class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 "
                >
                  <span v-if="form.processing">Saving...</span>
                  <span v-else>Save Changes</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script>
import AdminLayout from '@/layouts/AdminLayout.vue';
import { Link, useForm } from '@inertiajs/vue3';

export default {
  components: {
    AdminLayout,
    Link,
  },
  props: {
    file: Object,
  },
  setup(props) {
    
    const form = useForm({
      original_filename: props.file.original_filename,
      folder: props.file.folder,
    });

    const isImage = (file) => {
      const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/svg+xml', 'image/webp'];
      return imageTypes.includes(file.mime_type);
    };

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleString();
    };

    const submit = () => {
      form.put(route('admin.files.update', props.file.id), {
        onSuccess: () => {
        },
        onError: () => {
        },
      });
    };

    return {
      form,
      isImage,
      formatFileSize,
      formatDate,
      submit,
    };
  },
};
</script>
