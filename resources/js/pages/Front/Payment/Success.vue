<script setup>
import PrimaryButton from '@/components/PrimaryButton.vue';
import FrontLayout from '@/layouts/FrontLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
const appEmail = import.meta.env.VITE_APP_EMAIL || 'Laravel';

const props = defineProps({
    order: Object,
    message: String,
});

// Currency formatter
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY',
        minimumFractionDigits: 2,
    }).format(amount);
};

// Date formatter
const formatDate = (date) => {
    return new Date(date).toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};
</script>

<template>
    <Head title="Ödeme Başarılı" />

    <FrontLayout>
        <div class="min-h-screen bg-gray-50 py-12">
            <div class="mx-auto max-w-2xl px-4 sm:px-6 lg:px-8">
                <div class="rounded-lg bg-white p-8 shadow-sm">
                    <!-- Success Icon -->
                    <div class="text-center">
                        <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                            <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <h1 class="mt-4 text-2xl font-bold text-gray-900">Ödeme Başarılı!</h1>
                        <p class="mt-2 text-gray-600">{{ message || 'Ödemeniz başarıyla tamamlandı.' }}</p>
                    </div>

                    <!-- Order Details -->
                    <div v-if="order" class="mt-8 rounded-lg border border-gray-200 p-6">
                        <h2 class="text-lg font-medium text-gray-900">Sipariş Detayları</h2>
                        <div class="mt-4 space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Sipariş No:</span>
                                <span class="font-medium">{{ order.order_number }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Tarih:</span>
                                <span class="font-medium">{{ formatDate(order.created_at) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Müşteri:</span>
                                <span class="font-medium">{{ order.customer_name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">E-posta:</span>
                                <span class="font-medium">{{ order.customer_email }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Ödeme Yöntemi:</span>
                                <span class="font-medium">
                                    <span v-if="order.payment_method === 'paytr'">Online Ödeme</span>
                                    <span v-else>Banka Havalesi</span>
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Durum:</span>
                                <span class="inline-flex rounded-full bg-green-100 px-2 py-1 text-xs font-semibold text-green-800"> Ödendi </span>
                            </div>
                            <div class="flex justify-between border-t border-gray-200 pt-3">
                                <span class="text-lg font-semibold text-gray-900">Toplam Tutar:</span>
                                <span class="text-lg font-semibold text-gray-900">{{ formatCurrency(order.total_amount) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Next Steps -->
                    <div class="mt-8 rounded-lg bg-blue-50 p-6">
                        <h3 class="text-lg font-medium text-blue-900">Sonraki Adımlar</h3>
                        <div class="mt-4 space-y-2 text-sm text-blue-800">
                            <p>✓ Siparişiniz alındı ve ödemeniz onaylandı</p>
                            <p>✓ E-posta adresinize sipariş onay maili gönderilecek</p>
                            <p>✓ Siparişiniz hazırlanmaya başlanacak</p>
                            <p>✓ Kargo bilgileri e-posta ile bildirilecek</p>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-8 flex flex-col space-y-3 sm:flex-row sm:space-x-3 sm:space-y-0">
                        <Link :href="route('home')" class="flex-1">
                            <PrimaryButton class="w-full justify-center"> Ana Sayfaya Dön </PrimaryButton>
                        </Link>

                        <Link :href="route('front.products.index')" class="flex-1">
                            <button
                                type="button"
                                class="w-full rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                            >
                                Alışverişe Devam Et
                            </button>
                        </Link>
                    </div>

                    <!-- Contact Info -->
                    <div class="mt-8 text-center text-sm text-gray-500">
                        <p>Herhangi bir sorunuz varsa, bizimle iletişime geçebilirsiniz:</p>
                        <p class="mt-1">
                            <a href="mailto:{{ appEmail }}" class="text-indigo-600 hover:text-indigo-500">
                                {{ appEmail }}
                            </a>
                            veya
                            <a href="tel:+902121234567" class="text-indigo-600 hover:text-indigo-500"> +90 212 123 45 67 </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </FrontLayout>
</template>
