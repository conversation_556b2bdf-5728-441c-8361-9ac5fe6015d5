<script setup>
import { Head } from '@inertiajs/vue3';
import { computed, ref, onMounted, onUnmounted } from 'vue';
import FrontLayout from '@/layouts/FrontLayout.vue';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    galleries: Array,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

// For lightbox functionality
const isLightboxOpen = ref(false);
const currentImageIndex = ref(0);

const openLightbox = (index) => {
    currentImageIndex.value = index;
    isLightboxOpen.value = true;
    document.body.classList.add('overflow-hidden');
};

const closeLightbox = () => {
    isLightboxOpen.value = false;
    document.body.classList.remove('overflow-hidden');
};

const nextImage = () => {
    if (props.galleries && props.galleries.length > 0) {
        currentImageIndex.value = (currentImageIndex.value + 1) % props.galleries.length;
    }
};

const prevImage = () => {
    if (props.galleries && props.galleries.length > 0) {
        currentImageIndex.value = (currentImageIndex.value - 1 + props.galleries.length) % props.galleries.length;
    }
};

// Handle keyboard events for lightbox
const handleKeyDown = (e) => {
    if (!isLightboxOpen.value) return;
    
    if (e.key === 'Escape') {
        closeLightbox();
    } else if (e.key === 'ArrowRight') {
        nextImage();
    } else if (e.key === 'ArrowLeft') {
        prevImage();
    }
};

// Add event listener when component is mounted
onMounted(() => {
    window.addEventListener('keydown', handleKeyDown);
});

// Remove event listener when component is unmounted
onUnmounted(() => {
    window.removeEventListener('keydown', handleKeyDown);
});
</script>

<template>
    <Head :title="locale === 'tr' ? 'Galeri' : 'Gallery'" />

    <FrontLayout>
        <div class="bg-white py-24 sm:py-32">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <div class="mx-auto max-w-2xl text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                        {{ locale === 'tr' ? 'Galeri' : 'Gallery' }}
                    </h2>
                    <p class="mt-2 text-lg leading-8 text-gray-600">
                        {{ locale === 'tr' ? 'Çalışmalarımızdan bir seçki' : 'A selection of our works' }}
                    </p>
                </div>
                
                <div v-if="galleries && galleries.length" class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-8 sm:grid-cols-2 lg:mx-0 lg:max-w-none lg:grid-cols-3">
                    <div 
                        v-for="(gallery, index) in galleries" 
                        :key="gallery.id" 
                        class="relative cursor-pointer overflow-hidden rounded-2xl"
                        @click="openLightbox(index)"
                    >
                        <img
                            :src="gallery.thumbnail_path || '/images/gallery-placeholder.jpg'"
                            :alt="gallery.title[locale]"
                            class="aspect-[1/1] w-full object-cover -transform duration-300 hover:scale-105"
                        />
                        <div class="absolute inset-0 flex items-end p-4 bg-gradient-to-t from-black/60 to-transparent">
                            <h3 class="text-lg font-semibold text-white">{{ gallery.title[locale] }}</h3>
                        </div>
                    </div>
                </div>
                
                <div v-else class="text-center mt-16">
                    <p class="text-gray-500">
                        {{ locale === 'tr' ? 'Henüz galeri öğesi bulunmamaktadır.' : 'No gallery items available yet.' }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Lightbox -->
        <div v-if="isLightboxOpen" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90" @click="closeLightbox">
            <div class="relative max-w-4xl max-h-screen p-4" @click.stop>
                <button @click.stop="closeLightbox" class="absolute top-4 right-4 z-10 p-2 text-white bg-black/50 rounded-full hover:bg-black/70">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
                
                <button @click.stop="prevImage" class="absolute left-4 top-1/2 -translate-y-1/2 p-2 text-white bg-black/50 rounded-full hover:bg-black/70">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                
                <button @click.stop="nextImage" class="absolute right-4 top-1/2 -translate-y-1/2 p-2 text-white bg-black/50 rounded-full hover:bg-black/70">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
                
                <div class="flex flex-col items-center">
                    <img 
                        v-if="galleries && galleries[currentImageIndex]"
                        :src="galleries[currentImageIndex].image_path || galleries[currentImageIndex].thumbnail_path || '/images/gallery-placeholder.jpg'"
                        :alt="galleries[currentImageIndex].title[locale]"
                        class="max-h-[80vh] max-w-full object-contain"
                    />
                    <div class="mt-4 text-white text-center">
                        <h3 v-if="galleries && galleries[currentImageIndex]" class="text-xl font-semibold">
                            {{ galleries[currentImageIndex].title[locale] }}
                        </h3>
                        <p v-if="galleries && galleries[currentImageIndex] && galleries[currentImageIndex].description" class="mt-2">
                            {{ galleries[currentImageIndex].description[locale] }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </FrontLayout>
</template>
