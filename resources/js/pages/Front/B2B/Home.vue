<script setup lang="ts">
import B2BLayout from '@/layouts/B2BLayout.vue';
import { ref, watch } from 'vue';
// Lodash debounce fonksiyonu
import axios from 'axios';
import debounce from 'lodash/debounce';
// Arama metni
const searchValue = ref('');
// Yükleniyor durumu
const isLoading = ref(false);

// Ürün listesi (örnek tip tanımlaması)
const searchResults = ref<
    Array<{
        id: number;
        sku: string;
        name: string;
        unit: string;
        price: number;
        quantity: number;
    }>
>([]);

// Gerçek (veya sahte) API'den verileri getirir
const fetchProducts = async (query: string) => {
    isLoading.value = true;
    try {
        // Örnek gecikme simülasyonu
        await new Promise((resolve) => setTimeout(resolve, 300));

        // Sahte veriler
        const mockData = [
            { id: 1, sku: 'PRD001', name: 'Ürün 1', unit: 'Adet', price: 100, quantity: 1 },
            { id: 2, sku: 'PRD002', name: 'Ürün 2', unit: 'Koli', price: 250, quantity: 1 },
        ];

        // İstiyorsanız basit bir filtre uygulayabilirsiniz
        // (Gerçek projede backend'e query gönderip cevap almanız daha mantıklı)
        const filtered = mockData.filter((item) => item.name.toLowerCase().includes(query.toLowerCase()) || item.sku.toLowerCase().includes(query.toLowerCase()));

        searchResults.value = filtered;
    } catch (error) {
        console.error('Ürün arama hatası:', error);
    } finally {
        isLoading.value = false;
    }
};

// Debounce edilmiş arama fonksiyonu (500ms gecikme)
const debouncedSearch = debounce((val: string) => {
    fetchProducts(val);
}, 100);

// searchValue değiştikçe otomatik ara
watch(searchValue, (newVal) => {
    debouncedSearch(newVal);
});

// Sepete ekle fonksiyonu
const addToBasket = async (product: any) => {
    const response = await axios.post('/b2b/basket/add', {
        sku: product.sku,
        name: product.name,
        price: product.price,
        quantity: product.quantity,
    });
    if (response.status === 200) {
        console.log('Ürün sepete başarıyla eklendi:', response.data);
    } else {
        console.warn('Beklenmeyen yanıt kodu:', response.status, response.data);
    }
};
</script>

<template>
    <B2BLayout>
        <div class="bg-white py-6 sm:py-8">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <!-- Arama Alanı -->
                <div class="mb-4 flex items-center space-x-2">
                    <input type="text" v-model="searchValue" placeholder="Ürün ara..." class="w-64 rounded border border-gray-300 px-3 py-2 focus:outline-none focus:ring-1 focus:ring-theme-primary" />
                </div>

                <!-- Yükleniyor -->
                <div v-if="isLoading" class="py-4 text-center">
                    <!-- Spinner -->
                    <div class="mx-auto h-8 w-8 animate-spin rounded-full border-4 border-theme-primary border-t-transparent"></div>
                </div>

                <!-- Sonuç Tablosu -->
                <div v-else-if="searchResults.length > 0">
                    <table class="min-w-full divide-y divide-gray-200 border border-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-600">Kod</th>
                                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-600">Ürün Adı</th>
                                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-600">Birim</th>
                                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-600">Fiyat</th>
                                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-600">Miktar</th>
                                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-600"></th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <tr v-for="product in searchResults" :key="product.id" class="hover:bg-gray-50">
                                <td class="whitespace-nowrap px-4 py-2">{{ product.sku }}</td>
                                <td class="px-4 py-2">{{ product.name }}</td>
                                <td class="whitespace-nowrap px-4 py-2">{{ product.unit }}</td>
                                <td class="px-4 py-2">{{ product.price }} TL</td>
                                <td class="px-4 py-2">
                                    <!-- Her ürünün kendi quantity'si -->
                                    <input type="number" class="w-16 rounded border border-gray-300 px-2 py-1 focus:outline-none" v-model.number="product.quantity" min="1" />
                                </td>
                                <td class="whitespace-nowrap px-4 py-2">
                                    <button class="rounded bg-green-600 px-4 py-2 text-white hover:bg-green-700" @click="addToBasket(product)">Sepete Ekle</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Sonuç yok -->
                <div v-else class="py-4 text-center text-gray-500">Arama sonucu bulunamadı</div>
            </div>
        </div>
    </B2BLayout>
</template>
