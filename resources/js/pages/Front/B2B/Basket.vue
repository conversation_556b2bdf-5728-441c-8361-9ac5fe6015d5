<script setup lang="ts">
import B2BLayout from '@/layouts/B2BLayout.vue';
import { onMounted, ref, watch } from 'vue';
// Lodash debounce fonksiyonu
import axios from 'axios';
// <PERSON><PERSON> metni
// Yükleniyor durumu
const isLoading = ref(false);

 
// Gerçek (veya sahte) API'den verileri getirir
const basketData = ref<Array<{
  sku: string;
  name: string;
  price: number;
  quantity: number;
  // Diğer alanlar varsa buraya ekleyin
}>>([]);

// Sunucudan verileri çekme fonksiyonu
const fetchCard = async () => {
  isLoading.value = true;
  try {
    const response = await axios.get('/b2b/basket/get');
    // response.data yapısına göre uyarlayın:
    // Eğer veriler doğrudan response.data içindeyse:
    basketData.value = response.data.basket;
    // Eğer veriler response.data.data içindeyse:
    // cardData.value = response.data.data;
  } catch (error) {
    console.error('<PERSON><PERSON><PERSON> alınırken hata:', error);
  } finally {
    isLoading.value = false;
  }
};

// Bileşen yüklendiğinde verileri çek
onMounted(() => {
  fetchCard();
});
</script>

<template>
  <B2BLayout>
    <div class="bg-white py-6 sm:py-8">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        
        <!-- Yükleniyor -->
        <div v-if="isLoading" class="text-center py-4">
          <!-- Spinner -->
          <div class="w-8 h-8 rounded-full border-4 border-blue-600 border-t-transparent mx-auto animate-spin"></div>
        </div>

        <!-- Tablo (veriler geldiyse) -->
        <div v-else>
          <table class="min-w-full divide-y divide-gray-200 border">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-600">SKU</th>
                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-600">Kod</th>
                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-600">Fiyat</th>
                <th class="px-4 py-2 text-left text-sm font-semibold text-gray-600">Adı</th>

              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <!-- cardData dizisindeki her bir öğeyi satır olarak gösterir -->
              <tr
                v-for="(item, index) in basketData"
                :key="index"
                class="hover:bg-gray-50"
              >
                <td class="px-4 py-2 whitespace-nowrap">{{ item.sku }}</td>
                <td class="px-4 py-2 whitespace-nowrap">{{ item.name }}</td>
                <td class="px-4 py-2 whitespace-nowrap">{{ item.price }}</td>
                <td class="px-4 py-2 whitespace-nowrap">{{ item.quantity }}</td>

              </tr>
            </tbody>
          </table>
        </div>

      </div>
    </div>
  </B2BLayout>
</template>
