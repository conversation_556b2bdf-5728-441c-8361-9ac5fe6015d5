<script setup>
import { Head } from '@inertiajs/vue3';
import FrontLayout from '@/layouts/FrontLayout.vue';
import { computed } from 'vue';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    page: Object,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
</script>

<template>
    <Head :title="page.title[locale]" />

    <FrontLayout>
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <h1 class="text-3xl font-bold mb-6">{{ page.title[locale] }}</h1>
                        
                        <div class="prose max-w-none" v-html="page.content[locale]"></div>
                    </div>
                </div>
            </div>
        </div>
    </FrontLayout>
</template>
