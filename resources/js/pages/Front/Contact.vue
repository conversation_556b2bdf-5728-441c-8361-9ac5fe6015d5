<script setup lang="js">
import FrontLayout from '@/layouts/FrontLayout.vue';
import { useLocaleStore } from '@/Stores/locale';
import { Head, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';
const appPhone = import.meta.env.VITE_APP_PHONE || 'Laravel';

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
const appAddress = import.meta.env.VITE_APP_ADDRESS || 'Laravel';

const form = useForm({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
});

const submit = () => {
    form.post(route('front.contact.submit'), {
        onSuccess: () => form.reset(),
    });
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'İletişim' : 'Contact'" />

    <FrontLayout>
        <div class="bg-white py-24 sm:py-32">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <div class="mx-auto max-w-2xl text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                        {{ locale === 'tr' ? 'İletişim' : 'Contact Us' }}
                    </h2>
                    <p class="mt-2 text-lg leading-8 text-gray-600">
                        {{ locale === 'tr' ? 'Sorularınız için bizimle iletişime geçin' : 'Get in touch with us for any questions' }}
                    </p>
                </div>

                <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-2">
                    <!-- Contact Form -->
                    <div class="rounded-2xl bg-white p-8 shadow-lg ring-1 ring-gray-200">
                        <form @submit.prevent="submit" class="space-y-6">
                            <div v-if="form.recentlySuccessful" class="mb-4 rounded-md bg-green-50 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-green-800">
                                            {{ locale === 'tr' ? 'Mesajınız başarıyla gönderildi!' : 'Your message has been sent successfully!' }}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label for="name" class="block text-sm font-medium leading-6 text-gray-900">
                                    {{ locale === 'tr' ? 'İsim' : 'Name' }}
                                </label>
                                <div class="mt-2">
                                    <input
                                        type="text"
                                        id="name"
                                        v-model="form.name"
                                        :class="{ 'border-red-500': form.errors.name }"
                                        class="block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                        required
                                    />
                                    <div v-if="form.errors.name" class="mt-1 text-sm text-red-500">{{ form.errors.name }}</div>
                                </div>
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium leading-6 text-gray-900">
                                    {{ locale === 'tr' ? 'E-posta' : 'Email' }}
                                </label>
                                <div class="mt-2">
                                    <input
                                        type="email"
                                        id="email"
                                        v-model="form.email"
                                        :class="{ 'border-red-500': form.errors.email }"
                                        class="block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                        required
                                    />
                                    <div v-if="form.errors.email" class="mt-1 text-sm text-red-500">{{ form.errors.email }}</div>
                                </div>
                            </div>

                            <div>
                                <label for="phone" class="block text-sm font-medium leading-6 text-gray-900">
                                    {{ locale === 'tr' ? 'Telefon' : 'Phone' }}
                                </label>
                                <div class="mt-2">
                                    <input
                                        type="tel"
                                        id="phone"
                                        v-model="form.phone"
                                        :class="{ 'border-red-500': form.errors.phone }"
                                        class="block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    />
                                    <div v-if="form.errors.phone" class="mt-1 text-sm text-red-500">{{ form.errors.phone }}</div>
                                </div>
                            </div>

                            <div>
                                <label for="subject" class="block text-sm font-medium leading-6 text-gray-900">
                                    {{ locale === 'tr' ? 'Konu' : 'Subject' }}
                                </label>
                                <div class="mt-2">
                                    <input
                                        type="text"
                                        id="subject"
                                        v-model="form.subject"
                                        :class="{ 'border-red-500': form.errors.subject }"
                                        class="block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    />
                                    <div v-if="form.errors.subject" class="mt-1 text-sm text-red-500">{{ form.errors.subject }}</div>
                                </div>
                            </div>

                            <div>
                                <label for="message" class="block text-sm font-medium leading-6 text-gray-900">
                                    {{ locale === 'tr' ? 'Mesaj' : 'Message' }}
                                </label>
                                <div class="mt-2">
                                    <textarea
                                        id="message"
                                        v-model="form.message"
                                        :class="{ 'border-red-500': form.errors.message }"
                                        rows="4"
                                        class="block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                        required
                                    ></textarea>
                                    <div v-if="form.errors.message" class="mt-1 text-sm text-red-500">{{ form.errors.message }}</div>
                                </div>
                            </div>

                            <div>
                                <button
                                    type="submit"
                                    :disabled="form.processing"
                                    class="block w-full rounded-md bg-[#56509a] px-3.5 py-2.5 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-75"
                                >
                                    {{ locale === 'tr' ? 'Gönder' : 'Send' }}
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Contact Information -->
                    <div class="rounded-2xl bg-gray-50 p-8">
                        <h3 class="text-xl font-semibold text-gray-900">
                            {{ locale === 'tr' ? 'İletişim Bilgileri' : 'Contact Information' }}
                        </h3>

                        <dl class="mt-6 space-y-4">
                            <div class="flex gap-x-4">
                                <dt class="flex-none">
                                    <span class="sr-only">Address</span>
                                    <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008z"
                                        />
                                    </svg>
                                </dt>
                                <dd class="text-sm leading-7 text-gray-600">
                                    <address class="not-italic">
                                        {{ appAddress }}
                                    </address>
                                </dd>
                            </div>

                            <div class="flex gap-x-4">
                                <dt class="flex-none">
                                    <span class="sr-only">Telephone</span>
                                    <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z"
                                        />
                                    </svg>
                                </dt>
                                <dd class="text-sm leading-7 text-gray-600">
                                    <a class="hover:text-gray-900" href="tel:{{ appPhone }}">{{ appPhone }}</a>
                                </dd>
                            </div>

                            <div class="flex gap-x-4">
                                <dt class="flex-none">
                                    <span class="sr-only">Email</span>
                                    <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75"
                                        />
                                    </svg>
                                </dt>
                                <dd class="text-sm leading-7 text-gray-600">
                                    <a class="hover:text-gray-900" href="mailto:{{ appEmail }}">{{ appEmail }}</a>
                                </dd>
                            </div>
                        </dl>

                        <div class="mt-8">
                            <h4 class="text-base font-semibold text-gray-900">
                                {{ locale === 'tr' ? 'Çalışma Saatleri' : 'Working Hours' }}
                            </h4>
                            <dl class="mt-4 space-y-2 text-sm leading-6 text-gray-600">
                                <div class="flex justify-between">
                                    <dt>{{ locale === 'tr' ? 'Pazartesi - Cuma' : 'Monday - Friday' }}</dt>
                                    <dd>9:00 - 18:00</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt>{{ locale === 'tr' ? 'Cumartesi' : 'Saturday' }}</dt>
                                    <dd>10:00 - 14:00</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt>{{ locale === 'tr' ? 'Pazar' : 'Sunday' }}</dt>
                                    <dd>{{ locale === 'tr' ? 'Kapalı' : 'Closed' }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>

                <!-- Map -->
                <div class="mt-16 overflow-hidden rounded-lg">
                    <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3010.2763256169117!2d28.9783446!3d41.0082376!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14cab7650656bd63%3A0x8ca058b28c20b6c3!2zVGFrc2ltIE1leWRhbsSxLCBHw7xtw7zFn3N1eXUsIDM0NDM1IEJleW_En2x1L8Swc3RhbmJ1bA!5e0!3m2!1str!2str!4v1645554852986!5m2!1str!2str"
                        width="100%"
                        height="450"
                        style="border: 0"
                        allowfullscreen=""
                        loading="lazy"
                    ></iframe>
                </div>
            </div>
        </div>
    </FrontLayout>
</template>
