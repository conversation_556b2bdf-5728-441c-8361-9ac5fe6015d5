<script setup>
import FrontLayout from '@/layouts/FrontLayout.vue';
import { useCartStore } from '@/stores/cart';
import { useLocaleStore } from '@/Stores/locale';
import { Head, Link } from '@inertiajs/vue3';
import { computed } from 'vue';

const props = defineProps({
    products: Array,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
const cartStore = useCartStore();
// const { getInlineStyles } = useTheme(); // Geçici olarak devre dışı

// Currency formatter for Turkish Lira
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY',
        minimumFractionDigits: 2,
    }).format(amount);
};

const addToCart = (product) => {
    cartStore.addItem(product, 1);
    // Show success message
    alert(locale.value === 'tr' ? 'Ürün sepete eklendi!' : 'Product added to cart!');
};

// Get product image with fallback
const getProductImage = (product) => {
    // Try primary_image first (order = 0), then min_order_image_path
    if (product.primary_image) {
        return product.primary_image;
    }
    if (product.min_order_image_path) {
        return product.min_order_image_path;
    }
    // Fallback to placeholder image
    return '/images/placeholder-product.svg';
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'Ürünler' : 'Products'" />

    <FrontLayout>
        <div class="bg-white py-24 sm:py-32">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <div class="mx-auto max-w-2xl text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                        {{ locale === 'tr' ? 'Ürünlerimiz' : 'Our Products' }}
                    </h2>
                    <p class="mt-2 text-lg leading-8 text-gray-600">
                        {{ locale === 'tr' ? 'Tüm ürünlerimizi keşfedin' : 'Discover all our products' }}
                    </p>
                </div>

                <!-- Product Grid - 3 Column Layout -->
                <div class="mt-16 grid grid-cols-1 gap-x-8 gap-y-10 sm:grid-cols-2 lg:grid-cols-3">
                    <div v-for="product in products" :key="product.id" class="group">
                        <div class="aspect-h-1 aspect-w-1 w-full overflow-hidden rounded-lg bg-gray-100">
                            <img :src="getProductImage(product)" :alt="product.name[locale] || product.name" class="h-full w-full object-cover object-center group-hover:opacity-75" @error="$event.target.src = '/images/placeholder-product.svg'" />
                        </div>
                        <div class="mt-4 text-center">
                            <h3 class="text-lg font-medium text-gray-900">
                                <Link :href="route('front.products.show', product.slug)" class="hover:text-blue-600">
                                    {{ product.name[locale] }}
                                </Link>
                            </h3>
                            <p v-if="product.price" class="mt-2 text-xl font-semibold text-blue-600">
                                {{ formatCurrency(product.price) }}
                            </p>
                            <div class="mt-4 space-y-2">
                                <Link :href="route('front.products.show', product.slug)" class="block w-full rounded-md bg-gray-100 px-4 py-2 text-center font-medium text-gray-900 transition-colors hover:bg-gray-200">
                                    {{ locale === 'tr' ? 'Detayları Gör' : 'View Details' }}
                                </Link>
                                <button v-if="product.price" @click="addToCart(product)" class="bg-theme-primary hover:bg-theme-secondary w-full rounded-md px-4 py-2 font-medium text-white transition-colors">
                                    {{ locale === 'tr' ? 'Sepete Ekle' : 'Add to Cart' }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- No Products Message -->
                <div v-if="!products || products.length === 0" class="mt-16 text-center">
                    <p class="text-gray-500">
                        {{ locale === 'tr' ? 'Henüz ürün bulunmamaktadır.' : 'No products found.' }}
                    </p>
                </div>
            </div>
        </div>
    </FrontLayout>
</template>
