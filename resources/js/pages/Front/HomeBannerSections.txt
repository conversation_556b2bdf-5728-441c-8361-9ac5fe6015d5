<!-- Banner sections to be added to Home.vue -->

<!-- Banner 1 placement (above featured products) - Add after the section title/description and before the featured products grid -->
<div v-if="banner1 && banner1.length > 0" class="mt-8 mb-12">
    <BannerSlider :banners="banner1" :autoplay="true" :interval="6000" />
</div>

<!-- Banner 2 placement (between sections) - Add after the featured products section and before the gallery section -->
<div v-if="banner2 && banner2.length > 0" class="py-12 bg-gray-50">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <BannerSlider :banners="banner2" :autoplay="true" :interval="7000" />
    </div>
</div>

<!-- Banner 4 placement (bottom of page) - Add at the end of the page, before the closing FrontLayout tag -->
<div v-if="banner4 && banner4.length > 0" class="py-16 bg-white">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <BannerSlider :banners="banner4" :autoplay="true" :interval="8000" />
    </div>
</div>
