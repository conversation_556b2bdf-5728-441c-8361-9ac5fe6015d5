<script setup lang="ts">
import FrontLayout from '@/layouts/FrontLayout.vue';
import { useLocaleStore } from '@/Stores/locale';
import { Head, useForm } from '@inertiajs/vue3';
import { bakiyeOmur } from '@/lib/bakiyeOmur';
import { ref, computed } from 'vue';


const form = useForm({
  dogumGun: '',
  dogumAy: '',
  dogumYil: '',
  kazaGun: '',
  kazaAy: '',
  kazaYil: '',
  cinsiyet: ''
});

const hesaplananBakiyeOmur = ref(null);
const hesaplamaYapildi = ref(false);
const hataVar = ref(false);
const hataMesaji = ref('');

// Tarih kontrolü için gerekli değişkenler
const bugunTarih = new Date();
const bugunYil = bugunTarih.getFullYear();
const bugunAy = bugunTarih.getMonth() + 1;
const bugunGun = bugunTarih.getDate();

// Tarih tipini seçmek için değişken
const tarihTipi = ref("kaza"); // "kaza" veya "rapor"

/**
 * Doğum tarihi ve kaza tarihi arasındaki yaşı yıl ve gün olarak hesaplar
 * @param {Object} dogumTarihi - Doğum tarihi (Date nesnesi)
 * @param {Object} kazaTarihi - Kaza tarihi (Date nesnesi)
 * @returns {Object} Hesaplanan yaş bilgileri {tamYil, gunSayisi, toplamGun}
 */
const yasHesapla = (dogumTarihi, kazaTarihi) => {
  // Aynı tarih olma durumunu kontrol et
  if (dogumTarihi.getTime() === kazaTarihi.getTime()) {
    return { tamYil: 0, gunSayisi: 0, toplamGun: 0 };
  }

  // Milisaniye cinsinden farkı hesapla
  const farkMs = kazaTarihi.getTime() - dogumTarihi.getTime();

  // Toplam gün sayısını hesapla (Milisaniyeyi güne çevir)
  const toplamGun = Math.floor(farkMs / (1000 * 60 * 60 * 24));

  // Tam yılları hesapla
  let tamYil = 0;

  // Doğum günü ayarlaması yapılmış tarih (yıla göre)
  const yilDonumuTarihi = new Date(dogumTarihi);

  // Tam yılları hesapla
  while (true) {
    yilDonumuTarihi.setFullYear(yilDonumuTarihi.getFullYear() + 1);

    // Eğer yıldönümü kaza tarihinden sonraysa döngüden çık
    if (yilDonumuTarihi > kazaTarihi) {
      break;
    }

    tamYil++;
  }

  // Son yıldaki gün sayısını hesapla
  // Bir önceki yıldönümünü bul
  const sonYilDonumu = new Date(dogumTarihi);
  sonYilDonumu.setFullYear(dogumTarihi.getFullYear() + tamYil);

  // Son yıldönümünden kaza tarihine kadar geçen gün sayısı
  const gunSayisi = Math.floor((kazaTarihi - sonYilDonumu) / (1000 * 60 * 60 * 24));

  return {
    tamYil,        // Tam yıl (tamamlanmış yıllar)
    gunSayisi,     // Son yılda geçen gün sayısı
    toplamGun      // Toplam gün sayısı
  };
};

// Artık yıl kontrolü için yardımcı fonksiyon
const isLeapYear = (year) => {
  return ((year % 4 === 0) && (year % 100 !== 0)) || (year % 400 === 0);
};

// Yeni hesaplama mantığı için yardımcı fonksiyon
const getBakiyeOmurByYasVeGun = (yas, gunler, cinsiyet) => {
  // Eğer yaş tabloda yoksa (97'den büyükse)
  if (yas >= 97) {
    return bakiyeOmur.data[97][cinsiyet];
  }

  // Eğer yaş 0'dan küçükse (olmaz ama kontrol edelim)
  if (yas < 0) {
    return bakiyeOmur.data[0][cinsiyet];
  }

  // Şimdiki yaş için bakiye ömür değeri
  const mevcutBakiyeOmur = bakiyeOmur.data[yas][cinsiyet];

  // Bir sonraki yaş için bakiye ömür değeri (97'den büyük olamaz)
  const sonrakiYas = Math.min(yas + 1, 97);
  const sonrakiBakiyeOmur = bakiyeOmur.data[sonrakiYas][cinsiyet];

  // İki yaş arasındaki bakiye ömür farkı
  const fark = mevcutBakiyeOmur - sonrakiBakiyeOmur;

  // Yılın kaçta kaçı geçmiş hesabı (bir yıl 365.25 gün kabul edilir)
  const yilOrani = gunler / 365.25;

  // Oransal olarak çekilen bakiye ömür
  const indirgenmisOmur = fark * yilOrani;

  // Sonuç = Mevcut bakiye ömür - indirgenen miktar
  const sonuc = mevcutBakiyeOmur - indirgenmisOmur;

  return sonuc;
};

// İstenen bakiye ömür hesaplama fonksiyonu
const hesaplaBakiyeOmur = () => {
  hataVar.value = false;

  // Formu doğrula
  if (!form.dogumGun || !form.dogumAy || !form.dogumYil ||
      !form.kazaGun || !form.kazaAy || !form.kazaYil ||
      !form.cinsiyet) {
    hataVar.value = true;
    hataMesaji.value = 'Lütfen tüm alanları doldurunuz.';
    return;
  }

  try {
    // Tarihleri ayıkla
    const dogumTarihi = new Date(
      parseInt(form.dogumYil),
      parseInt(form.dogumAy) - 1, // JavaScript'te aylar 0-11 arası
      parseInt(form.dogumGun)
    );
    const kazaTarihi = new Date(
      parseInt(form.kazaYil),
      parseInt(form.kazaAy) - 1,
      parseInt(form.kazaGun)
    );

    // Geçerli tarihler mi kontrol et
    if (isNaN(dogumTarihi.getTime()) || isNaN(kazaTarihi.getTime())) {
      hataVar.value = true;
      hataMesaji.value = 'Geçersiz tarih formatı.';
      return;
    }

    // Kaza tarihi doğum tarihinden sonra mı kontrol et
    if (kazaTarihi < dogumTarihi) {
      hataVar.value = true;
      hataMesaji.value = 'Kaza tarihi doğum tarihinden önce olamaz.';
      return;
    }

    // Kaza anındaki yaşı hassas şekilde hesapla
    const yasBilgisi = yasHesapla(dogumTarihi, kazaTarihi);
    const kazaAnindakiYas = yasBilgisi.tamYil;
    const gunSayisi = yasBilgisi.gunSayisi;

    // Bakiye ömür tablosunda bu yaş var mı kontrol et
    if (kazaAnindakiYas < 0 || kazaAnindakiYas > 97) {
      hataVar.value = true;
      hataMesaji.value = 'Hesaplama için yaş 0-97 arasında olmalıdır.';
      return;
    }

    // İstenen yeni hesaplama mantığı:
    // 1. Kaza tarihindeki yaşını yıl olarak bul
    const yas = kazaAnindakiYas;

    // 2. Bakiye ömür tablosundan o yıla ait veriyi cinsiyetine göre çek
    const bakiyeOmurSimdi = bakiyeOmur.data[yas][form.cinsiyet];

    // 3. Kaza anındaki yaşına 1 yıl ekle ve bakiye ömür tablosundaki cinsiyete göre veriyi çek
    const sonrakiYas = Math.min(yas + 1, 97); // 97'den büyük olamaz
    const bakiyeOmurSonraki = bakiyeOmur.data[sonrakiYas][form.cinsiyet];

    // 4. Gelen iki veri nin farkını al
    const fark = bakiyeOmurSimdi - bakiyeOmurSonraki;

    // 5. Günleri aya çevirip (3.6 ile gün * ay) günler için pay çıkar
    const ayFaktoru = gunSayisi / 30; // Kabaca 1 ay 30 gün
    const gunDuzeltmePayi = (fark / 12) * ayFaktoru;

    // 6. Kaza tarihindeki bakiye ömürden çıkartarak final bakiye ömrü bul
    const finalBakiyeOmur = bakiyeOmurSimdi - gunDuzeltmePayi;

    // Alternatif, daha doğru hesaplama:
    const hassasBakiyeOmur = getBakiyeOmurByYasVeGun(yas, gunSayisi, form.cinsiyet);

    // Sonucu hazırla
    hesaplananBakiyeOmur.value = {
      yas: kazaAnindakiYas,
      gunSayisi: gunSayisi,
      toplamGun: yasBilgisi.toplamGun,
      omurStandart: bakiyeOmurSimdi,
      omurHassas: hassasBakiyeOmur,
      omur: finalBakiyeOmur, // İstenen hesaplama
      yil: Math.floor(finalBakiyeOmur),
      ay: Math.round((finalBakiyeOmur - Math.floor(finalBakiyeOmur)) * 12),
      // Hesaplama detayları
      bakiyeOmurSimdi,
      bakiyeOmurSonraki,
      fark,
      gunDuzeltmePayi
    };

    hesaplamaYapildi.value = true;
  } catch (error) {
    console.error("Hesaplama hatası:", error);
    hataVar.value = true;
    hataMesaji.value = 'Hesaplama sırasında bir hata oluştu: ' + error.message;
  }
};

const formSifirla = () => {
  form.reset();
  hesaplamaYapildi.value = false;
  hesaplananBakiyeOmur.value = null;
  hataVar.value = false;
};
</script>

<template>
  <Head title="Bakiye Ömür Tablosu, Yaşam Tablosu" />
  <FrontLayout>
    <div class="bg-white py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center mb-6">
          <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Bakiye Yaşam Tablosu
          </h2>
          <p class="mt-4 text-xl leading-8 text-gray-600">
            Kaza veya vaka tarihinde bakiye ömür hesaplaması yapabilirsiniz.
          </p>
        </div>

        <div class="mx-auto mt-16 max-w-2xl">
          <!-- Form Alanı -->
          <div class="bg-white rounded-lg shadow-lg p-10 border border-gray-200">
            <form @submit.prevent="hesaplaBakiyeOmur" class="space-y-8">
              <!-- Doğum Tarihi -->
              <div>
                <label class="block text-base font-medium text-gray-700 mb-3">
                  Doğum Tarihi
                </label>
                <div class="grid grid-cols-3 gap-4">
                  <div>
                    <label for="dogumGun" class="block text-sm text-gray-500 mb-2">Gün</label>
                    <select
                      id="dogumGun"
                      v-model="form.dogumGun"
                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 text-base"
                      required
                    >
                      <option value="">Gün</option>
                      <option v-for="gun in 31" :key="'dogum-gun-'+gun" :value="gun < 10 ? '0' + gun : ''+gun">
                        {{ gun }}
                      </option>
                    </select>
                  </div>
                  <div>
                    <label for="dogumAy" class="block text-sm text-gray-500 mb-2">Ay</label>
                    <select
                      id="dogumAy"
                      v-model="form.dogumAy"
                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 text-base"
                      required
                    >
                      <option value="">Ay</option>
                      <option value="01">Ocak</option>
                      <option value="02">Şubat</option>
                      <option value="03">Mart</option>
                      <option value="04">Nisan</option>
                      <option value="05">Mayıs</option>
                      <option value="06">Haziran</option>
                      <option value="07">Temmuz</option>
                      <option value="08">Ağustos</option>
                      <option value="09">Eylül</option>
                      <option value="10">Ekim</option>
                      <option value="11">Kasım</option>
                      <option value="12">Aralık</option>
                    </select>
                  </div>
                  <div>
                    <label for="dogumYil" class="block text-sm text-gray-500 mb-2">Yıl</label>
                    <select
                      id="dogumYil"
                      v-model="form.dogumYil"
                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 text-base"
                      required
                    >
                      <option value="">Yıl</option>
                      <option v-for="yil in 100" :key="'dogum-yil-'+yil" :value="''+(new Date().getFullYear() - yil + 1)">
                        {{ new Date().getFullYear() - yil + 1 }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- Kaza/Rapor Tarihi -->
              <div>
                <div class="flex items-center mb-3">
                  <label class="block text-base font-medium text-gray-700 mr-4">
                    Tarih Tipi
                  </label>
                  <div class="flex space-x-6">
                    <label class="inline-flex items-center cursor-pointer">
                      <input
                        type="radio"
                        v-model="tarihTipi"
                        value="kaza"
                        class="h-5 w-5 text-indigo-600 focus:ring-indigo-500"
                      />
                      <span class="ml-2 text-base text-gray-700">Kaza / Vaka Tarihi</span>
                    </label>
                    <label class="inline-flex items-center cursor-pointer">
                      <input
                        type="radio"
                        v-model="tarihTipi"
                        value="rapor"
                        class="h-5 w-5 text-indigo-600 focus:ring-indigo-500"
                      />
                      <span class="ml-2 text-base text-gray-700">Rapor Tarihi</span>
                    </label>
                  </div>
                </div>

                <label class="block text-base font-medium text-gray-700 mb-3">
                  {{ tarihTipi === "kaza" ? "Kaza / Vaka Tarihi" : "Rapor Tarihi" }}
                </label>
                <div class="grid grid-cols-3 gap-4">
                  <div>
                    <label for="kazaGun" class="block text-sm text-gray-500 mb-2">Gün</label>
                    <select
                      id="kazaGun"
                      v-model="form.kazaGun"
                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 text-base"
                      required
                    >
                      <option value="">Gün</option>
                      <option v-for="gun in 31" :key="'kaza-gun-'+gun" :value="gun < 10 ? '0' + gun : ''+gun">
                        {{ gun }}
                      </option>
                    </select>
                  </div>
                  <div>
                    <label for="kazaAy" class="block text-sm text-gray-500 mb-2">Ay</label>
                    <select
                      id="kazaAy"
                      v-model="form.kazaAy"
                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 text-base"
                      required
                    >
                      <option value="">Ay</option>
                      <option value="01">Ocak</option>
                      <option value="02">Şubat</option>
                      <option value="03">Mart</option>
                      <option value="04">Nisan</option>
                      <option value="05">Mayıs</option>
                      <option value="06">Haziran</option>
                      <option value="07">Temmuz</option>
                      <option value="08">Ağustos</option>
                      <option value="09">Eylül</option>
                      <option value="10">Ekim</option>
                      <option value="11">Kasım</option>
                      <option value="12">Aralık</option>
                    </select>
                  </div>
                  <div>
                    <label for="kazaYil" class="block text-sm text-gray-500 mb-2">Yıl</label>
                    <select
                      id="kazaYil"
                      v-model="form.kazaYil"
                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 text-base"
                      required
                    >
                      <option value="">Yıl</option>
                      <option v-for="yil in 100" :key="'kaza-yil-'+yil" :value="''+(new Date().getFullYear() - yil + 1)">
                        {{ new Date().getFullYear() - yil + 1 }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- Cinsiyet -->
              <div>
                <label class="block text-base font-medium text-gray-700 mb-3">
                  Cinsiyet
                </label>
                <div class="flex space-x-8 mt-2">
                  <div class="flex items-center">
                    <input
                      id="cinsiyet-erkek"
                      name="cinsiyet"
                      v-model="form.cinsiyet"
                      type="radio"
                      value="erkek"
                      class="h-6 w-6 text-indigo-600 focus:ring-indigo-500"
                      required
                    />
                    <label for="cinsiyet-erkek" class="ml-3 block text-base text-gray-700">
                      Erkek
                    </label>
                  </div>
                  <div class="flex items-center">
                    <input
                      id="cinsiyet-kadin"
                      name="cinsiyet"
                      v-model="form.cinsiyet"
                      type="radio"
                      value="kadin"
                      class="h-6 w-6 text-indigo-600 focus:ring-indigo-500"
                    />
                    <label for="cinsiyet-kadin" class="ml-3 block text-base text-gray-700">
                      Kadın
                    </label>
                  </div>
                </div>
              </div>

              <!-- Hata Mesajı -->
              <div v-if="hataVar" class="bg-red-50 border-l-4 border-red-500 p-5 mt-5">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-base text-red-700">
                      {{ hataMesaji }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Butonlar -->
              <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-100">
                <button
                  type="button"
                  @click="formSifirla"
                  class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Temizle
                </button>
                <button
                  type="submit"
                  class="inline-flex items-center px-8 py-4 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Hesapla
                </button>
              </div>
            </form>
          </div>

          <!-- Sonuç Alanı -->
          <div v-if="hesaplamaYapildi" class="mt-10 bg-indigo-50 rounded-lg shadow-lg p-8 border border-indigo-100">
            <h3 class="text-xl font-semibold text-indigo-800 mb-5">Hesaplama Sonucu</h3>

            <div class="bg-white rounded-md p-6 shadow-sm border border-indigo-100">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-3">
                  <p class="text-base text-gray-500">{{ tarihTipi === "kaza" ? "Kaza/Vaka" : "Rapor" }} Anındaki Yaş</p>
                  <p class="text-2xl font-bold text-gray-900">
                    {{ hesaplananBakiyeOmur.yas }} yıl {{ hesaplananBakiyeOmur.gunSayisi }} gün
                  </p>
                </div>

                <div class="space-y-3">
                  <p class="text-base text-gray-500">Bakiye Ömür</p>
                  <p class="text-2xl font-bold text-indigo-700">
                    {{ hesaplananBakiyeOmur.yil }} yıl
                    <span v-if="hesaplananBakiyeOmur.ay > 0">{{ hesaplananBakiyeOmur.ay }} ay</span>
                  </p>
                </div>
              </div>

              <div class="mt-8 space-y-4">
                <div class="p-4 bg-gray-50 rounded-md">
                  <h4 class="font-medium text-gray-700 mb-2">Detaylı Hesaplama Bilgileri</h4>
                  <ul class="space-y-2 text-sm">
                    <li class="flex justify-between">
                      <span class="text-gray-600">Doğum Tarihi:</span>
                      <span class="font-medium">{{ form.dogumGun }}/{{ form.dogumAy }}/{{ form.dogumYil }}</span>
                    </li>
                    <li class="flex justify-between">
                      <span class="text-gray-600">{{ tarihTipi === "kaza" ? "Kaza/Vaka" : "Rapor" }} Tarihi:</span>
                      <span class="font-medium">{{ form.kazaGun }}/{{ form.kazaAy }}/{{ form.kazaYil }}</span>
                    </li>
                    <li class="flex justify-between">
                      <span class="text-gray-600">Yaş (Tam Yıl):</span>
                      <span class="font-medium">{{ hesaplananBakiyeOmur.yas }} yıl</span>
                    </li>
                    <li class="flex justify-between">
                      <span class="text-gray-600">Son Yılda Geçen Gün:</span>
                      <span class="font-medium">{{ hesaplananBakiyeOmur.gunSayisi }} gün</span>
                    </li>
                    <li class="flex justify-between">
                      <span class="text-gray-600">{{ hesaplananBakiyeOmur.yas }} yaşındaki bakiye ömür:</span>
                      <span class="font-medium">{{ hesaplananBakiyeOmur.bakiyeOmurSimdi.toFixed(4) }} yıl</span>
                    </li>
                    <li class="flex justify-between">
                      <span class="text-gray-600">{{ hesaplananBakiyeOmur.yas + 1 }} yaşındaki bakiye ömür:</span>
                      <span class="font-medium">{{ hesaplananBakiyeOmur.bakiyeOmurSonraki.toFixed(4) }} yıl</span>
                    </li>
                    <li class="flex justify-between">
                      <span class="text-gray-600">Fark:</span>
                      <span class="font-medium">{{ hesaplananBakiyeOmur.fark.toFixed(4) }} yıl</span>
                    </li>
                    <li class="flex justify-between">
                      <span class="text-gray-600">Gün Düzeltme Payı:</span>
                      <span class="font-medium">{{ hesaplananBakiyeOmur.gunDuzeltmePayi.toFixed(4) }} yıl</span>
                    </li>
                    <li class="flex justify-between border-t border-gray-200 pt-2 mt-2">
                      <span class="text-gray-700 font-medium">Sonuç (Bakiye Ömür):</span>
                      <span class="font-bold text-indigo-600">{{ hesaplananBakiyeOmur.omur.toFixed(4) }} yıl</span>
                    </li>
                  </ul>
                </div>

                <div class="mt-6 pt-6 border-t border-gray-100">
                  <p class="text-base text-gray-600">
                    {{ hesaplananBakiyeOmur.yas }} yaş {{ hesaplananBakiyeOmur.gunSayisi }} gün ({{ form.cinsiyet === 'erkek' ? 'Erkek' : 'Kadın' }}) için bakiye ömür hesaplaması sonucu: {{ hesaplananBakiyeOmur.yil }} yıl
                    <span v-if="hesaplananBakiyeOmur.ay > 0">{{ hesaplananBakiyeOmur.ay }} ay</span>
                    daha yaşayabilir.
                  </p>
                </div>

                <div class="mt-6">
                  <div class="bg-gray-100 rounded-full overflow-hidden h-6">
                    <div class="bg-indigo-600 h-6" :style="`width: ${Math.min((hesaplananBakiyeOmur.yas / (hesaplananBakiyeOmur.yas + hesaplananBakiyeOmur.omur)) * 100, 100)}%`"></div>
                  </div>
                  <div class="flex justify-between mt-3 text-sm text-gray-500">
                    <span>Doğum</span>
                    <span>Beklenen Yaşam Süresi: {{ Math.round(hesaplananBakiyeOmur.yas + hesaplananBakiyeOmur.omur) }} yıl</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </FrontLayout>
</template>
