<script setup>
import BannerSlider from '@/components/BannerSlider.vue';
import FrontLayout from '@/layouts/FrontLayout.vue';
import { useLocaleStore } from '@/Stores/locale';
import { Head, Link } from '@inertiajs/vue3';
import { computed, onMounted, ref } from 'vue';

const props = defineProps({
    featuredProducts: Array,
    latestGallery: Array,
    mainBanners: Array,
    allMainBanners: Array,
    banner1: Array,
    banner2: Array,
    banner3: Array,
    banner4: Array,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
// const { getInlineStyles } = useTheme(); // Geçici olarak devre dışı

// Get product image with fallback
const getProductImage = (product) => {
    // Try primary_image first (order = 0), then min_order_image_path
    if (product.primary_image) {
        return product.primary_image;
    }
    if (product.min_order_image_path) {
        return product.min_order_image_path;
    }
    // Fallback to placeholder image
    return '/images/placeholder-product.svg';
};

// Fallback for hero section if no banners are available
const heroImages = ['/images/hero-1.png', '/images/hero-2.png', '/images/hero-3.png'];

const currentHeroIndex = ref(0);
const currentHeroImage = computed(() => heroImages[currentHeroIndex.value]);

// Auto-rotate hero images only if no banners are available
onMounted(() => {
    if (!props.allMainBanners || props.allMainBanners.length === 0) {
        setInterval(() => {
            currentHeroIndex.value = (currentHeroIndex.value + 1) % heroImages.length;
        }, 5000);
    }
});
</script>

<template>
    <Head title="Home" />

    <FrontLayout>
        <!-- Hero Section with Banner Slider -->
        <div v-if="allMainBanners && allMainBanners.length > 0" class="main-hero mb-8">
            <BannerSlider :banners="allMainBanners" :autoplay="true" :interval="5000" :isHero="true" />
        </div>

        <!-- Fallback Hero Section if no banners -->
        <div v-else class="relative h-[500px] overflow-hidden bg-gray-900 md:h-[600px] lg:h-[700px]">
            <div class="-opacity absolute inset-0 duration-1000 ease-in-out">
                <div class="-opacity absolute inset-0 bg-cover bg-center duration-1000" :style="{ backgroundImage: `url(${currentHeroImage})`, opacity: 1 }"></div>
                <div class="absolute inset-0 bg-black opacity-40"></div>
            </div>

            <div class="relative mx-auto flex h-full max-w-7xl items-center px-4 sm:px-6 lg:px-8"></div>
        </div>

        <!-- Featured Products Section -->
        <div class="bg-white py-24 sm:py-32">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <div class="mx-auto max-w-2xl text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                        {{ locale === 'tr' ? 'Öne Çıkan Ürünlerimiz' : 'Featured Products' }}
                    </h2>
                    <p class="mt-2 text-lg leading-8 text-gray-600">
                        {{ locale === 'tr' ? 'En kaliteli ürünlerimizi keşfedin' : 'Discover our highest quality products' }}
                    </p>
                </div>

                <!-- Banner 1 placement (above featured products) -->
                <div v-if="banner1 && banner1.length > 0" class="bg-gray-50 py-12">
                    <div class="mx-auto max-w-7xl px-6 lg:px-8">
                        <h2 class="mb-4 text-2xl font-bold text-gray-900 md:text-3xl">
                            {{ 'Banner 1' }}
                        </h2>
                        <BannerSlider :banners="banner1" :autoplay="true" :interval="6000" />
                    </div>
                </div>

                <div v-if="featuredProducts && featuredProducts.length" class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
                    <article v-for="product in featuredProducts" :key="product.id" class="flex flex-col items-start">
                        <div class="relative w-full">
                            <img
                                :src="getProductImage(product)"
                                :alt="product.name[locale] || product.name"
                                class="aspect-[16/9] w-full rounded-2xl bg-gray-100 object-cover sm:aspect-[2/1] lg:aspect-[3/2]"
                                @error="$event.target.src = '/images/placeholder-product.svg'"
                            />
                            <div class="absolute inset-0 rounded-2xl ring-1 ring-inset ring-gray-900/10"></div>
                        </div>
                        <div class="max-w-xl">
                            <div class="group relative">
                                <h3 class="mt-3 text-lg font-semibold leading-6 text-gray-900 group-hover:text-gray-600">
                                    <Link :href="route('front.products.show', product.slug)">
                                        <span class="absolute inset-0"></span>
                                        {{ product.name[locale] }}
                                    </Link>
                                </h3>
                            </div>
                        </div>
                    </article>
                </div>

                <div v-else class="mt-16 text-center">
                    <p class="text-gray-500">
                        {{ locale === 'tr' ? 'Henüz ürün bulunmamaktadır.' : 'No products available yet.' }}
                    </p>
                </div>

                <div class="mt-10 text-center">
                    <Link :href="route('front.products.index')" class="inline-flex items-center rounded-md border border-transparent bg-theme-primary px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-theme-secondary">
                        {{ locale === 'tr' ? 'Tüm Ürünleri Gör' : 'View All Products' }}
                    </Link>
                </div>
            </div>
        </div>

        <!-- Banner 2 placement (between sections) -->
        <div v-if="banner2 && banner2.length > 0" class="bg-gray-50 py-12">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <h2 class="mb-4 text-2xl font-bold text-gray-900 md:text-3xl">
                    {{ 'Banner 2' }}
                </h2>
                <BannerSlider :banners="banner2" :autoplay="true" :interval="7000" :isHero="false" />
            </div>
        </div>

        <!-- Gallery Preview Section -->
        <div class="bg-gray-100 py-24 sm:py-32">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <div class="mx-auto max-w-2xl text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                        {{ locale === 'tr' ? 'Galeri' : 'Gallery' }}
                    </h2>
                    <p class="mt-2 text-lg leading-8 text-gray-600">
                        {{ locale === 'tr' ? 'En son çalışmalarımızdan bir seçki' : 'A selection of our latest works' }}
                    </p>
                </div>

                <div v-if="latestGallery && latestGallery.length" class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-8 sm:grid-cols-2 lg:mx-0 lg:max-w-none lg:grid-cols-3">
                    <div v-for="gallery in latestGallery" :key="gallery.id" class="relative">
                        <img :src="gallery.thumbnail_path || '/images/gallery-placeholder.jpg'" :alt="gallery.title[locale]" class="aspect-[1/1] w-full rounded-2xl bg-gray-100 object-cover" />
                        <div class="absolute inset-0 flex items-end rounded-2xl bg-gradient-to-t from-black/60 to-transparent p-4">
                            <h3 class="text-lg font-semibold text-white">{{ gallery.title[locale] }}</h3>
                        </div>
                    </div>
                </div>

                <div v-else class="mt-16 text-center">
                    <p class="text-gray-500">
                        {{ locale === 'tr' ? 'Henüz galeri öğesi bulunmamaktadır.' : 'No gallery items available yet.' }}
                    </p>
                </div>

                <div class="mt-10 text-center">
                    <Link :href="route('front.gallery.index')" class="inline-flex items-center rounded-md border border-transparent bg-theme-primary px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-theme-secondary">
                        {{ locale === 'tr' ? 'Tüm Galeriyi Gör' : 'View Full Gallery' }}
                    </Link>
                </div>
            </div>
        </div>

        <!-- Banner 3 placement (before contact CTA) -->
        <div v-if="banner3 && banner3.length > 0" class="bg-white py-16">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <h2 class="mb-4 text-2xl font-bold text-gray-900 md:text-3xl">
                    {{ 'Banner 3' }}
                </h2>
                <BannerSlider :banners="banner3" :autoplay="true" :interval="9000" :isHero="false" />
            </div>
        </div>

        <!-- Contact CTA Section 
        <div class="bg-theme-primary">
            <div class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8 lg:py-40">
                <div class="mx-auto max-w-2xl text-center">
                    <h2 class="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                        {{ locale === 'tr' ? 'Bizimle İletişime Geçin' : 'Get in Touch With Us' }}
                    </h2>
                    <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-indigo-200">
                        {{ locale === 'tr' ? 'Sorularınız mı var? Bizimle iletişime geçin, size yardımcı olmaktan memnuniyet duyarız.' : 'Have questions? Contact us, we are happy to help.' }}
                    </p>
                    <div class="mt-10 flex items-center justify-center gap-x-6">
                        <Link
                            :href="route('front.contact.form')"
                            class="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-indigo-600 shadow-sm hover:bg-indigo-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
                        >
                            {{ locale === 'tr' ? 'İletişim Formu' : 'Contact Form' }}
                        </Link>
                    </div>
                </div>
            </div>
        </div>
-->
        <!-- Banner 4 placement (bottom of page) -->
        <div v-if="banner4 && banner4.length > 0" class="bg-white py-16">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <h2 class="mb-4 text-2xl font-bold text-gray-900 md:text-3xl">
                    {{ 'Banner 4' }}
                </h2>
                <BannerSlider :banners="banner4" :autoplay="true" :interval="8000" :isHero="false" />
            </div>
        </div>
    </FrontLayout>
</template>
