<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import { computed } from 'vue';
import FrontLayout from '@/layouts/FrontLayout.vue';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    order: Object,
    bankDetails: Object,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

// Currency formatter for Turkish Lira
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY',
        minimumFractionDigits: 2,
    }).format(amount);
};

const formatDate = (date) => {
    return new Date(date).toLocaleDateString(locale.value === 'tr' ? 'tr-TR' : 'en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
        alert(locale.value === 'tr' ? 'Panoya kopyalandı!' : 'Copied to clipboard!');
    });
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'Sipariş Onayı' : 'Order Confirmation'" />

    <FrontLayout>
        <div class="min-h-screen bg-gray-50 py-8">
            <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                <!-- Success Header -->
                <div class="text-center mb-8">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <h1 class="text-3xl font-bold text-gray-900">
                        {{ locale === 'tr' ? 'Siparişiniz Alındı!' : 'Order Received!' }}
                    </h1>
                    <p class="mt-2 text-lg text-gray-600">
                        {{ locale === 'tr' ? 'Siparişiniz başarıyla oluşturuldu. Aşağıdaki bilgileri kullanarak ödemenizi gerçekleştirebilirsiniz.' : 'Your order has been successfully created. You can complete your payment using the information below.' }}
                    </p>
                </div>

                <!-- Order Details -->
                <div class="bg-white shadow-sm rounded-lg overflow-hidden mb-8">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">
                            {{ locale === 'tr' ? 'Sipariş Detayları' : 'Order Details' }}
                        </h2>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">
                                    {{ locale === 'tr' ? 'Sipariş Numarası' : 'Order Number' }}
                                </dt>
                                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ order.order_number }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">
                                    {{ locale === 'tr' ? 'Sipariş Tarihi' : 'Order Date' }}
                                </dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(order.created_at) }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">
                                    {{ locale === 'tr' ? 'Müşteri' : 'Customer' }}
                                </dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ order.customer_name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">
                                    {{ locale === 'tr' ? 'E-posta' : 'Email' }}
                                </dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ order.customer_email }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">
                                    {{ locale === 'tr' ? 'Telefon' : 'Phone' }}
                                </dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ order.customer_phone }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">
                                    {{ locale === 'tr' ? 'Toplam Tutar' : 'Total Amount' }}
                                </dt>
                                <dd class="mt-1 text-lg font-semibold text-gray-900">{{ formatCurrency(order.total_amount) }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Bank Transfer Information -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-3 flex-1">
                            <h3 class="text-lg font-medium text-blue-900">
                                {{ locale === 'tr' ? 'Banka Havalesi Bilgileri' : 'Bank Transfer Information' }}
                            </h3>
                            <div class="mt-4 space-y-3">
                                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                    <div>
                                        <dt class="text-sm font-medium text-blue-800">
                                            {{ locale === 'tr' ? 'Banka Adı' : 'Bank Name' }}
                                        </dt>
                                        <dd class="mt-1 text-sm text-blue-900 flex items-center">
                                            {{ bankDetails.bank_name }}
                                            <button
                                                @click="copyToClipboard(bankDetails.bank_name)"
                                                class="ml-2 text-blue-600 hover:text-blue-800"
                                                :title="locale === 'tr' ? 'Kopyala' : 'Copy'"
                                            >
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                            </button>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-blue-800">
                                            {{ locale === 'tr' ? 'Hesap Sahibi' : 'Account Name' }}
                                        </dt>
                                        <dd class="mt-1 text-sm text-blue-900 flex items-center">
                                            {{ bankDetails.account_name }}
                                            <button
                                                @click="copyToClipboard(bankDetails.account_name)"
                                                class="ml-2 text-blue-600 hover:text-blue-800"
                                                :title="locale === 'tr' ? 'Kopyala' : 'Copy'"
                                            >
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                            </button>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-blue-800">IBAN</dt>
                                        <dd class="mt-1 text-sm text-blue-900 font-mono flex items-center">
                                            {{ bankDetails.iban }}
                                            <button
                                                @click="copyToClipboard(bankDetails.iban)"
                                                class="ml-2 text-blue-600 hover:text-blue-800"
                                                :title="locale === 'tr' ? 'Kopyala' : 'Copy'"
                                            >
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                            </button>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-blue-800">
                                            {{ locale === 'tr' ? 'Tutar' : 'Amount' }}
                                        </dt>
                                        <dd class="mt-1 text-lg font-semibold text-blue-900 flex items-center">
                                            {{ formatCurrency(order.total_amount) }}
                                            <button
                                                @click="copyToClipboard(order.total_amount.toString())"
                                                class="ml-2 text-blue-600 hover:text-blue-800"
                                                :title="locale === 'tr' ? 'Kopyala' : 'Copy'"
                                            >
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                            </button>
                                        </dd>
                                    </div>
                                </div>
                                
                                <div class="mt-4 p-3 bg-blue-100 rounded-md">
                                    <p class="text-sm text-blue-800">
                                        <strong>{{ locale === 'tr' ? 'Önemli:' : 'Important:' }}</strong>
                                        {{ locale === 'tr' ? 'Havale açıklama kısmına sipariş numaranızı (' + order.order_number + ') yazmayı unutmayın.' : 'Please include your order number (' + order.order_number + ') in the transfer description.' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="bg-white shadow-sm rounded-lg overflow-hidden mb-8">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">
                            {{ locale === 'tr' ? 'Sipariş Edilen Ürünler' : 'Ordered Items' }}
                        </h2>
                    </div>
                    <div class="divide-y divide-gray-200">
                        <div
                            v-for="item in order.order_items"
                            :key="item.id"
                            class="px-6 py-4 flex items-center justify-between"
                        >
                            <div class="flex items-center space-x-4">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-900">{{ item.product_name }}</h3>
                                    <p v-if="item.product_sku" class="text-sm text-gray-500">{{ locale === 'tr' ? 'Ürün Kodu:' : 'SKU:' }} {{ item.product_sku }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-gray-900">{{ item.quantity }} × {{ formatCurrency(item.unit_price) }}</p>
                                <p class="text-sm font-medium text-gray-900">{{ formatCurrency(item.total_price) }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                        <div class="flex justify-between">
                            <span class="text-base font-medium text-gray-900">{{ locale === 'tr' ? 'Toplam' : 'Total' }}</span>
                            <span class="text-base font-medium text-gray-900">{{ formatCurrency(order.total_amount) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Next Steps -->
                <div class="bg-white shadow-sm rounded-lg p-6 mb-8">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">
                        {{ locale === 'tr' ? 'Sonraki Adımlar' : 'Next Steps' }}
                    </h2>
                    <ol class="list-decimal list-inside space-y-2 text-sm text-gray-700">
                        <li>{{ locale === 'tr' ? 'Yukarıdaki banka bilgilerini kullanarak havale yapın' : 'Make a bank transfer using the information above' }}</li>
                        <li>{{ locale === 'tr' ? 'Havale açıklama kısmına sipariş numaranızı yazın' : 'Include your order number in the transfer description' }}</li>
                        <li>{{ locale === 'tr' ? 'Ödemeniz onaylandıktan sonra siparişiniz hazırlanacak' : 'Your order will be prepared after payment confirmation' }}</li>
                        <li>{{ locale === 'tr' ? 'Kargo bilgileri e-posta ile gönderilecek' : 'Shipping information will be sent via email' }}</li>
                    </ol>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link
                        :href="route('front.products.index')"
                        class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    >
                        {{ locale === 'tr' ? 'Alışverişe Devam Et' : 'Continue Shopping' }}
                    </Link>
                    <Link
                        :href="route('home')"
                        class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                        {{ locale === 'tr' ? 'Ana Sayfaya Dön' : 'Back to Home' }}
                    </Link>
                </div>
            </div>
        </div>
    </FrontLayout>
</template>
