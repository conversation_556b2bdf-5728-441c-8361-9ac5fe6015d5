<script setup>
import PrimaryButton from '@/components/PrimaryButton.vue';
import FrontLayout from '@/layouts/FrontLayout.vue';
import { Head, router } from '@inertiajs/vue3';
import { onMounted, ref } from 'vue';

const props = defineProps({
    order: Object,
});

const processing = ref(false);
const error = ref('');

// Currency formatter
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY',
        minimumFractionDigits: 2,
    }).format(amount);
};

// Process PayTR payment
const processPayment = async () => {
    processing.value = true;
    error.value = '';

    try {
        const response = await fetch(route('paytr.process'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
            body: JSON.stringify({
                order_id: props.order.id,
            }),
        });

        const result = await response.json();

        if (result.success) {
            // Redirect to PayTR payment page
            window.location.href = result.payment_url;
        } else {
            error.value = result.error || 'Ödeme işlemi başlatılamadı.';
            processing.value = false;
        }
    } catch (err) {
        error.value = 'Bir hata oluştu. Lütfen tekrar deneyin.';
        processing.value = false;
    }
};

// Auto-start payment process
onMounted(() => {
    // Auto-process payment after 3 seconds
    setTimeout(() => {
        if (!processing.value && !error.value) {
            processPayment();
        }
    }, 3000);
});
</script>

<template>
    <Head title="Ödeme İşlemi" />

    <FrontLayout>
        <div class="min-h-screen bg-gray-50 py-12">
            <div class="mx-auto max-w-2xl px-4 sm:px-6 lg:px-8">
                <div class="rounded-lg bg-white p-8 shadow-sm">
                    <!-- Header -->
                    <div class="text-center">
                        <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-theme-primary/20">
                            <svg class="text-theme-general h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                        </div>
                        <h1 class="mt-4 text-2xl font-bold text-gray-900">Ödeme İşlemi</h1>
                        <p class="mt-2 text-gray-600">Güvenli ödeme sayfasına yönlendiriliyorsunuz...</p>
                    </div>

                    <!-- Order Summary -->
                    <div class="mt-8 rounded-lg border border-gray-200 p-6">
                        <h2 class="text-lg font-medium text-gray-900">Sipariş Özeti</h2>
                        <div class="mt-4 space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Sipariş No:</span>
                                <span class="font-medium">{{ order.order_number }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Müşteri:</span>
                                <span class="font-medium">{{ order.customer_name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">E-posta:</span>
                                <span class="font-medium">{{ order.customer_email }}</span>
                            </div>
                            <div class="flex justify-between border-t border-gray-200 pt-3">
                                <span class="text-lg font-semibold text-gray-900">Toplam Tutar:</span>
                                <span class="text-lg font-semibold text-gray-900">{{ formatCurrency(order.total_amount) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Status -->
                    <div class="mt-8">
                        <div v-if="processing" class="text-center">
                            <div class="inline-flex items-center">
                                <svg class="mr-3 h-5 w-5 animate-spin text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span class="text-blue-600">Ödeme sayfası hazırlanıyor...</span>
                            </div>
                        </div>

                        <div v-if="error" class="rounded-md bg-red-50 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path
                                            fill-rule="evenodd"
                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                            clip-rule="evenodd"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">Ödeme Hatası</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <p>{{ error }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-8 flex flex-col space-y-3 sm:flex-row sm:space-x-3 sm:space-y-0">
                        <PrimaryButton v-if="!processing && error" @click="processPayment" class="flex-1 justify-center"> Tekrar Dene </PrimaryButton>

                        <PrimaryButton v-if="!processing && !error" @click="processPayment" class="flex-1 justify-center"> Ödeme Sayfasına Git </PrimaryButton>

                        <button
                            type="button"
                            @click="router.visit(route('checkout.confirmation', order.order_number))"
                            class="flex-1 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                        >
                            Sipariş Detayına Dön
                        </button>
                    </div>

                    <!-- Security Notice -->
                    <div class="mt-8 rounded-lg bg-green-50 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-800">Güvenli Ödeme</h3>
                                <div class="mt-2 text-sm text-green-700">
                                    <p>Ödemeniz SSL sertifikası ile korunmaktadır. Kredi kartı bilgileriniz güvenle işlenir ve saklanmaz.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </FrontLayout>
</template>
