<script setup>
import Checkbox from '@/components/Checkbox.vue';
import InputError from '@/components/InputError.vue';
import InputLabel from '@/components/InputLabel.vue';
import PrimaryButton from '@/components/PrimaryButton.vue';
import TextInput from '@/components/TextInput.vue';
import FrontLayout from '@/layouts/FrontLayout.vue';
import { useCartStore } from '@/stores/cart';
import { useLocaleStore } from '@/Stores/locale';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { computed, ref } from 'vue';

const cartStore = useCartStore();
const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const useSameAddress = ref(true);

// Redirect to cart if empty
if (cartStore.isEmpty) {
    window.location.href = '/cart';
}

const form = useForm({
    customer_name: '',
    customer_email: '',
    customer_phone: '',
    billing_address: {
        street: '',
        city: '',
        postal_code: '',
        country: 'Türkiye',
    },
    shipping_address: {
        street: '',
        city: '',
        postal_code: '',
        country: 'Türkiye',
    },
    cart_items: cartStore.items,
    subtotal: cartStore.subtotal,
    tax_amount: cartStore.taxAmount,
    total_amount: cartStore.total,
    payment_method: 'bank_transfer',
    notes: '',
});

// Currency formatter for Turkish Lira
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY',
        minimumFractionDigits: 2,
    }).format(amount);
};

const submit = async () => {
    // If using same address, copy billing to shipping
    if (useSameAddress.value) {
        form.shipping_address = { ...form.billing_address };
    }

    // For PayTR, we need to handle JSON response
    if (form.payment_method === 'paytr') {
        try {
            const response = await fetch(route('checkout.store'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify(form.data()),
            });

            const result = await response.json();

            if (result.success && result.payment_method === 'paytr') {
                // Clear cart and redirect to payment page
                cartStore.clearCart();
                window.location.href = result.redirect_url;
            } else {
                console.error('PayTR checkout error:', result);
            }
        } catch (error) {
            console.error('Checkout error:', error);
        }
    } else {
        // For bank transfer, use normal Inertia form submission
        form.post(route('checkout.store'), {
            onSuccess: () => {
                // Clear cart after successful order
                cartStore.clearCart();
            },
            onError: (errors) => {
                console.error('Checkout errors:', errors);
            },
        });
    }
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'Sipariş Tamamla' : 'Checkout'" />

    <FrontLayout>
        <div class="min-h-screen bg-gray-50 py-8">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <!-- Page Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">
                        {{ locale === 'tr' ? 'Sipariş Tamamla' : 'Checkout' }}
                    </h1>

                    <!-- Breadcrumb -->
                    <nav class="mt-4 flex" aria-label="Breadcrumb">
                        <ol class="flex items-center space-x-4">
                            <li>
                                <Link :href="route('cart.index')" class="text-gray-500 hover:text-gray-700">
                                    {{ locale === 'tr' ? 'Sepet' : 'Cart' }}
                                </Link>
                            </li>
                            <li>
                                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </li>
                            <li>
                                <span class="font-medium text-gray-900">
                                    {{ locale === 'tr' ? 'Sipariş Bilgileri' : 'Checkout' }}
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>

                <form @submit.prevent="submit" class="grid grid-cols-1 gap-8 lg:grid-cols-3">
                    <!-- Checkout Form -->
                    <div class="space-y-8 lg:col-span-2">
                        <!-- Customer Information -->
                        <div class="rounded-lg bg-white p-6 shadow-sm">
                            <h2 class="mb-6 text-lg font-medium text-gray-900">
                                {{ locale === 'tr' ? 'İletişim Bilgileri' : 'Contact Information' }}
                            </h2>

                            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                <div class="sm:col-span-2">
                                    <InputLabel for="customer_name" :value="locale === 'tr' ? 'Ad Soyad' : 'Full Name'" />
                                    <TextInput id="customer_name" v-model="form.customer_name" type="text" class="mt-1 block w-full" required autofocus />
                                    <InputError :message="form.errors.customer_name" class="mt-2" />
                                </div>

                                <div>
                                    <InputLabel for="customer_email" :value="locale === 'tr' ? 'E-posta' : 'Email'" />
                                    <TextInput id="customer_email" v-model="form.customer_email" type="email" class="mt-1 block w-full" required />
                                    <InputError :message="form.errors.customer_email" class="mt-2" />
                                </div>

                                <div>
                                    <InputLabel for="customer_phone" :value="locale === 'tr' ? 'Telefon' : 'Phone'" />
                                    <TextInput id="customer_phone" v-model="form.customer_phone" type="tel" class="mt-1 block w-full" required />
                                    <InputError :message="form.errors.customer_phone" class="mt-2" />
                                </div>
                            </div>
                        </div>

                        <!-- Billing Address -->
                        <div class="rounded-lg bg-white p-6 shadow-sm">
                            <h2 class="mb-6 text-lg font-medium text-gray-900">
                                {{ locale === 'tr' ? 'Fatura Adresi' : 'Billing Address' }}
                            </h2>

                            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                <div class="sm:col-span-2">
                                    <InputLabel for="billing_street" :value="locale === 'tr' ? 'Adres' : 'Street Address'" />
                                    <TextInput id="billing_street" v-model="form.billing_address.street" type="text" class="mt-1 block w-full" required />
                                    <InputError :message="form.errors['billing_address.street']" class="mt-2" />
                                </div>

                                <div>
                                    <InputLabel for="billing_city" :value="locale === 'tr' ? 'Şehir' : 'City'" />
                                    <TextInput id="billing_city" v-model="form.billing_address.city" type="text" class="mt-1 block w-full" required />
                                    <InputError :message="form.errors['billing_address.city']" class="mt-2" />
                                </div>

                                <div>
                                    <InputLabel for="billing_postal_code" :value="locale === 'tr' ? 'Posta Kodu' : 'Postal Code'" />
                                    <TextInput id="billing_postal_code" v-model="form.billing_address.postal_code" type="text" class="mt-1 block w-full" required />
                                    <InputError :message="form.errors['billing_address.postal_code']" class="mt-2" />
                                </div>

                                <div class="sm:col-span-2">
                                    <InputLabel for="billing_country" :value="locale === 'tr' ? 'Ülke' : 'Country'" />
                                    <select id="billing_country" v-model="form.billing_address.country" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        <option value="Türkiye">Türkiye</option>
                                        <option value="Germany">Germany</option>
                                        <option value="France">France</option>
                                        <option value="United Kingdom">United Kingdom</option>
                                        <option value="United States">United States</option>
                                    </select>
                                    <InputError :message="form.errors['billing_address.country']" class="mt-2" />
                                </div>
                            </div>
                        </div>

                        <!-- Shipping Address -->
                        <div class="rounded-lg bg-white p-6 shadow-sm">
                            <div class="mb-6 flex items-center justify-between">
                                <h2 class="text-lg font-medium text-gray-900">
                                    {{ locale === 'tr' ? 'Teslimat Adresi' : 'Shipping Address' }}
                                </h2>
                                <div class="flex items-center">
                                    <Checkbox id="use_same_address" v-model:checked="useSameAddress" />
                                    <InputLabel for="use_same_address" :value="locale === 'tr' ? 'Fatura adresi ile aynı' : 'Same as billing address'" class="ml-2" />
                                </div>
                            </div>

                            <div v-if="!useSameAddress" class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                <div class="sm:col-span-2">
                                    <InputLabel for="shipping_street" :value="locale === 'tr' ? 'Adres' : 'Street Address'" />
                                    <TextInput id="shipping_street" v-model="form.shipping_address.street" type="text" class="mt-1 block w-full" />
                                    <InputError :message="form.errors['shipping_address.street']" class="mt-2" />
                                </div>

                                <div>
                                    <InputLabel for="shipping_city" :value="locale === 'tr' ? 'Şehir' : 'City'" />
                                    <TextInput id="shipping_city" v-model="form.shipping_address.city" type="text" class="mt-1 block w-full" />
                                    <InputError :message="form.errors['shipping_address.city']" class="mt-2" />
                                </div>

                                <div>
                                    <InputLabel for="shipping_postal_code" :value="locale === 'tr' ? 'Posta Kodu' : 'Postal Code'" />
                                    <TextInput id="shipping_postal_code" v-model="form.shipping_address.postal_code" type="text" class="mt-1 block w-full" />
                                    <InputError :message="form.errors['shipping_address.postal_code']" class="mt-2" />
                                </div>

                                <div class="sm:col-span-2">
                                    <InputLabel for="shipping_country" :value="locale === 'tr' ? 'Ülke' : 'Country'" />
                                    <select id="shipping_country" v-model="form.shipping_address.country" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="Türkiye">Türkiye</option>
                                        <option value="Germany">Germany</option>
                                        <option value="France">France</option>
                                        <option value="United Kingdom">United Kingdom</option>
                                        <option value="United States">United States</option>
                                    </select>
                                    <InputError :message="form.errors['shipping_address.country']" class="mt-2" />
                                </div>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="rounded-lg bg-white p-6 shadow-sm">
                            <h2 class="mb-6 text-lg font-medium text-gray-900">
                                {{ locale === 'tr' ? 'Ödeme Yöntemi' : 'Payment Method' }}
                            </h2>

                            <div class="space-y-4">
                                <!-- Bank Transfer -->
                                <div class="flex items-start">
                                    <div class="flex h-5 items-center">
                                        <input id="bank_transfer" v-model="form.payment_method" value="bank_transfer" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="bank_transfer" class="font-medium text-gray-700">
                                            {{ locale === 'tr' ? 'Banka Havalesi' : 'Bank Transfer' }}
                                        </label>
                                        <p class="text-gray-500">
                                            {{ locale === 'tr' ? 'Sipariş onaylandıktan sonra banka bilgileri gönderilecektir.' : 'Bank details will be sent after order confirmation.' }}
                                        </p>
                                    </div>
                                </div>

                                <!-- PayTR Online Payment -->
                                <div class="flex items-start">
                                    <div class="flex h-5 items-center">
                                        <input id="paytr" v-model="form.payment_method" value="paytr" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="paytr" class="font-medium text-gray-700">
                                            {{ locale === 'tr' ? 'Online Ödeme (Kredi Kartı)' : 'Online Payment (Credit Card)' }}
                                        </label>
                                        <p class="text-gray-500">
                                            {{ locale === 'tr' ? 'Güvenli ödeme sayfasında kredi kartınızla ödeme yapabilirsiniz.' : 'Pay securely with your credit card on our payment page.' }}
                                        </p>
                                        <div class="mt-2 flex items-center space-x-2">
                                            <span class="text-xs text-gray-400">{{ locale === 'tr' ? 'Visa, Mastercard kabul edilir' : 'Visa, Mastercard accepted' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <InputError :message="form.errors.payment_method" class="mt-2" />
                        </div>

                        <!-- Order Notes -->
                        <div class="rounded-lg bg-white p-6 shadow-sm">
                            <h2 class="mb-6 text-lg font-medium text-gray-900">
                                {{ locale === 'tr' ? 'Sipariş Notları' : 'Order Notes' }}
                            </h2>

                            <div>
                                <InputLabel for="notes" :value="locale === 'tr' ? 'Ek notlarınız (isteğe bağlı)' : 'Additional notes (optional)'" />
                                <textarea
                                    id="notes"
                                    v-model="form.notes"
                                    rows="4"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    :placeholder="locale === 'tr' ? 'Siparişinizle ilgili özel taleplerinizi buraya yazabilirsiniz...' : 'Any special requests for your order...'"
                                ></textarea>
                                <InputError :message="form.errors.notes" class="mt-2" />
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="lg:col-span-1">
                        <div class="sticky top-8 rounded-lg bg-white p-6 shadow-sm">
                            <h2 class="mb-4 text-lg font-medium text-gray-900">
                                {{ locale === 'tr' ? 'Sipariş Özeti' : 'Order Summary' }}
                            </h2>

                            <!-- Cart Items -->
                            <div class="mb-6 space-y-4">
                                <div v-for="item in cartStore.items" :key="item.id" class="flex items-center space-x-3">
                                    <img :src="item.image || '/images/placeholder.jpg'" :alt="item.name[locale] || item.name" class="h-12 w-12 rounded-lg object-cover" />
                                    <div class="min-w-0 flex-1">
                                        <p class="truncate text-sm font-medium text-gray-900">
                                            {{ item.name[locale] || item.name }}
                                        </p>
                                        <p class="text-sm text-gray-500">{{ item.quantity }} × {{ formatCurrency(item.price) }}</p>
                                    </div>
                                    <p class="text-sm font-medium text-gray-900">
                                        {{ formatCurrency(item.price * item.quantity) }}
                                    </p>
                                </div>
                            </div>

                            <!-- Totals -->
                            <div class="space-y-3 border-t border-gray-200 pt-4">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">{{ locale === 'tr' ? 'Ara Toplam' : 'Subtotal' }}</span>
                                    <span class="font-medium">{{ formatCurrency(cartStore.subtotal) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">{{ locale === 'tr' ? 'KDV (%20)' : 'VAT (20%)' }}</span>
                                    <span class="font-medium">{{ formatCurrency(cartStore.taxAmount) }}</span>
                                </div>
                                <div class="border-t border-gray-200 pt-3">
                                    <div class="flex justify-between">
                                        <span class="text-lg font-semibold text-gray-900">{{ locale === 'tr' ? 'Toplam' : 'Total' }}</span>
                                        <span class="text-lg font-semibold text-gray-900">{{ formatCurrency(cartStore.total) }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Method -->
                            <div class="mt-6 rounded-lg bg-blue-50 p-4">
                                <h3 class="mb-2 text-sm font-medium text-blue-900">
                                    {{ locale === 'tr' ? 'Ödeme Yöntemi' : 'Payment Method' }}
                                </h3>
                                <p class="text-sm text-blue-700">
                                    <span v-if="form.payment_method === 'bank_transfer'">
                                        {{ locale === 'tr' ? 'Banka Havalesi' : 'Bank Transfer' }}
                                    </span>
                                    <span v-else-if="form.payment_method === 'paytr'">
                                        {{ locale === 'tr' ? 'Online Ödeme (Kredi Kartı)' : 'Online Payment (Credit Card)' }}
                                    </span>
                                </p>
                                <p class="mt-1 text-xs text-blue-600">
                                    <span v-if="form.payment_method === 'bank_transfer'">
                                        {{ locale === 'tr' ? 'Sipariş onaylandıktan sonra banka bilgileri gönderilecektir.' : 'Bank details will be sent after order confirmation.' }}
                                    </span>
                                    <span v-else-if="form.payment_method === 'paytr'">
                                        {{ locale === 'tr' ? 'Güvenli ödeme sayfasına yönlendirileceksiniz.' : 'You will be redirected to secure payment page.' }}
                                    </span>
                                </p>
                            </div>

                            <!-- Submit Button -->
                            <div class="mt-6">
                                <PrimaryButton type="submit" class="w-full justify-center" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                                    {{ form.processing ? (locale === 'tr' ? 'İşleniyor...' : 'Processing...') : locale === 'tr' ? 'Siparişi Tamamla' : 'Complete Order' }}
                                </PrimaryButton>
                            </div>

                            <!-- Back to Cart -->
                            <div class="mt-4">
                                <Link :href="route('cart.index')" class="block w-full rounded-md bg-gray-100 px-4 py-3 text-center font-medium text-gray-900 transition-colors hover:bg-gray-200">
                                    {{ locale === 'tr' ? 'Sepete Dön' : 'Back to Cart' }}
                                </Link>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </FrontLayout>
</template>
