<script setup>
import { Head, Link } from '@inertiajs/vue3';
import { computed } from 'vue';
import FrontLayout from '@/layouts/FrontLayout.vue';
import { useCartStore } from '@/stores/cart';
import { useLocaleStore } from '@/Stores/locale';

const cartStore = useCartStore();
const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

// Currency formatter for Turkish Lira
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY',
        minimumFractionDigits: 2,
    }).format(amount);
};

const incrementQuantity = (productId) => {
    cartStore.incrementQuantity(productId);
};

const decrementQuantity = (productId) => {
    cartStore.decrementQuantity(productId);
};

const removeItem = (productId) => {
    if (confirm(locale.value === 'tr' ? '<PERSON>u <PERSON>rünü sepetten çıkarmak istediğinizden emin misiniz?' : 'Are you sure you want to remove this item from cart?')) {
        cartStore.removeItem(productId);
    }
};

const clearCart = () => {
    if (confirm(locale.value === 'tr' ? 'Sepeti tamamen temizlemek istediğinizden emin misiniz?' : 'Are you sure you want to clear the entire cart?')) {
        cartStore.clearCart();
    }
};
</script>

<template>
    <Head :title="locale === 'tr' ? 'Alışveriş Sepeti' : 'Shopping Cart'" />

    <FrontLayout>
        <div class="min-h-screen bg-gray-50 py-8">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <!-- Page Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">
                        {{ locale === 'tr' ? 'Alışveriş Sepeti' : 'Shopping Cart' }}
                    </h1>
                    <p class="mt-2 text-gray-600">
                        {{ locale === 'tr' ? 'Sepetinizdeki ürünleri gözden geçirin ve siparişinizi tamamlayın' : 'Review the items in your cart and complete your order' }}
                    </p>
                </div>

                <!-- Empty Cart State -->
                <div v-if="cartStore.isEmpty" class="text-center py-16">
                    <svg class="mx-auto h-24 w-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.1 5H19M7 13v6a2 2 0 002 2h6a2 2 0 002-2v-6m-8 0V9a2 2 0 012-2h4a2 2 0 012 2v4" />
                    </svg>
                    <h3 class="mt-4 text-lg font-medium text-gray-900">
                        {{ locale === 'tr' ? 'Sepetiniz boş' : 'Your cart is empty' }}
                    </h3>
                    <p class="mt-2 text-gray-500">
                        {{ locale === 'tr' ? 'Alışverişe başlamak için ürünlerimize göz atın' : 'Start shopping by browsing our products' }}
                    </p>
                    <Link
                        :href="route('front.products.index')"
                        class="mt-6 inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
                    >
                        {{ locale === 'tr' ? 'Ürünlere Göz At' : 'Browse Products' }}
                    </Link>
                </div>

                <!-- Cart Content -->
                <div v-else class="grid grid-cols-1 gap-8 lg:grid-cols-3">
                    <!-- Cart Items -->
                    <div class="lg:col-span-2">
                        <div class="bg-white shadow-sm rounded-lg overflow-hidden">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h2 class="text-lg font-medium text-gray-900">
                                    {{ locale === 'tr' ? 'Sepet Ürünleri' : 'Cart Items' }}
                                    <span class="ml-2 text-sm text-gray-500">({{ cartStore.itemCount }} {{ locale === 'tr' ? 'ürün' : 'items' }})</span>
                                </h2>
                            </div>

                            <div class="divide-y divide-gray-200">
                                <div
                                    v-for="item in cartStore.items"
                                    :key="item.id"
                                    class="p-6 flex items-center space-x-4"
                                >
                                    <!-- Product Image -->
                                    <div class="flex-shrink-0">
                                        <img
                                            :src="item.image || '/images/placeholder.jpg'"
                                            :alt="item.name[locale] || item.name"
                                            class="h-20 w-20 rounded-lg object-cover"
                                        />
                                    </div>

                                    <!-- Product Details -->
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-lg font-medium text-gray-900 truncate">
                                            {{ item.name[locale] || item.name }}
                                        </h3>
                                        <p v-if="item.sku" class="text-sm text-gray-500">
                                            {{ locale === 'tr' ? 'Ürün Kodu:' : 'SKU:' }} {{ item.sku }}
                                        </p>
                                        <p class="text-lg font-semibold text-blue-600">
                                            {{ formatCurrency(item.price) }}
                                        </p>
                                    </div>

                                    <!-- Quantity Controls -->
                                    <div class="flex items-center space-x-3">
                                        <button
                                            @click="decrementQuantity(item.id)"
                                            :disabled="item.quantity <= 1"
                                            class="p-1 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                                            </svg>
                                        </button>
                                        <span class="w-12 text-center font-medium">{{ item.quantity }}</span>
                                        <button
                                            @click="incrementQuantity(item.id)"
                                            class="p-1 rounded-md border border-gray-300 hover:bg-gray-50"
                                        >
                                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                            </svg>
                                        </button>
                                    </div>

                                    <!-- Item Total -->
                                    <div class="text-right">
                                        <p class="text-lg font-semibold text-gray-900">
                                            {{ formatCurrency(item.price * item.quantity) }}
                                        </p>
                                    </div>

                                    <!-- Remove Button -->
                                    <button
                                        @click="removeItem(item.id)"
                                        class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md"
                                        :title="locale === 'tr' ? 'Ürünü Kaldır' : 'Remove Item'"
                                    >
                                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- Clear Cart Button -->
                            <div class="px-6 py-4 border-t border-gray-200">
                                <button
                                    @click="clearCart"
                                    class="text-sm text-red-600 hover:text-red-800"
                                >
                                    {{ locale === 'tr' ? 'Sepeti Temizle' : 'Clear Cart' }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="lg:col-span-1">
                        <div class="bg-white shadow-sm rounded-lg p-6 sticky top-8">
                            <h2 class="text-lg font-medium text-gray-900 mb-4">
                                {{ locale === 'tr' ? 'Sipariş Özeti' : 'Order Summary' }}
                            </h2>

                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">{{ locale === 'tr' ? 'Ara Toplam' : 'Subtotal' }}</span>
                                    <span class="font-medium">{{ formatCurrency(cartStore.subtotal) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">{{ locale === 'tr' ? 'KDV (%20)' : 'VAT (20%)' }}</span>
                                    <span class="font-medium">{{ formatCurrency(cartStore.taxAmount) }}</span>
                                </div>
                                <div class="border-t border-gray-200 pt-3">
                                    <div class="flex justify-between">
                                        <span class="text-lg font-semibold text-gray-900">{{ locale === 'tr' ? 'Toplam' : 'Total' }}</span>
                                        <span class="text-lg font-semibold text-gray-900">{{ formatCurrency(cartStore.total) }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-6 space-y-3">
                                <Link
                                    :href="route('checkout.index')"
                                    class="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors text-center block"
                                >
                                    {{ locale === 'tr' ? 'Siparişi Tamamla' : 'Proceed to Checkout' }}
                                </Link>
                                <Link
                                    :href="route('front.products.index')"
                                    class="w-full bg-gray-100 text-gray-900 py-3 px-4 rounded-md font-medium hover:bg-gray-200 transition-colors text-center block"
                                >
                                    {{ locale === 'tr' ? 'Alışverişe Devam Et' : 'Continue Shopping' }}
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </FrontLayout>
</template>
