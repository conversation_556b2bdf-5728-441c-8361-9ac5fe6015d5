<script setup lang="js">
import { Head } from '@inertiajs/vue3';
import { computed} from 'vue';
import FrontLayout from '@/layouts/FrontLayout.vue';
import { useLocaleStore } from '@/Stores/locale';
 

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

// For lightbox functionality
 
</script>

<template>
    <Head :title="locale === 'tr' ? 'Galeri' : 'Gallery'" />

    <FrontLayout>
        <div class="bg-white py-24 sm:py-32">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
            
            </div>
        </div>

        <!-- Lightbox -->
       
    </FrontLayout>
</template>
