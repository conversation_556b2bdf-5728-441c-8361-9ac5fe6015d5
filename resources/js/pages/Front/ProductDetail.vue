<script setup>
import ButtonLink from '@/components/ButtonLink.vue';
import FrontLayout from '@/layouts/FrontLayout.vue';
import { useCartStore } from '@/stores/cart';
import { useLocaleStore } from '@/Stores/locale';
import { Head } from '@inertiajs/vue3';
import { computed, ref } from 'vue';

const props = defineProps({
    product: Object,
    relatedProducts: Array,
});

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
const cartStore = useCartStore();

const quantity = ref(1);

// Currency formatter for Turkish Lira
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY',
        minimumFractionDigits: 2,
    }).format(amount);
};

const addToCart = () => {
    cartStore.addItem(props.product, quantity.value);
    // Show success message
    alert(locale.value === 'tr' ? `${quantity.value} adet ürün sepete eklendi!` : `${quantity.value} item(s) added to cart!`);
};

const incrementQuantity = () => {
    quantity.value++;
};

const decrementQuantity = () => {
    if (quantity.value > 1) {
        quantity.value--;
    }
};

// Get product image with fallback
const getProductImage = (product) => {
    // Try primary_image first (order = 0), then min_order_image_path
    if (product.primary_image) {
        return product.primary_image;
    }
    if (product.min_order_image_path) {
        return product.min_order_image_path;
    }
    // Fallback to placeholder image
    return '/images/placeholder-product.svg';
};

// Get all product images
const getProductImages = (product) => {
    if (product.product_images && product.product_images.length > 0) {
        return product.product_images
            .filter((img) => img.is_active)
            .sort((a, b) => a.order - b.order)
            .map((img) => '/storage/' + img.image_path);
    }
    return [getProductImage(product)];
};
</script>

<template>
    <Head :title="product.name[locale]" />

    <FrontLayout>
        <div class="bg-white">
            <div class="pt-6">
                <!-- Product images -->
                <div class="mx-auto max-w-2xl px-4 sm:px-6 lg:max-w-7xl lg:px-8">
                    <div class="aspect-h-4 aspect-w-3 overflow-hidden rounded-lg lg:block">
                        <img :src="getProductImage(product)" :alt="product.name[locale] || product.name" class="h-full w-full object-cover object-center" @error="$event.target.src = '/images/placeholder-product.svg'" />
                    </div>
                </div>

                <!-- Product info -->
                <div class="mx-auto max-w-2xl px-4 pb-16 pt-10 sm:px-6 lg:grid lg:max-w-7xl lg:grid-cols-2 lg:grid-rows-[auto,auto,1fr] lg:gap-x-8 lg:px-8 lg:pb-24 lg:pt-16">
                    <div class="lg:col-span-2 lg:border-r lg:border-gray-200 lg:pr-8">
                        <h1 class="text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl">{{ product.name[locale] }}</h1>

                        <!-- Category Badge -->
                        <div v-if="product.category" class="mt-2">
                            <a :href="route('front.products.index', { category: product.category.id })" class="inline-flex items-center rounded-md bg-indigo-50 px-2 py-1 text-sm font-medium text-indigo-700 ring-1 ring-inset ring-indigo-700/10">
                                {{ product.category.name[locale] }}
                            </a>
                        </div>

                        <!-- Price -->
                        <div v-if="product.price" class="mt-4">
                            <p class="text-3xl font-bold text-blue-600">{{ formatCurrency(product.price) }}</p>
                        </div>
                    </div>

                    <!-- Add to Cart Section -->
                    <div v-if="product.price" class="mt-4 lg:row-span-3 lg:mt-0">
                        <div class="rounded-lg bg-gray-50 p-6">
                            <h2 class="mb-4 text-lg font-medium text-gray-900">
                                {{ locale === 'tr' ? 'Sepete Ekle' : 'Add to Cart' }}
                            </h2>

                            <!-- Quantity Selector -->
                            <div class="mb-6">
                                <label class="mb-2 block text-sm font-medium text-gray-700">
                                    {{ locale === 'tr' ? 'Adet' : 'Quantity' }}
                                </label>
                                <div class="flex items-center space-x-3">
                                    <button @click="decrementQuantity" :disabled="quantity <= 1" class="rounded-md border border-gray-300 p-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                                        </svg>
                                    </button>
                                    <span class="w-16 text-center text-lg font-medium">{{ quantity }}</span>
                                    <button @click="incrementQuantity" class="rounded-md border border-gray-300 p-2 hover:bg-gray-50">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- Total Price -->
                            <div class="mb-6">
                                <div class="flex items-center justify-between">
                                    <span class="text-lg font-medium text-gray-900">{{ locale === 'tr' ? 'Toplam:' : 'Total:' }}</span>
                                    <span class="text-2xl font-bold text-theme-primary">{{ formatCurrency(product.price * quantity) }}</span>
                                </div>
                            </div>

                            <!-- Add to Cart Button -->
                            <button @click="addToCart" class="w-full rounded-md bg-theme-primary px-6 py-3 text-lg font-medium text-white transition-colors hover:bg-theme-primary/90">
                                {{ locale === 'tr' ? 'Sepete Ekle' : 'Add to Cart' }}
                            </button>

                            <!-- Contact Button -->
                            <div class="mt-4">
                                <ButtonLink href="/contact" class="w-full text-center">
                                    {{ locale === 'tr' ? 'Detaylı bilgi için iletişime geçiniz' : 'Contact for more details' }}
                                </ButtonLink>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Only (if no price) -->
                    <div v-else class="mt-4 lg:row-span-3 lg:mt-0">
                        <div class="rounded-lg bg-gray-50 p-6">
                            <ButtonLink href="/contact" class="w-full text-center">
                                {{ locale === 'tr' ? 'Fiyat bilgisi için iletişime geçiniz' : 'Contact for pricing information' }}
                            </ButtonLink>
                        </div>
                    </div>

                    <div class="py-10 lg:col-span-2 lg:col-start-1 lg:border-r lg:border-gray-200 lg:pb-16 lg:pr-8 lg:pt-6">
                        <!-- Description and details -->
                        <div>
                            <h3 class="sr-only">{{ locale === 'tr' ? 'Açıklama' : 'Description' }}</h3>

                            <div class="space-y-6">
                                <p class="text-base text-gray-900" v-html="product.description[locale]"></p>
                            </div>
                        </div>

                        <div class="mt-10">
                            <h3 class="text-sm font-medium text-gray-900">{{ locale === 'tr' ? 'Detaylar' : 'Details' }}</h3>

                            <div class="mt-4 space-y-6">
                                <p class="text-sm text-gray-600" v-html="product.details ? product.details[locale] : ''"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </FrontLayout>
</template>
