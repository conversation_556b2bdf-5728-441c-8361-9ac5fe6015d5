<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import TextLink from '@/components/TextLink.vue';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import FrontLayout from '@/layouts/FrontLayout.vue';
import { useForm } from '@inertiajs/vue3';
import { LoaderCircle } from 'lucide-vue-next';

defineProps<{
    status?: string;
    canResetPassword: boolean;
}>();

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const submit = () => {
    form.post(route('login'), {
        onFinish: () => form.reset('password'),
    });
};
</script>

<template>
    <FrontLayout>
        <div class="flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
            <div class="w-full max-w-md rounded-xl border border-gray-100 bg-white p-6 shadow-lg sm:p-8">
                <form @submit.prevent="submit" class="flex flex-col gap-6">
                    <div class="grid gap-6">
                        <!-- Form Header -->
                        <div class="text-center">
                            <h2 class="text-2xl font-bold text-gray-800">Hoşgeldiniz</h2>
                            <p class="mt-2 text-sm text-gray-600">Hesabınıza giriş yapın</p>
                        </div>

                        <!-- Email Input -->
                        <div class="grid gap-2">
                            <Label for="email" class="text-sm font-medium text-gray-700">E-posta</Label>
                            <div class="relative">
                                <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                    </svg>
                                </div>
                                <Input
                                    id="email"
                                    type="email"
                                    required
                                    autofocus
                                    :tabindex="1"
                                    autocomplete="email"
                                    v-model="form.email"
                                    placeholder="<EMAIL>"
                                    class="rounded-lg border-gray-300 bg-white pl-10 text-gray-800 placeholder-gray-400 shadow-sm transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                                />
                            </div>
                            <InputError :message="form.errors.email" class="text-xs text-red-500" />
                        </div>

                        <!-- Password Input -->
                        <div class="grid gap-2">
                            <div class="flex items-center justify-between">
                                <Label for="password" class="text-sm font-medium text-gray-700">Şifre</Label>
                                <TextLink v-if="canResetPassword" :href="route('password.request')" class="text-xs font-medium text-blue-600 transition-colors hover:text-blue-700" :tabindex="5"> Şifremi unuttum? </TextLink>
                            </div>
                            <div class="relative">
                                <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <Input
                                    id="password"
                                    type="password"
                                    required
                                    :tabindex="2"
                                    autocomplete="current-password"
                                    v-model="form.password"
                                    placeholder="Şifre"
                                    class="rounded-lg border-gray-300 bg-white pl-10 text-gray-800 placeholder-gray-400 shadow-sm transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                                />
                            </div>
                            <InputError :message="form.errors.password" class="text-xs text-red-500" />
                        </div>

                        <!-- Remember Me Checkbox -->
                        <div class="flex items-center justify-between" :tabindex="3">
                            <Label for="remember" class="flex items-center space-x-3 text-sm text-gray-700">
                                <Checkbox id="remember" v-model:checked="form.remember" :tabindex="4" class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-2 focus:ring-blue-500" />
                                <span>Beni hatırla</span>
                            </Label>
                        </div>

                        <!-- Submit Button -->
                        <Button
                            type="submit"
                            class="mt-2 w-full rounded-lg bg-theme-primary py-2.5 text-sm font-medium text-white transition-all duration-200 hover:bg-theme-primary/90 focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-offset-2"
                            :tabindex="4"
                            :disabled="form.processing"
                        >
                            <LoaderCircle v-if="form.processing" class="mr-2 h-4 w-4 animate-spin" />
                            <span>Giriş</span>
                        </Button>
                    </div>

                    <!-- Sign Up Link -->
                    <div class="text-center text-sm text-gray-600">
                        Hesabınız yok mu?
                        <TextLink :href="route('register')" class="font-medium text-theme-primary transition-colors hover:text-theme-primary/80" :tabindex="5"> Kayıt ol </TextLink>
                    </div>
                </form>
            </div>
        </div>
    </FrontLayout>
</template>
