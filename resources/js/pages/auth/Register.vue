<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import TextLink from '@/components/TextLink.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import FrontLayout from '@/layouts/FrontLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { LoaderCircle } from 'lucide-vue-next';

const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
});

const submit = () => {
    form.post(route('register'), {
        onFinish: () => form.reset('password', 'password_confirmation'),
    });
};
</script>

<template>
    <Head :title="Kayıt" />
    <FrontLayout>
        <div class="flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4 sm:p-6 lg:p-8">
            <div class="w-full max-w-md space-y-8 rounded-xl border border-gray-100 bg-white p-6 shadow-lg sm:p-8">
                <div class="text-center">
                    <h2 class="text-2xl font-extrabold text-gray-900">Hesap Oluştur</h2>
                </div>

                <form @submit.prevent="submit" class="space-y-6">
                    <div class="space-y-5">
                        <div class="space-y-2">
                            <Label for="name" class="text-sm font-medium text-gray-700">Adınız</Label>
                            <Input
                                id="name"
                                type="text"
                                required
                                autofocus
                                :tabindex="1"
                                autocomplete="name"
                                v-model="form.name"
                                placeholder="Adınız ve soyadınız"
                                class="bg-black text-white transition duration-200 placeholder:text-gray-400 focus:border-transparent focus:ring-2 focus:ring-blue-500"
                            />
                            <InputError :message="form.errors.name" class="text-xs text-red-500" />
                        </div>

                        <div class="space-y-2">
                            <Label for="email" class="text-sm font-medium text-gray-700">Eposta</Label>
                            <Input
                                id="email"
                                type="email"
                                required
                                :tabindex="2"
                                autocomplete="email"
                                v-model="form.email"
                                placeholder="<EMAIL>"
                                class="bg-black text-white transition duration-200 placeholder:text-gray-400 focus:border-transparent focus:ring-2 focus:ring-blue-500"
                            />
                            <InputError :message="form.errors.email" class="text-xs text-red-500" />
                        </div>

                        <div class="space-y-2">
                            <Label for="password" class="text-sm font-medium text-gray-700">Şifre</Label>
                            <Input
                                id="password"
                                type="password"
                                required
                                :tabindex="3"
                                autocomplete="new-password"
                                v-model="form.password"
                                placeholder="Şifre"
                                class="bg-black text-white transition duration-200 placeholder:text-gray-400 focus:border-transparent focus:ring-2 focus:ring-blue-500"
                            />
                            <InputError :message="form.errors.password" class="text-xs text-red-500" />
                        </div>

                        <div class="space-y-2">
                            <Label for="password_confirmation" class="text-sm font-medium text-gray-700">Şifre doğrula</Label>
                            <Input
                                id="password_confirmation"
                                type="password"
                                required
                                :tabindex="4"
                                autocomplete="new-password"
                                v-model="form.password_confirmation"
                                placeholder="Şifre tekrar"
                                class="bg-black text-white transition duration-200 placeholder:text-gray-400 focus:border-transparent focus:ring-2 focus:ring-blue-500"
                            />
                            <InputError :message="form.errors.password_confirmation" class="text-xs text-red-500" />
                        </div>
                    </div>

                    <Button
                        type="submit"
                        class="flex w-full items-center justify-center rounded-lg bg-blue-600 py-2.5 text-sm font-medium text-white transition duration-200 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300"
                        tabindex="5"
                        :disabled="form.processing"
                    >
                        <LoaderCircle v-if="form.processing" class="mr-2 h-4 w-4 animate-spin" />
                        <span>Hesap Oluştur</span>
                    </Button>

                    <div class="mt-4 text-center text-sm text-gray-600">
                        Hesabınız var mı?
                        <TextLink
                            :href="route('login')"
                            class="ml-1 font-medium text-blue-600 underline underline-offset-4 hover:text-blue-800"
                            :tabindex="6"
                        >
                            Giriş yap
                        </TextLink>
                    </div>
                </form>
            </div>
        </div>
    </FrontLayout>
</template>
