<script setup lang="ts">
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import { ref } from 'vue';

import DeleteUser from '@/components/DeleteUser.vue';
import HeadingSmall from '@/components/HeadingSmall.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';

import { BreadcrumbItem, SharedData, User } from '@/types';
interface Props {
    mustVerifyEmail: boolean;
    status?: string;
    className?: string;
    activityLogs?: any[];
}

defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Profile settings',
        href: '/settings/profile',
    },
];

const page = usePage<SharedData>();
const user = page.props.auth.user as User;

const form = useForm({
    name: user.name,
    email: user.email,
    phone: user.phone || '',
    bio: user.bio || '',
    address: user.address || '',
    city: user.city || '',
    country: user.country || '',
    postal_code: user.postal_code || '',
    birth_date: user.birth_date || '',
});

const photoForm = useForm({
    photo: null as File | null,
});

// Tab kontrolü için
const activeTab = ref('personal'); // Default tab

const switchTab = (tabName: any) => {
    activeTab.value = tabName;
};

const preferencesForm = useForm({
    preferences: user.preferences || {
        emailNotifications: true,
        marketingEmails: false,
        darkMode: false,
        language: 'en',
    },
});

const twoFactorForm = useForm({});

const photoInput = ref<HTMLInputElement | null>(null);
const photoPreview = ref<string | null>(null);

const submit = () => {
    form.patch(route('profile.update'), {
        preserveScroll: true,
    });
};

const selectNewPhoto = () => {
    photoInput.value?.click();
};

const updatePhoto = (e: Event) => {
    const target = e.target as HTMLInputElement;
    if (target.files?.length) {
        const file = target.files[0];
        photoForm.photo = file;

        const reader = new FileReader();
        reader.onload = (e) => {
            photoPreview.value = e.target?.result as string;
        };
        reader.readAsDataURL(file);
    }
};

const uploadPhoto = () => {
    photoForm.post(route('profile.photo.update'), {
        preserveScroll: true,
        onSuccess: () => {
            photoPreview.value = null;
            photoForm.reset();
        },
    });
};

const cancelPhotoUpload = () => {
    photoPreview.value = null;
    photoForm.reset();
};

const updatePreferences = () => {
    preferencesForm.post(route('profile.preferences.update'), {
        preserveScroll: true,
    });
};

const toggleTwoFactor = () => {
    twoFactorForm.post(route('profile.two-factor.toggle'), {
        preserveScroll: true,
    });
};
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="Profile settings" />

        <SettingsLayout>
            <div class="flex flex-col space-y-6">
                <div class="mb-6 flex space-x-4 border-b border-gray-200">
                    <button :class="['px-4 py-2 font-medium', activeTab === 'personal' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700']" @click="switchTab('personal')">Personal Info</button>
                    <button :class="['px-4 py-2 font-medium', activeTab === 'profile' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700']" @click="switchTab('profile')">Profile Photo</button>
                    <button :class="['px-4 py-2 font-medium', activeTab === 'preferences' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700']" @click="switchTab('preferences')">Preferences</button>
                    <button :class="['px-4 py-2 font-medium', activeTab === 'security' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700']" @click="switchTab('security')">Security</button>
                    <button :class="['px-4 py-2 font-medium', activeTab === 'activity' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700']" @click="switchTab('activity')">Activity</button>
                </div>

                <!-- Personal Information Section -->
                <HeadingSmall title="Profile information" description="Update your personal information" v-if="activeTab === 'personal'" />

                <form @submit.prevent="submit" class="space-y-6" v-if="activeTab === 'personal'">
                    <div class="grid gap-4 md:grid-cols-2">
                        <div class="grid gap-2">
                            <Label for="name">Name</Label>
                            <Input id="name" class="mt-1 block w-full" v-model="form.name" required autocomplete="name" placeholder="Full name" />
                            <InputError class="mt-2" :message="form.errors.name" />
                        </div>

                        <div class="grid gap-2">
                            <Label for="email">Email address</Label>
                            <Input id="email" type="email" class="mt-1 block w-full" v-model="form.email" required autocomplete="username" placeholder="Email address" />
                            <InputError class="mt-2" :message="form.errors.email" />
                        </div>

                        <div class="grid gap-2">
                            <Label for="phone">Phone number</Label>
                            <Input id="phone" class="mt-1 block w-full" v-model="form.phone" autocomplete="tel" placeholder="Phone number" />
                            <InputError class="mt-2" :message="form.errors.phone" />
                        </div>

                        <div class="grid gap-2">
                            <Label for="birth_date">Birth date</Label>
                            <Input id="birth_date" type="date" class="mt-1 block w-full" v-model="form.birth_date" autocomplete="bday" />
                            <InputError class="mt-2" :message="form.errors.birth_date" />
                        </div>
                    </div>

                    <div class="grid gap-2">
                        <Label for="bio">Bio</Label>
                        <textarea
                            id="bio"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            v-model="form.bio"
                            rows="4"
                            placeholder="Tell us about yourself"
                        ></textarea>
                        <InputError class="mt-2" :message="form.errors.bio" />
                    </div>

                    <div class="grid gap-4 md:grid-cols-2">
                        <div class="grid gap-2">
                            <Label for="address">Address</Label>
                            <Input id="address" class="mt-1 block w-full" v-model="form.address" autocomplete="street-address" placeholder="Address" />
                            <InputError class="mt-2" :message="form.errors.address" />
                        </div>

                        <div class="grid gap-2">
                            <Label for="city">City</Label>
                            <Input id="city" class="mt-1 block w-full" v-model="form.city" autocomplete="address-level2" placeholder="City" />
                            <InputError class="mt-2" :message="form.errors.city" />
                        </div>

                        <div class="grid gap-2">
                            <Label for="country">Country</Label>
                            <Input id="country" class="mt-1 block w-full" v-model="form.country" autocomplete="country-name" placeholder="Country" />
                            <InputError class="mt-2" :message="form.errors.country" />
                        </div>

                        <div class="grid gap-2">
                            <Label for="postal_code">Postal code</Label>
                            <Input id="postal_code" class="mt-1 block w-full" v-model="form.postal_code" autocomplete="postal-code" placeholder="Postal code" />
                            <InputError class="mt-2" :message="form.errors.postal_code" />
                        </div>
                    </div>

                    <div v-if="mustVerifyEmail && !user.email_verified_at">
                        <p class="text-sm text-muted-foreground">
                            Your email address is unverified.
                            <Link
                                :href="route('verification.send')"
                                method="post"
                                as="button"
                                class="hover:decoration-current! -colors text-foreground underline decoration-neutral-300 underline-offset-4 duration-300 ease-out dark:decoration-neutral-500"
                            >
                                Click here to resend the verification email.
                            </Link>
                        </p>

                        <div v-if="status === 'verification-link-sent'" class="mt-2 text-sm font-medium text-green-600">A new verification link has been sent to your email address.</div>
                    </div>

                    <div class="flex items-center gap-4">
                        <Button :disabled="form.processing">Save</Button>

                        <Root :show="form.recentlySuccessful" enter=" ease-in-out" enter-from="opacity-0" leave=" ease-in-out" leave-to="opacity-0">
                            <p class="text-sm text-neutral-600">Saved.</p>
                        </Root>
                    </div>
                </form>

                <!-- Profile Photo Section -->
                <HeadingSmall title="Profile photo" description="Update your profile photo" v-if="activeTab === 'profile'" />

                <div class="flex flex-col items-center space-y-6 sm:flex-row sm:space-x-6 sm:space-y-0" v-if="activeTab === 'profile'">
                    <div class="flex flex-col items-center space-y-2">
                        <div class="h-32 w-32 overflow-hidden rounded-full bg-gray-100">
                            <img v-if="photoPreview" :src="photoPreview" class="h-full w-full object-cover" />
                            <img v-else-if="user.profile_photo_url" :src="user.profile_photo_url" class="h-full w-full object-cover" />
                            <div v-else class="flex h-full w-full items-center justify-center text-2xl font-medium text-gray-400">
                                {{ user.name.charAt(0) }}
                            </div>
                        </div>
                        <p class="text-sm text-gray-500">JPG, GIF or PNG. Max size 1MB.</p>
                    </div>

                    <div class="flex flex-col space-y-3">
                        <input ref="photoInput" type="file" class="hidden" @change="updatePhoto" accept="image/*" />

                        <div v-if="!photoPreview" class="flex space-x-2">
                            <Button type="button" @click="selectNewPhoto"> Select new photo </Button>
                        </div>

                        <div v-else class="flex space-x-2">
                            <Button type="button" @click="uploadPhoto" :disabled="photoForm.processing"> Upload photo </Button>
                            <Button type="button" variant="outline" @click="cancelPhotoUpload"> Cancel </Button>
                        </div>
                    </div>
                </div>

                <!-- Preferences Section -->
                <HeadingSmall title="User preferences" description="Manage your preferences" v-if="activeTab === 'preferences'" />

                <form @submit.prevent="updatePreferences" class="space-y-6" v-if="activeTab === 'preferences'">
                    <div class="space-y-6 rounded-lg bg-white p-6 shadow">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Notifications</h3>
                            <p class="text-sm text-gray-500">Configure how you receive notifications</p>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium">Email notifications</p>
                                    <p class="text-sm text-gray-500">Receive email notifications about account activity</p>
                                </div>
                                <input type="checkbox" v-model="preferencesForm.preferences.emailNotifications" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium">Marketing emails</p>
                                    <p class="text-sm text-gray-500">Receive emails about new features and offers</p>
                                </div>
                                <input type="checkbox" v-model="preferencesForm.preferences.marketingEmails" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                            </div>
                        </div>

                        <div>
                            <Button type="submit" :disabled="preferencesForm.processing">Save preferences</Button>
                        </div>
                    </div>
                </form>

                <!-- Security Section -->
                <HeadingSmall title="Security settings" description="Manage your security settings" v-if="activeTab === 'security'" />

                <div class="space-y-6 rounded-lg bg-white p-6 shadow" v-if="activeTab === 'security'">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Two-factor authentication</h3>
                        <p class="text-sm text-gray-500">Add an extra layer of security to your account</p>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium">Two-factor authentication</p>
                            <p class="text-sm text-gray-500">
                                {{ user.two_factor_enabled ? 'Enabled' : 'Disabled' }}
                            </p>
                        </div>
                        <button
                            type="button"
                            @click="toggleTwoFactor"
                            :disabled="twoFactorForm.processing"
                            class="-colors relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                            :class="user.two_factor_enabled ? 'bg-[#56509a]' : 'bg-gray-200'"
                        >
                            <span class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 duration-200 ease-in-out" :class="user.two_factor_enabled ? 'translate-x-5' : 'translate-x-0'"></span>
                        </button>
                    </div>
                </div>

                <DeleteUser v-if="activeTab === 'security'" />

                <!-- Activity Section -->
                <HeadingSmall title="Account activity" description="View your recent account activity" v-if="activeTab === 'activity'" />

                <div class="rounded-lg bg-white p-6 shadow" v-if="activeTab === 'activity'">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Recent activity</h3>
                        <p class="text-sm text-gray-500">Your recent account activity</p>
                    </div>

                    <div v-if="activityLogs && activityLogs.length > 0" class="mt-6 space-y-4">
                        <div v-for="log in activityLogs" :key="log.id" class="flex items-start space-x-4 border-b pb-4 last:border-0">
                            <div class="flex-1">
                                <p class="font-medium">{{ log.description }}</p>
                                <div class="flex items-center space-x-2 text-sm text-gray-500">
                                    <span>{{ new Date(log.created_at).toLocaleString() }}</span>
                                    <span>•</span>
                                    <span>{{ log.ip_address }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class="py-4 text-center">
                        <p class="text-gray-500">No recent activity found</p>
                    </div>
                </div>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
