<template>
    <MemoLayout>
      <!-- <PERSON>: arkaplan resmi -->
      <div
        class="relative min-h-screen bg-[url('/bg.jpg')] bg-cover bg-center bg-no-repeat"
      >
        <!-- İsterseniz koyu bir overlay eklemek için bu div’i açabilirsiniz:
        <div class="absolute inset-0 bg-black bg-opacity-30"></div>
        -->
  
        <!-- <PERSON><PERSON><PERSON><PERSON> böl<PERSON>ü -->
        <section class="py-20 relative z-10">
          <div class="max-w-7xl mx-auto px-4">
     
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12 mt-10">
              <!-- Contact Info -->
              <div class="lg:col-span-1 space-y-8">
                <div class="bg-white rounded-lg p-8 shadow-lg">
                  <h3 class="text-2xl font-bold mb-6">Contact Information</h3>
                  <div class="space-y-6">
                    <div class="flex items-start">
                      <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-map-marker-alt text-blue-600 text-xl"></i>
                      </div>
                      <div class="ml-4">
                        <h4 class="text-lg font-semibold">Address</h4>
                        <p class="text-gray-600">
                            881 NW 13th Ave<br />Miami, FL 33125<br />United States
                        </p>
                      </div>
                    </div>
                    <div class="flex items-start">
                      <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-phone text-blue-600 text-xl"></i>
                      </div>
                      <div class="ml-4">
                        <h4 class="text-lg font-semibold">Phone</h4>
                        <p class="text-gray-600">+****************</p>
                      </div>
                    </div>
                    <div class="flex items-start">
                      <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-envelope text-blue-600 text-xl"></i>
                      </div>
                      <div class="ml-4">
                        <h4 class="text-lg font-semibold">E-mail</h4>
                        <p class="text-gray-600"><EMAIL></p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="bg-white rounded-lg p-8 shadow-lg">
                  <h3 class="text-2xl font-bold mb-6">Working Hours</h3>
                  <div class="space-y-4">
                    <div class="flex justify-between">
                      <span class="text-gray-600">Monday - Friday:</span>
                      <span class="font-semibold">09:00 - 18:00</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Saturday:</span>
                      <span class="font-semibold">10:00 - 16:00</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Sunday:</span>
                      <span class="font-semibold">Closed</span>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Contact Form -->
              <div class="lg:col-span-2">
                <div class="bg-white rounded-lg p-8 shadow-lg">
                  <h3 class="text-2xl font-bold mb-6">Contact Form</h3>
                  <form @submit.prevent="handleSubmit" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div class="space-y-2">
                        <label class="text-gray-700 font-medium">Your Name</label>
                        <input
                          type="text"
                          v-model="formData.name"
                          placeholder="Your Name"
                          class="w-full px-4 py-3 border-none bg-gray-50 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div class="space-y-2">
                        <label class="text-gray-700 font-medium"
                          >Your E-mail Address</label
                        >
                        <input
                          type="email"
                          v-model="formData.email"
                          placeholder="Your E-mail Address"
                          class="w-full px-4 py-3 border-none bg-gray-50 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                    <div class="space-y-2">
                      <label class="text-gray-700 font-medium"
                        >Your Phone Number</label
                      >
                      <input
                        type="tel"
                        v-model="formData.phone"
                        placeholder="Your Phone Number"
                        class="w-full px-4 py-3 border-none bg-gray-50 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div class="space-y-2">
                      <label class="text-gray-700 font-medium">Yacht Type</label>
                      <select
                        v-model="formData.yachtType"
                        class="w-full px-4 py-3 border-none bg-gray-50 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="" disabled selected>Select Yacht Type</option>
                        <option value="motor">Motor Yacht</option>
                        <option value="sailing">Sailing Yacht</option>
                        <option value="catamaran">Catamaran</option>
                        <option value="gulet">Gulet</option>
                      </select>
                    </div>
                    <div class="space-y-2">
                      <label class="text-gray-700 font-medium"
                        >Travel Date</label
                      >
                      <input
                        type="date"
                        v-model="formData.travelDate"
                        class="w-full px-4 py-3 border-none bg-gray-50 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div class="space-y-2">
                      <label class="text-gray-700 font-medium"
                        >Number of Guests</label
                      >
                      <input
                        type="number"
                        v-model="formData.guestCount"
                        min="1"
                        max="16"
                        class="w-full px-4 py-3 border-none bg-gray-50 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div class="space-y-2">
                      <label class="text-gray-700 font-medium">Your Message</label>
                      <textarea
                        v-model="formData.message"
                        rows="4"
                        placeholder="Your Message"
                        class="w-full px-4 py-3 border-none bg-gray-50 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                      ></textarea>
                    </div>
                    <div class="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        v-model="formData.agreement"
                        id="agreement"
                        class="rounded text-blue-600 focus:ring-blue-500"
                      />
                      <label for="agreement" class="text-gray-600 text-sm"
                        >I allow my personal data to be processed</label
                      >
                    </div>
                    <div>
                      <button
                        type="submit"
                        class="!rounded-button bg-blue-600 text-white px-12 py-4 text-lg hover:bg-blue-700 whitespace-nowrap w-full"
                      >
                        <i class="fas fa-paper-plane mr-2"></i> Send Message
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
            <!-- Map Section -->
            <div class="mt-16">
              <div class="bg-white rounded-lg p-8 shadow-lg">
                <h3 class="text-2xl font-bold mb-6">Location</h3>
                <div class="aspect-w-16 aspect-h-9">
                    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3592.634895610368!2d-80.21643019999999!3d25.782620400000003!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x88d90065fc35901b%3A0x4f97ea3c7f139ea0!2sMerrill-Stevens%20Yachts%20Sales!5e0!3m2!1sen!2str!4v1741202292301!5m2!1sen!2str" width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </MemoLayout>
  </template>
  
  <script lang="ts" setup>
  import { reactive } from "vue";
  import MemoLayout from "@/layouts/MemoLayout.vue";
  
  const formData = reactive({
    name: "",
    email: "",
    phone: "",
    yachtType: "",
    message: "",
    travelDate: "",
    guestCount: 1,
    agreement: false,
  });
  
  const handleSubmit = () => {
    // Form validation
    if (
      !formData.name ||
      !formData.email ||
      !formData.phone ||
      !formData.yachtType ||
      !formData.message ||
      !formData.travelDate ||
      !formData.agreement
    ) {
      alert(
        "Please fill in all required fields and accept the Terms and Conditions."
      );
      return;
    }
  
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      alert("Please enter a valid e-mail address.");
      return;
    }
  
    if (!/^[0-9]{10}$/.test(formData.phone.replace(/\s/g, ""))) {
      alert("Please enter a valid phone number.");
      return;
    }
  
    // Here you would typically send the form data to your backend
    console.log("Form submitted:", formData);
  
    // Reset form
    Object.keys(formData).forEach((key) => {
      if (key === "guestCount") {
        formData[key] = 1;
      } else if (key === "agreement") {
        formData[key] = false;
      } else {
        formData[key] = "";
      }
    });
  
    // Show success message
    const successMessage = document.createElement("div");
    successMessage.className =
      "fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50";
    successMessage.textContent =
      "Your message has been successfully sent. We will get back to you as soon as possible.";
    document.body.appendChild(successMessage);
  
    setTimeout(() => {
      successMessage.remove();
    }, 3000);
  };
  </script>
  
  <style scoped>
  /* If you want the form cards to be semi-transparent:
  .bg-white {
    @apply bg-white/70;
  }
  */
  </style>
  