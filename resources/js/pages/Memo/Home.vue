<!-- resources/js/Pages/Home.vue -->
<template>
       <MemoLayout>
      <!-- Hero Slider -->
      <div class="relative">
        <swiper
          :modules="swiperModules"
          :slides-per-view="1"
          :loop="true"
          :pagination="{ clickable: true }"
          :autoplay="{ delay: 5000 }"
          class="h-screen"
        >
          <swiper-slide v-for="(slide, index) in heroSlides" :key="index">
            <div class="relative h-full">
              <img
                :src="slide.image"
                :alt="slide.title"
                class="w-full h-full object-cover"
              />
              <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center">
                <div class="max-w-7xl mx-auto px-4 text-white">
                  <h1 class="text-4xl md:text-6xl font-bold mb-4">
                    {{ slide.title }}
                  </h1>
                  <p class="text-xl md:text-2xl mb-8">
                    {{ slide.description }}
                  </p>
                  <button
                    class="!rounded-button bg-blue-600 text-white px-8 py-3 text-lg hover:bg-blue-700"
                  >
                    Make a Reservation
                  </button>
                </div>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
  
      <!-- Yacht Cards -->
      <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4">
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-5xl font-bold mb-4">
              Premium Yacht Collection
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              Enjoy an unforgettable maritime experience with our world-class
              luxury yachts. Each carefully selected vessel in our fleet brings
              together top-tier comfort and technology.
            </p>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div
              v-for="yacht in yachts"
              :key="yacht.id"
              class="bg-white rounded-lg shadow-lg overflow-hidden transform hover:scale-105  duration-300"
            >
              <div class="h-64 overflow-hidden">
                <img
                  :src="yacht.image"
                  :alt="yacht.name"
                  class="w-full h-full object-cover"
                />
              </div>
              <div class="p-6">
                <h3 class="text-2xl font-bold mb-2">{{ yacht.name }}</h3>
                <p class="text-gray-600 mb-4">{{ yacht.description }}</p>
                <div class="flex flex-col space-y-3 mb-4">
                  <div class="flex items-center">
                    <i class="fas fa-users text-blue-600 mr-2"></i>
                    <span>{{ yacht.capacity }} Guests</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-tag text-blue-600 mr-2"></i>
                    <span class="font-semibold">{{ yacht.price }}</span>
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <span
                      v-for="feature in yacht.features"
                      :key="feature"
                      class="bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded"
                    >
                      {{ feature }}
                    </span>
                  </div>
                </div>
                <div class="flex space-x-2">
                  <button
                    class="!rounded-button flex-1 bg-blue-600 text-white py-3 hover:bg-blue-700"
                  >
                    <i class="fas fa-calendar-alt mr-2"></i> Reservation
                  </button>
                  <button
                    @click="showYachtDetails(yacht)"
                    class="!rounded-button flex-1 border-2 border-blue-600 text-blue-600 py-3 hover:bg-blue-50"
                  >
                    <i class="fas fa-info-circle mr-2"></i> Details
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
  
      <!-- Yacht Details Modal -->
      <div
        v-if="selectedYacht"
        class="fixed inset-0 z-50 overflow-y-auto"
        aria-labelledby="modal-title"
        role="dialog"
        aria-modal="true"
      >
        <div
          class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
        >
          <div
            class="fixed inset-0 bg-gray-500 bg-opacity-75 -opacity"
            aria-hidden="true"
            @click="selectedYacht = null"
          ></div>
          <div
            class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform -all sm:my-8 sm:align-middle sm:max-w-7xl sm:w-full"
          >
            <div class="bg-white">
              <!-- Gallery -->
              <div class="relative h-[600px]">
                <swiper
                  :modules="swiperModules"
                  :slides-per-view="1"
                  :loop="true"
                  :pagination="{ clickable: true }"
                  :autoplay="{ delay: 5000 }"
                  class="h-full"
                >
                  <swiper-slide
                    v-for="(image, index) in selectedYacht.gallery"
                    :key="index"
                  >
                    <img
                      :src="image"
                      :alt="selectedYacht.name"
                      class="w-full h-full object-cover"
                    />
                  </swiper-slide>
                </swiper>
                <button
                  @click="selectedYacht = null"
                  class="absolute top-4 right-4 bg-white rounded-full p-2 shadow-lg"
                >
                  <i class="fas fa-times text-2xl"></i>
                </button>
              </div>
              <div class="px-8 py-6">
                <div class="flex justify-between items-start">
                  <div>
                    <h2 class="text-4xl font-bold mb-2">
                      {{ selectedYacht.name }}
                    </h2>
                    <p class="text-2xl text-blue-600 font-semibold mb-4">
                      {{ selectedYacht.price }}
                    </p>
                  </div>
                  <button
                    class="!rounded-button bg-blue-600 text-white px-8 py-3 text-lg hover:bg-blue-700"
                  >
                    <i class="fas fa-calendar-alt mr-2"></i> Make a Reservation
                  </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
                  <div class="col-span-2">
                    <h3 class="text-2xl font-semibold mb-4">About the Yacht</h3>
                    <p class="text-gray-600 text-lg mb-6">
                      {{ selectedYacht.fullDescription }}
                    </p>
                    <h3 class="text-2xl font-semibold mb-4">Technical Details</h3>
                    <div class="grid grid-cols-2 gap-4">
                      <div
                        v-for="(spec, index) in selectedYacht.specifications"
                        :key="index"
                        class="flex items-center"
                      >
                        <i :class="spec.icon + ' text-blue-600 mr-3 text-xl'"></i>
                        <span
                          >{{ spec.label }}:
                          <strong>{{ spec.value }}</strong></span
                        >
                      </div>
                    </div>
                    <h3 class="text-2xl font-semibold mt-8 mb-4">
                      Features & Equipment
                    </h3>
                    <div class="grid grid-cols-2 gap-4">
                      <div
                        v-for="(feature, index) in selectedYacht.detailedFeatures"
                        :key="index"
                        class="flex items-center"
                      >
                        <i class="fas fa-check text-green-500 mr-3"></i>
                        <span>{{ feature }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-2xl font-semibold mb-4">Quick Info</h3>
                    <div class="space-y-4">
                      <div class="flex items-center">
                        <i class="fas fa-users text-blue-600 mr-3"></i>
                        <span
                          >Capacity:
                          <strong>{{ selectedYacht.capacity }} Guests</strong></span
                        >
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-bed text-blue-600 mr-3"></i>
                        <span
                          >Cabins:
                          <strong>{{ selectedYacht.cabins }} Cabins</strong></span
                        >
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-ruler text-blue-600 mr-3"></i>
                        <span
                          >Length:
                          <strong>{{ selectedYacht.length }}</strong></span
                        >
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-tachometer-alt text-blue-600 mr-3"></i>
                        <span
                          >Speed: <strong>{{ selectedYacht.speed }}</strong></span
                        >
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-calendar-alt text-blue-600 mr-3"></i>
                        <span
                          >Year of Build:
                          <strong>{{ selectedYacht.year }}</strong></span
                        >
                      </div>
                    </div>
                    <div class="mt-8">
                      <h3 class="text-2xl font-semibold mb-4">Crew</h3>
                      <div class="space-y-4">
                        <div
                          v-for="(crew, index) in selectedYacht.crew"
                          :key="index"
                          class="flex items-center"
                        >
                          <i class="fas fa-user text-blue-600 mr-3"></i>
                          <span
                            >{{ crew.role }}:
                            <strong>{{ crew.count }}</strong></span
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Experience Section -->
      <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div class="space-y-8">
              <div>
                <h2 class="text-4xl font-bold mb-6">Motor Yacht Experience</h2>
                <p class="text-xl text-gray-600">
                  Enjoy a unique voyage along the breathtaking waters of the
                  Mediterranean with our luxury motor yachts. Outfitted with the
                  latest technology, our fleet is the perfect choice for sea
                  enthusiasts.
                </p>
              </div>
              <div class="space-y-4">
                <div class="flex items-center">
                  <i class="fas fa-check-circle text-blue-600 text-xl mr-3"></i>
                  <span class="text-lg">
                    State-of-the-art navigation systems
                  </span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-check-circle text-blue-600 text-xl mr-3"></i>
                  <span class="text-lg">Professional captain and crew</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-check-circle text-blue-600 text-xl mr-3"></i>
                  <span class="text-lg">Luxurious interior design</span>
                </div>
              </div>
              <button
                class="!rounded-button bg-blue-600 text-white px-8 py-3 text-lg hover:bg-blue-700"
              >
                More Information
              </button>
            </div>
            <div class="grid grid-cols-1 gap-6">
              <div class="rounded-xl overflow-hidden h-[300px]">
                <img
                  src="https://public.readdy.ai/ai/img_res/8b2ea8f08ebc317aa24e7de4a21afa2e.jpg"
                  alt="Motor Yacht Experience"
                  class="w-full h-full object-cover"
                />
              </div>
              <div class="rounded-xl overflow-hidden h-[300px]">
                <img
                  src="https://public.readdy.ai/ai/img_res/c03afaf77bae6e0cb85b48c257f0432a.jpg"
                  alt="Mediterranean Experience"
                  class="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
  
      <!-- Contact Form Section -->
      <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4">
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-5xl font-bold mb-4">Contact Us</h2>
            <p class="text-xl text-gray-600">
              Get in touch with us for your dream yacht vacation
            </p>
          </div>
          <div class="max-w-4xl mx-auto">
            <form @submit.prevent="handleSubmit" class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <input
                    type="text"
                    v-model="formData.name"
                    placeholder="Your Name"
                    class="w-full px-4 py-3 border-none bg-white rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <input
                    type="email"
                    v-model="formData.email"
                    placeholder="Your Email"
                    class="w-full px-4 py-3 border-none bg-white rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              <div>
                <input
                  type="tel"
                  v-model="formData.phone"
                  placeholder="Your Phone Number"
                  class="w-full px-4 py-3 border-none bg-white rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <select
                  v-model="formData.yachtType"
                  class="w-full px-4 py-3 border-none bg-white rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                >
                  <option value="" disabled selected>Select Yacht Type</option>
                  <option value="motor">Motor Yacht</option>
                  <option value="sailing">Sailing Yacht</option>
                  <option value="catamaran">Catamaran</option>
                  <option value="gulet">Gulet</option>
                </select>
              </div>
              <div>
                <textarea
                  v-model="formData.message"
                  rows="4"
                  placeholder="Your Message"
                  class="w-full px-4 py-3 border-none bg-white rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                ></textarea>
              </div>
              <div class="text-center">
                <button
                  type="submit"
                  class="!rounded-button bg-blue-600 text-white px-12 py-4 text-lg hover:bg-blue-700 whitespace-nowrap"
                >
                  Send Message
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>
    </MemoLayout>
  </template>
  
  <script setup lang="ts">
  import MemoLayout from '@/layouts/MemoLayout.vue'
  import { ref, reactive } from "vue";
  import { Swiper, SwiperSlide } from "swiper/vue";
  import { Pagination, Autoplay } from "swiper/modules";
  import "swiper/css";
  
 
  // Swiper modules
  const swiperModules = [Pagination, Autoplay];
  
  // Hero slides
  const heroSlides = ref([
    {
      image:
        "https://public.readdy.ai/ai/img_res/5108c568c53067e0f912c3fc218a3d26.jpg",
      title: "Luxury Yacht Charter",
      description:
        "Exclusive yacht charter service for an unforgettable maritime experience",
    },
    {
      image:
        "https://public.readdy.ai/ai/img_res/a30fc6a76d057e0c8c360be8785a6858.jpg",
      title: "Custom Interior Designs",
      description: "Yachts designed for top-level comfort and luxury",
    },
    {
      image:
        "https://public.readdy.ai/ai/img_res/4442e12b8df9f39a5022c5f4d67f66bb.jpg",
      title: "Blue Voyage",
      description: "An unforgettable holiday experience in unique coves",
    },
  ]);
  
  // Contact form data
  const formData = reactive({
    name: "",
    email: "",
    phone: "",
    yachtType: "",
    message: "",
  });
  
  const handleSubmit = () => {
    if (
      !formData.name ||
      !formData.email ||
      !formData.phone ||
      !formData.yachtType ||
      !formData.message
    ) {
      alert("Please fill in all fields.");
      return;
    }
    console.log("Form submitted:", formData);
    // Reset form
    formData.name = "";
    formData.email = "";
    formData.phone = "";
    formData.yachtType = "";
    formData.message = "";
    alert("Your message has been successfully sent. We will contact you soon.");
  };
  
  // Modal data
  const selectedYacht = ref(null);
  
  // Function to open yacht details
  const showYachtDetails = (yacht: any) => {
    selectedYacht.value = {
      ...yacht,
      gallery: [
        yacht.image,
        "https://public.readdy.ai/ai/img_res/7ebca4c18250d803d58c0fc1b671ff7b.jpg",
        "https://public.readdy.ai/ai/img_res/67859a706fc03ceec10a3f9a6d5fd3a3.jpg",
        "https://public.readdy.ai/ai/img_res/9b2ece7ee09f58afda627cbc2591e554.jpg",
      ],
      fullDescription:
        "Ocean Paradise is one of the most prestigious examples in the world of luxury yacht charter. This 42-meter ultra-luxury motor yacht stands out with its modern design and superior technology...",
      specifications: [
        { icon: "fas fa-ruler-combined", label: "Length", value: "42 meters" },
        { icon: "fas fa-arrows-alt-h", label: "Beam", value: "9.2 meters" },
        {
          icon: "fas fa-tachometer-alt",
          label: "Max Speed",
          value: "25 knots",
        },
        { icon: "fas fa-gas-pump", label: "Fuel Capacity", value: "45,000 L" },
        { icon: "fas fa-water", label: "Water Capacity", value: "8,000 L" },
        {
          icon: "fas fa-broadcast-tower",
          label: "Engine Power",
          value: "2 x 2,000 HP",
        },
      ],
      detailedFeatures: [
        "Infinity Pool",
        "Fully Equipped Spa Center",
        "Private Cinema",
        "Helicopter Pad",
        "Beach Club",
        "Jacuzzi",
        "Water Sports Equipment",
        "Sea Platform",
        "Stabilizer System",
        "Satellite TV & Wi-Fi",
        "Air Conditioning",
        "Sound System",
      ],
      cabins: 6,
      length: "42 meters",
      speed: "25 knots",
      year: "2023",
      crew: [
        { role: "Captain", count: 1 },
        { role: "Chef", count: 2 },
        { role: "Service Staff", count: 4 },
        { role: "Engineer", count: 2 },
      ],
    };
  };
  
  // Yacht list
  const yachts = ref([
    {
      id: 1,
      name: "Ocean Paradise",
      description:
        "42-meter ultra-luxury motor yacht with 6 suite cabins, infinity pool, spa center, and cinema",
      capacity: 12,
      price: "€25,000/day",
      features: ["Helicopter pad", "Water toys", "Professional crew"],
      image:
        "https://public.readdy.ai/ai/img_res/c16cb0a350f7d49a7b6838706940c574.jpg",
    },
    {
      id: 2,
      name: "Windly Prestige",
      description:
        "38-meter premium sailing yacht, 5 luxury cabins, deck jacuzzi, and outdoor cinema",
      capacity: 10,
      price: "€18,000/day",
      features: ["Stabilizer", "Water sports equipment", "Gourmet chef"],
      image:
        "https://public.readdy.ai/ai/img_res/31dd8e2d5a6cdcc06b9ae5b0569cfc21.jpg",
    },
    {
      id: 3,
      name: "Crystal Blue",
      description:
        "45-meter mega yacht, 7 master suites, wellness center, beach club, and observation deck",
      capacity: 14,
      price: "€35,000/day",
      features: ["Beach club", "Fitness center", "Wine cellar"],
      image:
        "https://public.readdy.ai/ai/img_res/4b7dbeae09bd120f5ef0c17d405001c6.jpg",
    },
    {
      id: 4,
      name: "Royal Elegance",
      description:
        "50-meter luxury explorer yacht, 8 VIP suites, helipad, underwater observation room",
      capacity: 16,
      price: "€45,000/day",
      features: ["Underwater lounge", "Helipad", "Full spa"],
      image:
        "https://public.readdy.ai/ai/img_res/785ad954675e6b00532fc4a6bfe9c290.jpg",
    },
  ]);
  </script>
  
  <style scoped>
  .swiper {
    width: 100%;
    height: 100%;
  }
  .swiper-pagination-bullet {
    background: white;
    opacity: 0.7;
  }
  .swiper-pagination-bullet-active {
    opacity: 1;
  }
  </style>
  