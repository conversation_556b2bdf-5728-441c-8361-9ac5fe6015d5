<script setup lang="ts">
import Heading from '@/components/Heading.vue';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/vue3';
import { onMounted, onUnmounted } from 'vue';

const sidebarNavItems: NavItem[] = [
    {
        title: 'Profile',
        href: route('profile.edit'),
    },
    {
        title: 'Password',
        href: route('password.edit'),
    },
    {
        title: 'Appearance',
        href: route('appearance'),
    },
];

const currentPath = window.location.pathname;

// Settings sayfalarında dark mode'u zorla light mode'a çevir
let originalTheme: string | null = null;

onMounted(() => {
    // Mevcut tema durumunu kaydet
    originalTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';

    // Dark mode'u kaldır ve light mode'a geç
    document.documentElement.classList.remove('dark');
});

onUnmounted(() => {
    // Sayfa ayrılırken orijinal temayı geri yükle
    if (originalTheme === 'dark') {
        document.documentElement.classList.add('dark');
    }
});
</script>

<template>
    <div class="px-4 py-6">
        <Heading title="Settings" description="Manage your profile and account settings" />

        <div class="flex flex-col space-y-8 md:space-y-0 lg:flex-row lg:space-x-12 lg:space-y-0">
            <aside class="w-full max-w-xl lg:w-48">
                <nav class="flex flex-col space-x-0 space-y-1">
                    <Button v-for="item in sidebarNavItems" :key="item.href" variant="ghost" :class="['w-full justify-start', { 'bg-muted': currentPath === item.href }]" as-child>
                        <Link :href="item.href">
                            {{ item.title }}
                        </Link>
                    </Button>
                </nav>
            </aside>

            <Separator class="my-6 md:hidden" />

            <div class="flex-1 md:max-w-2xl">
                <section class="max-w-xl space-y-12">
                    <slot />
                </section>
            </div>
        </div>
    </div>
</template>
