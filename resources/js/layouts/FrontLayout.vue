<script setup>
import AppLogo from '@/components/AppLogo.vue';
import CartIcon from '@/components/CartIcon.vue';
import { useLocaleStore } from '@/Stores/locale';
import { Link } from '@inertiajs/vue3';
import axios from 'axios'; // Import axios
import { computed, onMounted, onUnmounted, ref } from 'vue';
const appPhone = import.meta.env.VITE_APP_PHONE || 'Laravel';

const showingNavigationDropdown = ref(false);
const showingToolsDropdown = ref(false);
const menus = ref([]);
const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
const appName = import.meta.env.VITE_APP_NAME || 'Laravel';
const appEmail = import.meta.env.VITE_APP_EMAIL || 'Laravel';
const appAddress = import.meta.env.VITE_APP_ADDRESS || 'Laravel';

// Theme colors - geçici olarak devre dışı
// const { getInlineStyles, getTailwindColor, getHoverBackgroundClass } = useTheme();

// Timer for dropdown close delay
let closeTimer = null;

const startCloseTimer = () => {
    closeTimer = setTimeout(() => {
        showingToolsDropdown.value = false;
    }, 300); // 300ms delay before closing
};

const clearCloseTimer = () => {
    if (closeTimer) {
        clearTimeout(closeTimer);
        closeTimer = null;
    }
};

// Click outside handler
const handleClickOutside = (event) => {
    const dropdown = event.target.closest('.tools-dropdown');
    if (!dropdown) {
        showingToolsDropdown.value = false;
        clearCloseTimer();
    }
};

// Fetch menus on component mount
onMounted(async () => {
    try {
        const response = await fetch('/api/menus');
        if (response.ok) {
            const data = await response.json();
            menus.value = data.filter((menu) => menu.is_active && !menu.parent_id);
        }
    } catch (error) {
        console.error('Failed to fetch menus:', error);
    }

    // Add click outside listener
    document.addEventListener('click', handleClickOutside);
});

// Cleanup on unmount
onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
    clearCloseTimer();
});

const switchLocale = (newLocale) => {
    // Don't do anything if the locale is already set
    if (localeStore.locale === newLocale) return;

    // Set the locale directly in the store
    localeStore.locale = newLocale;
    localStorage.setItem('locale', newLocale);

    // Make the API call without reloading the page
    axios
        .post(`/api/locale/${newLocale}`)
        .then((response) => {
            console.log('Locale switched to:', newLocale, response.data);

            // Instead of reloading, force a re-render by changing the page props
            // This will update the UI without a full page reload
            document.documentElement.lang = newLocale;

            // Force a re-render of the page with Inertia
            // Using router instead of location for a smoother
            if (window.Inertia) {
                window.Inertia.reload({ only: ['locale'] });
            }
        })
        .catch((error) => {
            console.error('Failed to set locale:', error);
        });
};
</script>

<template>
    <div>
        <div class="min-h-screen">
            <!-- Top Bar with improved mobile responsiveness -->
            <div class="w-full bg-green-50 py-3 text-black">
                <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div class="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                        <!-- Contact Information - Responsive adjustments -->
                        <div class="flex flex-wrap gap-3 text-sm">
                            <!-- Phone number - Always visible -->
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                    />
                                </svg>
                                <span>{{ appPhone }}</span>
                            </div>

                            <!-- Email - Always visible -->
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <span>{{ appEmail }}</span>
                            </div>

                            <!-- Hours - Hidden on extra small screens, visible on small and up -->
                            <div class="hidden items-center sm:flex">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span>{{ locale == 'tr' ? 'Pzt - Cuma: 09:00 - 18:00' : 'Mon - Fri: 09:00 AM - 06:00 PM' }}</span>
                            </div>
                        </div>

                        <!-- Login/Register & Social Media Links -->
                        <div class="flex flex-col items-center space-y-3 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
                            <!-- Login/Register Buttons -->
                            <div class="flex space-x-2">
                                <Link
                                    class="inline-flex items-center rounded-md border border-gray-400 bg-gray-100 px-3 py-1.5 text-xs font-bold text-gray-700 shadow-sm transition duration-150 ease-in-out hover:border-theme-primary hover:bg-theme-primary hover:text-white focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-opacity-50"
                                    href="/login"
                                >
                                    GİRİŞ
                                </Link>
                                <Link
                                    class="inline-flex items-center rounded-md border border-gray-400 bg-gray-100 px-3 py-1.5 text-xs font-bold text-gray-700 shadow-sm transition duration-150 ease-in-out hover:border-theme-primary hover:bg-theme-primary hover:text-white focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-opacity-50"
                                    href="/register"
                                >
                                    ÜYE OL
                                </Link>
                            </div>

                            <!-- Social Media Links -->
                            <div class="flex justify-center space-x-3 sm:justify-end">
                                <!-- Facebook -->
                                <a href="#" class="flex h-8 w-8 items-center justify-center rounded-full text-gray-500 transition duration-150 hover:bg-theme-primary hover:text-white sm:h-6 sm:w-6">
                                    <svg class="h-4 w-4 sm:h-3.5 sm:w-3.5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path
                                            fill-rule="evenodd"
                                            d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                                            clip-rule="evenodd"
                                        ></path>
                                    </svg>
                                </a>

                                <!-- Instagram -->
                                <a href="#" class="flex h-8 w-8 items-center justify-center rounded-full text-gray-500 transition duration-150 hover:bg-theme-primary hover:text-white sm:h-6 sm:w-6">
                                    <svg class="h-4 w-4 sm:h-3.5 sm:w-3.5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path
                                            fill-rule="evenodd"
                                            d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                                            clip-rule="evenodd"
                                        ></path>
                                    </svg>
                                </a>

                                <!-- X (Twitter) -->
                                <a href="#" class="flex h-8 w-8 items-center justify-center rounded-full text-gray-500 transition duration-150 hover:bg-theme-primary hover:text-white sm:h-6 sm:w-6">
                                    <svg class="h-4 w-4 sm:h-3.5 sm:w-3.5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path>
                                    </svg>
                                </a>

                                <!-- YouTube -->
                                <a href="#" class="flex h-8 w-8 items-center justify-center rounded-full text-gray-500 transition duration-150 hover:bg-theme-primary hover:text-white sm:h-6 sm:w-6">
                                    <svg class="h-4 w-4 sm:h-3.5 sm:w-3.5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path
                                            d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"
                                        ></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <nav class="bg-opacity-200 border-b">
                <!-- Primary Navigation Menu -->
                <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div class="flex h-16 justify-between">
                        <!-- Logo -->
                        <div class="flex shrink-0 items-center">
                            <Link :href="route('home')">
                                <AppLogo class="block h-9 w-auto" />
                            </Link>
                        </div>

                        <!-- Navigation Links - Center -->
                        <div class="hidden items-center justify-center sm:flex">
                            <div class="rowdies myFont flex space-x-6 text-xl">
                                <Link href="/" class="backdrop: myFont inline-flex items-center px-3 py-2 text-gray-600 duration-150 ease-in-out hover:bg-theme-primary hover:text-white"> ANASAYFA </Link>
                                <Link href="/" class="backdrop: myFont inline-flex items-center px-3 py-2 text-gray-600 duration-150 ease-in-out hover:bg-theme-primary hover:text-white"> KURUMSAL </Link>
                                <Link href="/" class="backdrop: myFont inline-flex items-center px-3 py-2 text-gray-600 duration-150 ease-in-out hover:bg-theme-primary hover:text-white"> KULLANIM </Link>
                                <Link href="/" class="backdrop: myFont inline-flex items-center px-3 py-2 text-gray-600 duration-150 ease-in-out hover:bg-theme-primary hover:text-white"> PAKETLER </Link>
                                <Link v-for="menu in menus" :key="menu.id" :href="menu.url" class="myFont inline-flex items-center px-3 py-2 text-gray-600 duration-150 ease-in-out hover:bg-theme-primary hover:text-white">
                                    {{ menu.name[locale] }}
                                </Link>

                                <!-- Araçlar Dropdown 

                                <div class="tools-dropdown group relative" @mouseleave="startCloseTimer" @mouseenter="clearCloseTimer">
                                    <button
                                        @click="showingToolsDropdown = !showingToolsDropdown"
                                        @mouseenter="
                                            showingToolsDropdown = true;
                                            clearCloseTimer();
                                        "
                                        class="myFont inline-flex items-center px-3 py-2 text-gray-600 duration-150 ease-in-out hover:bg-theme-primary hover:text-white focus:outline-none"
                                        :class="{ 'bg-theme-primary text-white': showingToolsDropdown }"
                                    >
                                        ARAÇLAR
                                        <svg class="ml-1 h-4 w-4 transition-transform duration-200" :class="{ 'rotate-180': showingToolsDropdown }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>

                             
                                    <div v-show="showingToolsDropdown" class="absolute left-0 z-50 mt-1 w-56 origin-top-left rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                        <div class="py-1">
                                            <Link :href="route('bakiyeOmurTablosu')" class="block px-4 py-3 text-sm text-gray-700 duration-150 ease-in-out hover:bg-theme-primary hover:text-white"> Bakiye Ömür Tablosu </Link>
                                            <Link :href="route('bakiyePMFTablosu')" class="block px-4 py-3 text-sm text-gray-700 duration-150 ease-in-out hover:bg-theme-primary hover:text-white"> Bakiye PMF Tablosu </Link>
                                        </div>
                                    </div>
                                </div>
                            -->
                                <Link href="/contact" class="myFont inline-flex items-center px-3 py-2 text-gray-600 duration-150 ease-in-out hover:bg-theme-primary hover:text-white">
                                    {{ locale == 'tr' ? 'İLETİŞİM' : 'CONTACT' }}
                                </Link>
                            </div>
                        </div>

                        <!-- Cart Icon -->
                        <div class="hidden items-center sm:flex">
                            <CartIcon />
                        </div>

                        <!-- Hamburger -->
                        <div class="-mr-2 flex items-center sm:hidden">
                            <button
                                @click="showingNavigationDropdown = !showingNavigationDropdown"
                                class="inline-flex items-center justify-center rounded-md p-2 text-gray-400 duration-150 ease-in-out hover:bg-gray-100 hover:text-gray-500 focus:bg-gray-100 focus:text-gray-500 focus:outline-none"
                            >
                                <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                                    <path
                                        :class="{
                                            hidden: showingNavigationDropdown,
                                            'inline-flex': !showingNavigationDropdown,
                                        }"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M4 6h16M4 12h16M4 18h16"
                                    />
                                    <path
                                        :class="{
                                            hidden: !showingNavigationDropdown,
                                            'inline-flex': showingNavigationDropdown,
                                        }"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"
                                    />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Responsive Navigation Menu -->
                <div :class="{ block: showingNavigationDropdown, hidden: !showingNavigationDropdown }" class="sm:hidden">
                    <div class="space-y-1 pb-3 pt-2">
                        <Link href="/" class="inline-flex items-center px-3 py-2 text-sm font-bold text-gray-600 duration-150 ease-in-out hover:bg-theme-primary hover:text-white"> ANASAYFA </Link>
                        <Link href="/" class="inline-flex items-center px-3 py-2 text-sm font-bold text-gray-600 duration-150 ease-in-out hover:bg-theme-primary hover:text-white"> HAKKINDA </Link>
                        <Link href="/" class="inline-flex items-center px-3 py-2 text-sm font-bold text-gray-600 duration-150 ease-in-out hover:bg-theme-primary hover:text-white"> KULLANIM </Link>
                        <Link href="/" class="inline-flex items-center px-3 py-2 text-sm font-bold text-gray-600 duration-150 ease-in-out hover:bg-theme-primary hover:text-white"> PAKETLER </Link>
                        <Link
                            v-for="menu in menus"
                            :key="menu.id"
                            :href="menu.url"
                            class="block border-l-4 border-transparent py-2 pl-3 pr-4 text-base font-medium text-gray-600 duration-150 ease-in-out hover:border-theme-primary hover:bg-theme-primary hover:text-white focus:border-theme-primary focus:bg-theme-primary focus:text-white focus:outline-none"
                        >
                            {{ menu.name[locale] }}
                        </Link>

                        <!-- Mobile Araçlar Section -->
                        <div class="border-l-4 border-transparent py-2 pl-3 pr-4">
                            <div class="mb-2 text-base font-medium text-gray-600">ARAÇLAR</div>
                            <Link :href="route('bakiyeOmurTablosu')" class="block py-1 pl-4 text-sm text-gray-500 duration-150 ease-in-out hover:text-theme-primary"> Bakiye Ömür Tablosu </Link>
                            <Link :href="route('bakiyePMFTablosu')" class="block py-1 pl-4 text-sm text-gray-500 duration-150 ease-in-out hover:text-theme-primary"> Bakiye PMF Tablosu </Link>
                        </div>
                    </div>

                    <!-- Responsive Language Options -->
                    <div class="border-t border-gray-200 pb-1 pt-4">
                        <div class="flex space-x-4 px-4">
                            <div class="relative">
                                <button
                                    class="myFont mr-2 inline-flex items-center rounded-md border border-gray-400 bg-gray-100 px-4 py-2 text-sm font-bold text-gray-700 shadow-sm transition duration-150 ease-in-out hover:border-theme-primary hover:bg-theme-primary hover:text-white focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-opacity-50"
                                >
                                    GİRİŞ
                                </button>
                                <button
                                    class="myFont inline-flex items-center rounded-md border border-gray-400 bg-gray-100 px-4 py-2 text-sm font-bold text-gray-700 shadow-sm transition duration-150 ease-in-out hover:border-theme-primary hover:bg-theme-primary hover:text-white focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-opacity-50"
                                >
                                    ÜYE OL
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Page Content -->
            <main>
                <slot />
            </main>

            <!-- Footer -->
            <footer class="bg-gray-900 text-white">
                <div class="mx-auto max-w-7xl px-4 py-4">
                    <div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
                        <!-- Company Information -->
                        <div>
                            <div class="mb-4 flex items-center">
                                <AppLogo class="h-10 w-auto brightness-0 invert filter" />
                            </div>
                        </div>

                        <!-- Quick Links -->
                        <div>
                            <h4 class="mb-4 border-b border-gray-700 pb-2 text-lg font-semibold">Kurumsal</h4>
                            <ul class="space-y-2">
                                <li>
                                    <Link :href="route('home')" class="-colors text-gray-400 hover:bg-theme-primary hover:text-white">Home</Link>
                                </li>
                                <li>
                                    <Link :href="route('front.pages.show', 'about')" class="-colors text-gray-400 hover:bg-theme-primary hover:text-white">Kurumsal</Link>
                                </li>
                                <li>
                                    <Link :href="route('front.products.index')" class="-colors text-gray-400 hover:bg-theme-primary hover:text-white">Kullanım</Link>
                                </li>
                                <li>
                                    <Link :href="route('front.gallery.index')" class="-colors text-gray-400 hover:bg-theme-primary hover:text-white">Paketler</Link>
                                </li>
                                <li>
                                    <Link :href="route('front.contact.form')" class="-colors text-gray-400 hover:bg-theme-primary hover:text-white">İletişim</Link>
                                </li>
                            </ul>
                        </div>

                        <!-- Contact Information -->
                        <div>
                            <h4 class="mb-4 border-b border-gray-700 pb-2 text-lg font-semibold">İletişim</h4>
                            <ul class="space-y-3 text-gray-400">
                                <li class="flex items-start">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 mt-0.5 h-5 w-5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <span>{{ appAddress }}</span>
                                </li>
                                <li class="flex items-start">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 mt-0.5 h-5 w-5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                        />
                                    </svg>
                                    <span>{{ appPhone }}</span>
                                </li>
                                <li class="flex items-start">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 mt-0.5 h-5 w-5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    <span>{{ appEmail }}</span>
                                </li>
                            </ul>
                        </div>

                        <!-- Newsletter & Social -->
                        <div>
                            <h4 class="mb-4 border-b border-gray-700 pb-2 text-lg font-semibold">Sosyal Medya</h4>
                            <p class="mb-4 text-gray-400">Güncellemeler ve haberler için bizi sosyal medyada takip edin</p>
                            <div class="mb-6 flex space-x-4">
                                <a href="#" class="-colors text-gray-400 hover:bg-[#56509a] hover:text-white">
                                    <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path
                                            fill-rule="evenodd"
                                            d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                                            clip-rule="evenodd"
                                        ></path>
                                    </svg>
                                </a>
                                <a href="#" class="-colors text-gray-400 hover:bg-[#56509a] hover:text-white">
                                    <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path
                                            d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                                        ></path>
                                    </svg>
                                </a>
                                <a href="#" class="-colors text-gray-400 hover:bg-[#56509a] hover:text-white">
                                    <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path
                                            fill-rule="evenodd"
                                            d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                                            clip-rule="evenodd"
                                        ></path>
                                    </svg>
                                </a>
                                <a href="#" class="-colors text-gray-400 hover:bg-[#56509a] hover:text-white">
                                    <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path
                                            d="M19.7 3.037c-.88-.415-1.85-.618-2.926-.618-1.945 0-3.547.825-4.774 2.476-1.225-1.65-2.825-2.476-4.774-2.476-1.075 0-2.045.204-2.926.618C2.861 4.076 2 5.723 2 7.644c0 1.632.828 3.088 2.488 4.375 1.66 1.287 3.953 2.344 6.847 3.187-.194 2.31.142 4.492 1.013 6.538.87 2.046 2.287 3.256 4.233 3.256 1.948 0 3.366-1.21 4.236-3.256.87-2.046 1.207-4.228 1.013-6.538 2.894-.843 5.187-1.9 6.847-3.187C20.517 10.732 21 9.276 21 7.644c0-1.921-.861-3.568-2.3-4.607z"
                                        ></path>
                                    </svg>
                                </a>
                            </div>
                            <h5 class="mb-2 text-sm font-medium text-gray-300">Çalışma Saatleri:</h5>
                            <p class="text-sm text-gray-400">Pazartesi - Cuma: 9:00 - 18:00</p>
                        </div>
                    </div>
                </div>

                <!-- Bottom Bar with Legal Info -->
                <div class="border-t border-gray-800">
                    <div class="mx-auto max-w-7xl px-4 py-4">
                        <div class="flex flex-col items-center justify-between md:flex-row">
                            <div class="mb-4 md:mb-0">
                                <p class="text-sm text-gray-400">{{ new Date().getFullYear() }} {{ appName }} All rights reserved.</p>
                            </div>
                            <div class="flex space-x-6">
                                <Link :href="route('front.pages.show', 'privacy')" class="-colors text-sm text-gray-400 hover:bg-[#56509a] hover:text-white"> K.V.K.K Aydınlatma Metni </Link>
                                <Link :href="route('front.pages.show', 'privacy')" class="-colors text-sm text-gray-400 hover:bg-[#56509a] hover:text-white"> Gizlilik Politikası </Link>
                                <Link :href="route('front.pages.show', 'terms')" class="-colors text-sm text-gray-400 hover:bg-[#56509a] hover:text-white"> Hizmet Şartları </Link>
                                <Link :href="route('front.pages.show', 'legal')" class="-colors text-sm text-gray-400 hover:bg-[#56509a] hover:text-white"> Yasal Bilgiler </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>
</template>
