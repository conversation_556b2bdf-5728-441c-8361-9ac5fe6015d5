<script setup>
import AdminSidebar from '@/components/AdminSidebar.vue';
import FlashMessage from '@/components/FlashMessage.vue';
import { useLocaleStore } from '@/Stores/locale';
import { usePage } from '@inertiajs/vue3';
import { computed, ref } from 'vue';

const flashMessage = ref(null);

const showFlashMessage = (message, type = 'success') => {
    flashMessage.value?.showMessage(message, type);
};

const showingNavigationDropdown = ref(false);
const contentManagementDropdown = ref(false);
const userManagementDropdown = ref(false);

function toggleContentManagementDropdown() {
    contentManagementDropdown.value = !contentManagementDropdown.value;
    if (contentManagementDropdown.value) userManagementDropdown.value = false;
}

function toggleUserManagementDropdown() {
    userManagementDropdown.value = !userManagementDropdown.value;
    if (userManagementDropdown.value) contentManagementDropdown.value = false;
}

const page = usePage();
const user = computed(() => page.props.auth.user);
const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

// For sidebar mobile menu control
const isSidebarOpen = ref(false);
const toggleSidebar = () => {
    isSidebarOpen.value = !isSidebarOpen.value;
};

defineExpose({
    showFlashMessage,
});
</script>

<template>
    <AdminSidebar :user="user" @close="isSidebarOpen = false" />

    <div class="flex h-screen bg-gray-100">
        <FlashMessage ref="flashMessage" />
        <!-- Sidebar Component -->

        <!-- Main Content -->
        <div class="flex flex-1 flex-col overflow-hidden">
            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto bg-gray-100 p-4">
                <slot />
            </main>
        </div>
    </div>
</template>
