<template>
    <div class="min-h-screen bg-gray-50">
      <!-- Fixed Header -->
      <header class="fixed top-0 left-0 right-0 bg-white shadow-md z-50">
        <div class="max-w-7xl mx-auto px-4">
          <div class="flex items-center justify-between h-16">
            <!-- Logo -->
            <div class="flex items-center">
              <img
                src="https://public.readdy.ai/ai/img_res/d8a80eba966b13f1cc0fc304b8860dc6.jpg"
                alt="LOFA"
                class="h-8"
              />
            </div>
  
            <!-- Mobile Menu Toggle -->
            <button @click="isMenuOpen = !isMenuOpen" class="block md:hidden text-gray-600">
              <i class="fas fa-bars"></i>
            </button>
  
            <!-- Navigation (Desktop) -->
            <nav class="hidden md:flex items-center space-x-8">
              <!-- Inertia Link -->
              <Link
                v-for="item in menuItems"
                :key="item.id"
                :href="item.href"
                class="flex items-center cursor-pointer whitespace-nowrap"
                :class="activeMenu === item.id
                  ? 'text-blue-600 font-medium'
                  : 'text-gray-600 hover:text-blue-600'"
                @click="activeMenu = item.id"
              >
                <i :class="['mr-2', item.icon]"></i>
                {{ item.name }}
              </Link>
            </nav>
          </div>
  
          <!-- Navigation (Mobile) -->
          <nav
            v-if="isMenuOpen"
            class="md:hidden flex flex-col items-start bg-white shadow-lg mt-2 p-4 space-y-2"
          >
            <Link
              v-for="item in menuItems"
              :key="item.id"
              :href="item.href"
              class="flex items-center cursor-pointer whitespace-nowrap"
              :class="activeMenu === item.id
                ? 'text-blue-600 font-medium'
                : 'text-gray-600 hover:text-blue-600'"
              @click="() => {
                activeMenu = item.id
                isMenuOpen = false
              }"
            >
              <i :class="['mr-2', item.icon]"></i>
              {{ item.name }}
            </Link>
          </nav>
        </div>
      </header>
  
      <!-- Main Content -->
      <main class="pt-20 pb-8 max-w-7xl mx-auto px-4">
        <slot />
      </main>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue'
  // Inertia Link import’u
  import { Link } from '@inertiajs/vue3'
  
  // Menü durumu
  const activeMenu = ref('home')
  const isMenuOpen = ref(false)
  
  // Her item için bir 'href' oluşturun. route() helper'ı da kullanabilirsiniz.
  // Örneğin, Laravel'de route('orders.index') gibi:
  const menuItems = [
    { id: 'home', name: 'Anasayfa', icon: 'fas fa-home', href: '/b2b' },
    { id: 'statement', name: 'Ekstre', icon: 'fas fa-file-invoice', href: '/ekstre' },
    { id: 'orders', name: 'Siparişler', icon: 'fas fa-shopping-bag', href: '/orders' },
    { id: 'basket', name: 'Sepet', icon: 'fas fa-shopping-cart', href: '/b2b/basket' },
    { id: 'payment', name: 'Ödeme', icon: 'fas fa-credit-card', href: '/payment' },
    { id: 'contact', name: 'İletişim', icon: 'fas fa-envelope', href: '/contact' }
  ]
  </script>
  