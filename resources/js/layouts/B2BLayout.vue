<template>
    <div class="min-h-screen bg-gray-50">
        <!-- Fixed Header -->
        <header class="fixed left-0 right-0 top-0 z-50 bg-theme-general shadow-md">
            <div class="mx-auto max-w-7xl px-4">
                <div class="flex h-16 items-center justify-between">
                    <!-- Logo -->
                    <div class="flex items-center">
                        <img src="https://public.readdy.ai/ai/img_res/d8a80eba966b13f1cc0fc304b8860dc6.jpg" alt="LOFA" class="h-8 brightness-0 invert filter" />
                    </div>

                    <!-- Mobile Menu Toggle -->
                    <button @click="isMenuOpen = !isMenuOpen" class="block text-white md:hidden">
                        <i class="fas fa-bars"></i>
                    </button>

                    <!-- Navigation (Desktop) -->
                    <nav class="hidden items-center space-x-8 md:flex">
                        <!-- Inertia Link -->
                        <Link
                            v-for="item in menuItems"
                            :key="item.id"
                            :href="item.href"
                            class="flex cursor-pointer items-center whitespace-nowrap"
                            :class="activeMenu === item.id ? 'font-medium text-theme-primary' : 'text-white hover:text-theme-primary'"
                            @click="activeMenu = item.id"
                        >
                            <i :class="['mr-2', item.icon]"></i>
                            {{ item.name }}
                        </Link>
                    </nav>
                </div>

                <!-- Navigation (Mobile) -->
                <nav v-if="isMenuOpen" class="mt-2 flex flex-col items-start space-y-2 bg-theme-general p-4 shadow-lg md:hidden">
                    <Link
                        v-for="item in menuItems"
                        :key="item.id"
                        :href="item.href"
                        class="flex cursor-pointer items-center whitespace-nowrap"
                        :class="activeMenu === item.id ? 'font-medium text-theme-primary' : 'text-white hover:text-theme-primary'"
                        @click="
                            () => {
                                activeMenu = item.id;
                                isMenuOpen = false;
                            }
                        "
                    >
                        <i :class="['mr-2', item.icon]"></i>
                        {{ item.name }}
                    </Link>
                </nav>
            </div>
        </header>

        <!-- Main Content -->
        <main class="mx-auto max-w-7xl px-4 pb-8 pt-20">
            <slot />
        </main>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
// Inertia Link import’u
import { Link } from '@inertiajs/vue3';

// Menü durumu
const activeMenu = ref('home');
const isMenuOpen = ref(false);

// Her item için bir 'href' oluşturun. route() helper'ı da kullanabilirsiniz.
// Örneğin, Laravel'de route('orders.index') gibi:
const menuItems = [
    { id: 'home', name: 'Anasayfa', icon: 'fas fa-home', href: '/b2b' },
    { id: 'statement', name: 'Ekstre', icon: 'fas fa-file-invoice', href: '/ekstre' },
    { id: 'orders', name: 'Siparişler', icon: 'fas fa-shopping-bag', href: '/orders' },
    { id: 'basket', name: 'Sepet', icon: 'fas fa-shopping-cart', href: '/b2b/basket' },
    { id: 'payment', name: 'Ödeme', icon: 'fas fa-credit-card', href: '/payment' },
    { id: 'contact', name: 'İletişim', icon: 'fas fa-envelope', href: '/contact' },
];
</script>
