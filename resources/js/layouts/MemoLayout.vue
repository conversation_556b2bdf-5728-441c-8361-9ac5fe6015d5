<!-- resources/js/Layouts/MemoLayout.vue -->
<template>
  <div class="min-h-screen bg-white">
    <!-- Header -->
    <header class="fixed w-full z-50 bg-transparent">
      <nav
        class="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center bg-gray-800 bg-opacity-50 rounded-full my-4"
      >
        <div class="text-2xl font-bold text-white h-[50px] flex items-center">
          <img
            src="/memoLogo.png"
            alt="Yacht Charter Logo"
            class="h-[80px] object-contain "
          />
        </div>
        <div class="hidden md:flex space-x-8">
          <a href="/" class="text-white hover:text-blue-200">Home</a>
          <a href="#" class="text-white hover:text-blue-200">Yachts</a>
          <a href="#" class="text-white hover:text-blue-200">Services</a>
          <a href="/memo-contact" class="text-white hover:text-blue-200">Contact</a>
        </div>
        <button class="md:hidden text-white">
          <i class="fas fa-bars text-2xl"></i>
        </button>
      </nav>
    </header>

    <!-- İçerik (Home.vue vb.) -->
    <main >
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white mt-8">
      <div class="max-w-7xl mx-auto px-4 py-16">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 class="text-xl font-bold mb-4">Yacht Charter</h3>
            <p class="text-gray-400">
              10 years of experience in luxury yacht charter
            </p>
          </div>
          <div>
            <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
            <ul class="space-y-2">
              <li>
                <a href="#" class="text-gray-400 hover:text-white">Home</a>
              </li>
              <li>
                <a href="#" class="text-gray-400 hover:text-white">Yachts</a>
              </li>
              <li>
                <a href="#" class="text-gray-400 hover:text-white">Services</a>
              </li>
              <li>
                <a href="/memo-contact" class="text-gray-400 hover:text-white">Contact</a>
              </li>  
            </ul>
          </div>
          <div>
            <h4 class="text-lg font-semibold mb-4">Contact</h4>
            <ul class="space-y-2 text-gray-400">
              <li>
                <i class="fas fa-map-marker-alt mr-2"></i>         881 NW 13th Ave<br />Miami, FL 33125<br />United States
              </li>
              <li><i class="fas fa-phone mr-2"></i>+****************</li>
              <li><i class="fas fa-envelope mr-2"></i><EMAIL></li>
            </ul>
          </div>
          <div>
            <h4 class="text-lg font-semibold mb-4">Social Media</h4>
            <div class="flex space-x-4">
              <a href="#" class="text-gray-400 hover:text-white"
                ><i class="fab fa-facebook text-2xl"></i
              ></a>
              <a href="#" class="text-gray-400 hover:text-white"
                ><i class="fab fa-instagram text-2xl"></i
              ></a>
              <a href="#" class="text-gray-400 hover:text-white"
                ><i class="fab fa-twitter text-2xl"></i
              ></a>
            </div>
          </div>
        </div>
      </div>
      <div class="border-t border-gray-800">
        <div class="max-w-7xl mx-auto px-4 py-4">
          <p class="text-center text-gray-400">
            © 2025 Yacht Charter. All rights reserved.
          </p>
        </div>
      </div>

      <!-- WhatsApp Floating Button -->
      <a
        href="https://api.whatsapp.com/send/?phone=19297054044&text&app_absent=0"
        target="_blank"
        rel="noopener noreferrer"
        class="fixed bottom-4 right-4 z-50
              flex items-center justify-center
              w-14 h-14
              bg-[#25D366] rounded-full
              shadow-lg
              -colors
              hover:bg-[#20b455]"
      >
        <i class="fab fa-whatsapp text-white text-2xl"></i>
      </a>
    </footer>
  </div>
</template>

<script setup lang="ts">
// Layout özelinde ek bir script kullanmanıza genelde gerek olmuyor.
// İhtiyaç varsa burada import vb. yapabilirsiniz.
</script>

<style scoped>
/* Layout'a özgü stiller */
</style>
