import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useCartStore = defineStore('cart', () => {
    // State
    const items = ref([]);
    const isLoading = ref(false);

    // Getters
    const itemCount = computed(() => {
        return items.value.reduce((total, item) => total + item.quantity, 0);
    });

    const subtotal = computed(() => {
        return items.value.reduce((total, item) => total + (item.price * item.quantity), 0);
    });

    const taxAmount = computed(() => {
        // Turkish VAT is 20% for most products
        return subtotal.value * 0.20;
    });

    const total = computed(() => {
        return subtotal.value + taxAmount.value;
    });

    const isEmpty = computed(() => {
        return items.value.length === 0;
    });

    // Actions
    const addItem = (product, quantity = 1) => {
        const existingItem = items.value.find(item => item.id === product.id);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            items.value.push({
                id: product.id,
                name: product.name,
                slug: product.slug,
                price: parseFloat(product.price),
                sku: product.sku,
                image: product.min_order_image_path,
                quantity: quantity,
            });
        }
        
        saveToLocalStorage();
    };

    const removeItem = (productId) => {
        const index = items.value.findIndex(item => item.id === productId);
        if (index > -1) {
            items.value.splice(index, 1);
            saveToLocalStorage();
        }
    };

    const updateQuantity = (productId, quantity) => {
        const item = items.value.find(item => item.id === productId);
        if (item) {
            if (quantity <= 0) {
                removeItem(productId);
            } else {
                item.quantity = quantity;
                saveToLocalStorage();
            }
        }
    };

    const incrementQuantity = (productId) => {
        const item = items.value.find(item => item.id === productId);
        if (item) {
            item.quantity++;
            saveToLocalStorage();
        }
    };

    const decrementQuantity = (productId) => {
        const item = items.value.find(item => item.id === productId);
        if (item && item.quantity > 1) {
            item.quantity--;
            saveToLocalStorage();
        }
    };

    const clearCart = () => {
        items.value = [];
        saveToLocalStorage();
    };

    const saveToLocalStorage = () => {
        try {
            localStorage.setItem('cart', JSON.stringify(items.value));
        } catch (error) {
            console.error('Failed to save cart to localStorage:', error);
        }
    };

    const loadFromLocalStorage = () => {
        try {
            const savedCart = localStorage.getItem('cart');
            if (savedCart) {
                items.value = JSON.parse(savedCart);
            }
        } catch (error) {
            console.error('Failed to load cart from localStorage:', error);
            items.value = [];
        }
    };

    const getItem = (productId) => {
        return items.value.find(item => item.id === productId);
    };

    const isInCart = (productId) => {
        return items.value.some(item => item.id === productId);
    };

    // Initialize cart from localStorage
    loadFromLocalStorage();

    return {
        // State
        items,
        isLoading,
        
        // Getters
        itemCount,
        subtotal,
        taxAmount,
        total,
        isEmpty,
        
        // Actions
        addItem,
        removeItem,
        updateQuantity,
        incrementQuantity,
        decrementQuantity,
        clearCart,
        getItem,
        isInCart,
        loadFromLocalStorage,
    };
});
