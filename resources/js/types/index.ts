import type { LucideIcon } from 'lucide-vue-next';
import type { PageProps } from '@inertiajs/core';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon;
    isActive?: boolean;
}

export interface SharedData extends PageProps {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
}

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    phone?: string;
    bio?: string;
    address?: string;
    city?: string;
    country?: string;
    postal_code?: string;
    birth_date?: string;
    profile_photo_path?: string;
    profile_photo_url?: string;
    preferences?: {
        emailNotifications?: boolean;
        marketingEmails?: boolean;
        darkMode?: boolean;
        language?: string;
    };
    two_factor_enabled?: boolean;
    last_active_at?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
}

export type BreadcrumbItemType = BreadcrumbItem;
