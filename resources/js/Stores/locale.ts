import { defineStore } from 'pinia';

interface LocaleState {
    locale: string;
}

export const useLocaleStore = defineStore('locale', {
    state: (): LocaleState => ({
        locale: localStorage.getItem('locale') || 'tr', // Default to Turkish
    }),
    
    actions: {
        setLocale(locale: string): void {
            this.locale = locale;
            localStorage.setItem('locale', locale);
            
            // Set the HTML lang attribute
            document.documentElement.lang = locale;
            
            // You might want to update the app's locale via an API call here
            fetch(`/api/locale/${locale}`, { method: 'POST' });
        },
    },
});
