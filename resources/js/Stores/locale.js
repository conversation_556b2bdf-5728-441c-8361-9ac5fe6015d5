import { defineStore } from 'pinia';
import { ref } from 'vue';
import { usePage } from '@inertiajs/vue3';
import axios from 'axios';

export const useLocaleStore = defineStore('locale', () => {
    const page = usePage();
    // Try to get locale from localStorage first, then from page props, then default to 'tr'
    const storedLocale = localStorage.getItem('locale');
    const locale = ref(storedLocale || page.props.locale || 'tr');
    
    // Initialize by storing the current locale
    if (locale.value) {
        localStorage.setItem('locale', locale.value);
    }

    const setLocale = async (newLocale) => {
        if (newLocale === locale.value) return;
        
        try {
            // Update the store value immediately
            locale.value = newLocale;
            
            // Store in localStorage for persistence
            localStorage.setItem('locale', newLocale);
            
            // Make the API call to update server-side
            await axios.post(`/api/locale/${newLocale}`);
            
            // No reload here - let the component handle any UI updates
        } catch (error) {
            console.error('Failed to set locale:', error);
            // Revert on error
            locale.value = localStorage.getItem('locale') || page.props.locale || 'tr';
        }
    };

    return {
        locale,
        setLocale
    };
});
