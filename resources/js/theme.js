// Theme initialization - Set CSS custom properties from environment
function initializeThemeColors() {
    // Get theme colors from Inertia shared data or use defaults
    let themeColors = {
        primary: '184 207 206',
        secondary: '160 200 120',
        general: '20 61 96',
        accent: '221 235 157',
    };

    // Try to get colors from Inertia page props
    if (window.page && window.page.props && window.page.props.themeColors) {
        themeColors = window.page.props.themeColors;
    }
    // Fallback: try to get from global window object
    else if (window.themeColors) {
        themeColors = window.themeColors;
    }

    // Set CSS custom properties on document root
    const root = document.documentElement;
    root.style.setProperty('--theme-primary', themeColors.primary);
    root.style.setProperty('--theme-secondary', themeColors.secondary);
    root.style.setProperty('--theme-general', themeColors.general);
    root.style.setProperty('--theme-accent', themeColors.accent);

    // Set RGB versions for use with opacity
    root.style.setProperty('--theme-primary-rgb', themeColors.primary);
    root.style.setProperty('--theme-secondary-rgb', themeColors.secondary);
    root.style.setProperty('--theme-general-rgb', themeColors.general);
    root.style.setProperty('--theme-accent-rgb', themeColors.accent);

    console.log('Theme colors initialized:', themeColors);
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', initializeThemeColors);

// Also initialize on Inertia page visits (for SPA navigation)
document.addEventListener('inertia:finish', initializeThemeColors);

// Export function to update theme colors dynamically
export function updateThemeColors(colors) {
    const root = document.documentElement;
    if (colors.primary) {
        root.style.setProperty('--theme-primary', colors.primary);
        root.style.setProperty('--theme-primary-rgb', colors.primary);
    }
    if (colors.secondary) {
        root.style.setProperty('--theme-secondary', colors.secondary);
        root.style.setProperty('--theme-secondary-rgb', colors.secondary);
    }
    if (colors.general) {
        root.style.setProperty('--theme-general', colors.general);
        root.style.setProperty('--theme-general-rgb', colors.general);
    }
    if (colors.accent) {
        root.style.setProperty('--theme-accent', colors.accent);
        root.style.setProperty('--theme-accent-rgb', colors.accent);
    }
}
