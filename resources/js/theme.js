// Theme initialization - Set CSS custom properties from environment
function initializeThemeColors() {
    // Get theme colors from Inertia shared data or use defaults
    let themeColors = {
        primary: '14 165 233',
        secondary: '79 70 229',
        accent: '249 115 22',
    };

    // Try to get colors from Inertia page props
    if (window.page && window.page.props && window.page.props.themeColors) {
        themeColors = window.page.props.themeColors;
    }
    // Fallback: try to get from global window object
    else if (window.themeColors) {
        themeColors = window.themeColors;
    }

    // Set CSS custom properties on document root
    const root = document.documentElement;
    root.style.setProperty('--color-primary', themeColors.primary);
    root.style.setProperty('--color-secondary', themeColors.secondary);
    root.style.setProperty('--color-accent', themeColors.accent);

    // Set RGB versions for use with opacity
    root.style.setProperty('--color-primary-rgb', themeColors.primary);
    root.style.setProperty('--color-secondary-rgb', themeColors.secondary);
    root.style.setProperty('--color-accent-rgb', themeColors.accent);

    console.log('Theme colors initialized:', themeColors);
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', initializeThemeColors);

// Also initialize on Inertia page visits (for SPA navigation)
document.addEventListener('inertia:finish', initializeThemeColors);

// Export function to update theme colors dynamically
export function updateThemeColors(colors) {
    const root = document.documentElement;
    if (colors.primary) {
        root.style.setProperty('--color-primary', colors.primary);
        root.style.setProperty('--color-primary-rgb', colors.primary);
    }
    if (colors.secondary) {
        root.style.setProperty('--color-secondary', colors.secondary);
        root.style.setProperty('--color-secondary-rgb', colors.secondary);
    }
    if (colors.accent) {
        root.style.setProperty('--color-accent', colors.accent);
        root.style.setProperty('--color-accent-rgb', colors.accent);
    }
}
