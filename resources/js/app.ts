import '../css/app.css';
import './theme.js';

import '@fortawesome/fontawesome-free/css/all.min.css';
import { createInertiaApp } from '@inertiajs/vue3';
import axios from 'axios';
import { createPinia } from 'pinia';
import { createApp, h } from 'vue';
import Vue3Toastify from 'vue3-toastify';
import 'vue3-toastify/dist/index.css';
import { ZiggyVue } from '../../vendor/tightenco/ziggy';
import { initializeTheme } from './composables/useAppearance';

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';
const pinia = createPinia();

// Axios CSRF token konfigürasyonu
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    axios.defaults.headers.common['X-CSRF-TOKEN'] = token.getAttribute('content');
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

// Theme renklerini ayarlayan fonksiyon
function initializeThemeColors(page: any) {
    if (page.props.themeColors) {
        const colors = page.props.themeColors;
        const root = document.documentElement;

        root.style.setProperty('--theme-primary', colors.primary);
        root.style.setProperty('--theme-secondary', colors.secondary);
        root.style.setProperty('--theme-general', colors.general);
        root.style.setProperty('--theme-accent', colors.accent);

        // RGB versiyonları da set et
        root.style.setProperty('--theme-primary-rgb', colors.primary);
        root.style.setProperty('--theme-secondary-rgb', colors.secondary);
        root.style.setProperty('--theme-general-rgb', colors.general);
        root.style.setProperty('--theme-accent-rgb', colors.accent);
    }
}

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: async (name) => {
        const pages = import.meta.glob('./pages/**/*.vue');
        const pageModule = (await pages[`./pages/${name}.vue`]()) as any;
        return pageModule.default;
    },
    setup({ el, App, props, plugin }) {
        // Theme renklerini ilk yüklemede ayarla
        initializeThemeColors(props.initialPage);

        createApp({
            render: () => h(App, props),
            mounted() {
                // Component mount edildiğinde de theme renklerini ayarla
                initializeThemeColors(props.initialPage);
            },
        })
            .use(plugin)
            .use(Vue3Toastify)
            .use(ZiggyVue)
            .use(pinia)
            .mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});

// Sayfa yüklendiğinde tema ayarını uygular.
initializeTheme();
