<template>
    <a
      :href="href"
      class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium 
             rounded-md shadow-sm focus:outline-none 
             bg-blue-600 text-white hover:bg-blue-700 text-center content-center"
    >
      <!-- The default slot will be the text or elements you pass to the component -->
      <slot>Default Button Text</slot>
    </a>
  </template>
  
  <script>
  export default {
    name: "ButtonLink",
    props: {
      // If you want to use this as a normal link,
      // just pass the external URL in 'href'.
      href: {
        type: String,
        default: "#",
      },
    },
  };
  </script>
  