<template>
    <a :href="href" class="inline-flex content-center items-center rounded-md border border-transparent bg-theme-primary px-4 py-2 text-center text-sm font-medium text-white shadow-sm hover:bg-theme-primary/90 focus:outline-none">
        <!-- The default slot will be the text or elements you pass to the component -->
        <slot>Default Button Text</slot>
    </a>
</template>

<script>
export default {
    name: 'ButtonLink',
    props: {
        // If you want to use this as a normal link,
        // just pass the external URL in 'href'.
        href: {
            type: String,
            default: '#',
        },
    },
};
</script>
