<script setup>
import { ref, computed, watch } from 'vue';
import { router } from '@inertiajs/vue3';
import { Search, X, Loader2 } from 'lucide-vue-next';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useLocaleStore } from '@/Stores/locale';
import { debounce } from 'lodash';

const props = defineProps({
    trigger: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['close']);

const isOpen = ref(false);
const searchQuery = ref('');
const isLoading = ref(false);
const searchResults = ref({
    products: [],
    pages: [],
    galleries: []
});
const activeIndex = ref(0);
const totalResults = ref(0);

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

// Translations
const translations = {
    tr: {
        searchPlaceholder: '<PERSON><PERSON><PERSON><PERSON>, sayfa veya galeri ara...',
        noResults: '<PERSON>uç bulunamadı',
        products: 'Ürünler',
        pages: 'Sayfalar',
        galleries: 'Galeri',
        searching: 'Aranıyor...'
    },
    en: {
        searchPlaceholder: 'Search for products, pages or gallery...',
        noResults: 'No results found',
        products: 'Products',
        pages: 'Pages',
        galleries: 'Gallery',
        searching: 'Searching...'
    }
};

const t = computed(() => translations[locale.value]);

// Watch for trigger prop changes
watch(() => props.trigger, (newVal) => {
    if (newVal) {
        isOpen.value = true;
    }
});

// Watch for dialog open/close
watch(isOpen, (newVal) => {
    if (!newVal) {
        emit('close');
    } else {
        // Focus the input when dialog opens
        setTimeout(() => {
            document.getElementById('global-search-input')?.focus();
        }, 100);
    }
});

// Debounced search function
const performSearch = debounce(async () => {
    if (!searchQuery.value.trim()) {
        searchResults.value = { products: [], pages: [], galleries: [] };
        totalResults.value = 0;
        isLoading.value = false;
        return;
    }

    isLoading.value = true;
    
    try {
        const response = await fetch(`/api/search?q=${encodeURIComponent(searchQuery.value)}`);
        const data = await response.json();
        
        searchResults.value = data;
        totalResults.value = 
            data.products.length + 
            data.pages.length + 
            data.galleries.length;
            
        // Reset active index
        activeIndex.value = 0;
    } catch (error) {
        console.error('Search error:', error);
    } finally {
        isLoading.value = false;
    }
}, 300);

// Watch for search query changes
watch(searchQuery, () => {
    performSearch();
});

// Handle keyboard navigation
const handleKeyDown = (e) => {
    if (totalResults.value === 0) return;
    
    // Arrow down
    if (e.key === 'ArrowDown') {
        e.preventDefault();
        activeIndex.value = (activeIndex.value + 1) % totalResults.value;
    }
    
    // Arrow up
    if (e.key === 'ArrowUp') {
        e.preventDefault();
        activeIndex.value = (activeIndex.value - 1 + totalResults.value) % totalResults.value;
    }
    
    // Enter
    if (e.key === 'Enter') {
        e.preventDefault();
        handleResultClick(getActiveResult());
    }
};

// Get the active result based on activeIndex
const getActiveResult = () => {
    let index = activeIndex.value;
    const { products, pages, galleries } = searchResults.value;
    
    if (index < products.length) {
        return { type: 'product', item: products[index] };
    }
    
    index -= products.length;
    if (index < pages.length) {
        return { type: 'page', item: pages[index] };
    }
    
    index -= pages.length;
    return { type: 'gallery', item: galleries[index] };
};

// Handle result click
const handleResultClick = (result) => {
    if (!result) return;
    
    isOpen.value = false;
    
    switch (result.type) {
        case 'product':
            router.visit(route('front.products.show', result.item.slug));
            break;
        case 'page':
            router.visit(route('front.pages.show', result.item.slug));
            break;
        case 'gallery':
            router.visit(route('front.gallery.index'));
            break;
    }
};

// Check if a result is active
const isResultActive = (type, index) => {
    let baseIndex = 0;
    
    if (type === 'product') {
        return activeIndex.value === index;
    }
    
    baseIndex += searchResults.value.products.length;
    if (type === 'page') {
        return activeIndex.value === baseIndex + index;
    }
    
    baseIndex += searchResults.value.pages.length;
    return activeIndex.value === baseIndex + index;
};
</script>

<template>
    <Dialog v-model:open="isOpen">
        <DialogTrigger as-child>
            <Button variant="ghost" size="icon" class="group h-9 w-9 cursor-pointer">
                <Search class="size-5 opacity-80 group-hover:opacity-100" />
            </Button>
        </DialogTrigger>
        <DialogContent class="sm:max-w-[600px] p-0">
            <div class="flex flex-col">
                <!-- Search input -->
                <div class="flex items-center border-b p-4">
                    <Search class="mr-2 h-5 w-5 shrink-0 opacity-50" />
                    <Input
                        id="global-search-input"
                        v-model="searchQuery"
                        type="text"
                        :placeholder="t.searchPlaceholder"
                        class="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                        @keydown="handleKeyDown"
                    />
                    <Button v-if="searchQuery" variant="ghost" size="sm" class="h-8 px-2" @click="searchQuery = ''">
                        <X class="h-4 w-4" />
                    </Button>
                </div>
                
                <!-- Loading state -->
                <div v-if="isLoading" class="flex items-center justify-center py-6">
                    <Loader2 class="h-6 w-6 animate-spin text-muted-foreground" />
                    <span class="ml-2 text-sm text-muted-foreground">{{ t.searching }}</span>
                </div>
                
                <!-- No results -->
                <div v-else-if="searchQuery && totalResults === 0" class="p-6 text-center">
                    <p class="text-sm text-muted-foreground">{{ t.noResults }}</p>
                </div>
                
                <!-- Results -->
                <div v-else-if="searchQuery" class="max-h-[60vh] overflow-auto p-2">
                    <!-- Products -->
                    <div v-if="searchResults.products.length > 0" class="mb-4">
                        <h3 class="px-2 text-xs font-semibold text-muted-foreground">{{ t.products }}</h3>
                        <div class="mt-2 space-y-1">
                            <div
                                v-for="(product, index) in searchResults.products"
                                :key="`product-${product.id}`"
                                class="flex cursor-pointer items-center rounded-md px-2 py-1 hover:bg-accent"
                                :class="{ 'bg-accent': isResultActive('product', index) }"
                                @click="handleResultClick({ type: 'product', item: product })"
                            >
                                <div class="flex-shrink-0 mr-2">
                                    <img
                                        :src="product.image_path || '/images/product-placeholder.jpg'"
                                        class="h-8 w-8 rounded-md object-cover"
                                        :alt="product.name[locale]"
                                    />
                                </div>
                                <div class="flex-1 truncate">
                                    <div class="text-sm font-medium">{{ product.name[locale] }}</div>
                                    <div class="text-xs text-muted-foreground">{{ product.price }} TL</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pages -->
                    <div v-if="searchResults.pages.length > 0" class="mb-4">
                        <h3 class="px-2 text-xs font-semibold text-muted-foreground">{{ t.pages }}</h3>
                        <div class="mt-2 space-y-1">
                            <div
                                v-for="(page, index) in searchResults.pages"
                                :key="`page-${page.id}`"
                                class="flex cursor-pointer items-center rounded-md px-2 py-1 hover:bg-accent"
                                :class="{ 'bg-accent': isResultActive('page', index) }"
                                @click="handleResultClick({ type: 'page', item: page })"
                            >
                                <div class="flex-1 truncate">
                                    <div class="text-sm font-medium">{{ page.name[locale] }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Galleries -->
                    <div v-if="searchResults.galleries.length > 0" class="mb-4">
                        <h3 class="px-2 text-xs font-semibold text-muted-foreground">{{ t.galleries }}</h3>
                        <div class="mt-2 space-y-1">
                            <div
                                v-for="(gallery, index) in searchResults.galleries"
                                :key="`gallery-${gallery.id}`"
                                class="flex cursor-pointer items-center rounded-md px-2 py-1 hover:bg-accent"
                                :class="{ 'bg-accent': isResultActive('gallery', index) }"
                                @click="handleResultClick({ type: 'gallery', item: gallery })"
                            >
                                <div class="flex-shrink-0 mr-2">
                                    <img
                                        :src="gallery.image_path || '/images/gallery-placeholder.jpg'"
                                        class="h-8 w-8 rounded-md object-cover"
                                        :alt="gallery.title[locale]"
                                    />
                                </div>
                                <div class="flex-1 truncate">
                                    <div class="text-sm font-medium">{{ gallery.title[locale] }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </DialogContent>
    </Dialog>
</template>
