&lt;script setup lang="ts"&gt;
import { ref } from 'vue'
import { useCookies } from '@vueuse/core'

const cookies = useCookies()
const showBanner = ref(!cookies.get('gdpr-consent'))

const acceptAll = () => {
  cookies.set('gdpr-consent', 'all', { expires: 365 })
  showBanner.value = false
}

const acceptEssential = () => {
  cookies.set('gdpr-consent', 'essential', { expires: 365 })
  showBanner.value = false
}
&lt;/script&gt;

&lt;template&gt;
  &lt;div
    v-if="showBanner"
    class="fixed bottom-0 left-0 right-0 z-50 bg-white p-4 shadow-lg dark:bg-gray-800"
  &gt;
    &lt;div class="container mx-auto"&gt;
      &lt;div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between"&gt;
        &lt;div class="flex-1"&gt;
          &lt;p class="text-sm text-gray-600 dark:text-gray-400"&gt;
            Bu web sitesi, size en iyi deneyimi sunmak için çerezleri kullanmaktadır.
            Gizlilik politikamızı inceleyebilir ve çerez tercihlerinizi
            güncelleyebilirsiniz.
          &lt;/p&gt;
        &lt;/div&gt;
        &lt;div class="flex gap-2"&gt;
          &lt;button
            @click="acceptEssential"
            class="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          &gt;
            Sadece Gerekli Çerezler
          &lt;/button&gt;
          &lt;button
            @click="acceptAll"
            class="rounded-md bg-primary-600 px-4 py-2 text-sm font-medium text-white hover:bg-primary-700"
          &gt;
            Tümünü Kabul Et
          &lt;/button&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;
