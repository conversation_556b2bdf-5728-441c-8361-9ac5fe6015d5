<script setup>
import { useLocaleStore } from '@/Stores/locale';
import { computed, onMounted, ref } from 'vue';

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const showConsent = ref(false);

onMounted(() => {
    // Check if user has already consented to cookies
    if (!localStorage.getItem('cookie_consent')) {
        showConsent.value = true;
    }
});

const accept = () => {
    localStorage.setItem('cookie_consent', 'accepted');
    showConsent.value = false;
};

const acceptOnlyEssential = () => {
    localStorage.setItem('cookie_consent', 'essential_only');
    showConsent.value = false;
};
</script>

<template>
    <div v-if="showConsent" class="fixed bottom-0 left-0 z-50 w-full bg-gray-900 p-4 text-white shadow-lg">
        <div class="container mx-auto flex flex-col items-center justify-between md:flex-row">
            <div class="mb-4 md:mb-0 md:mr-4">
                <h3 class="mb-1 text-lg font-semibold">
                    {{ locale === 'tr' ? 'Çerez Politikası' : 'Cookie Policy' }}
                </h3>
                <p class="text-sm">
                    {{
                        locale === 'tr'
                            ? 'Bu web sitesi, deneyiminizi geliştirmek için çerezleri kullanmaktadır. Sitemizi kullanmaya devam ederek çerezlerin kullanımını kabul etmiş olursunuz.'
                            : 'This website uses cookies to improve your experience. By continuing to use our site, you consent to our use of cookies.'
                    }}
                </p>
                <p class="mt-1 text-sm">
                    <a href="/privacy-policy" class="underline hover:text-gray-300">
                        {{ locale === 'tr' ? 'Gizlilik Politikamız' : 'Our Privacy Policy' }}
                    </a>
                </p>
            </div>
            <div class="flex flex-col space-y-2 sm:flex-row sm:space-x-2 sm:space-y-0">
                <button @click="acceptOnlyEssential" class="-colors rounded bg-gray-700 px-4 py-2 text-white hover:bg-gray-600">
                    {{ locale === 'tr' ? 'Sadece Gerekli Çerezler' : 'Essential Cookies Only' }}
                </button>
                <button @click="accept" class="-colors rounded bg-[#56509a] px-4 py-2 text-white hover:bg-indigo-500">
                    {{ locale === 'tr' ? 'Tüm Çerezleri Kabul Et' : 'Accept All Cookies' }}
                </button>
            </div>
        </div>
    </div>
</template>
