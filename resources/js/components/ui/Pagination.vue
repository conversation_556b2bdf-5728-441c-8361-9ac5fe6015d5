<script setup lang="ts">
import { Link } from '@inertiajs/vue3';
import { computed } from 'vue';

const props = defineProps({
    links: {
        type: Array,
        required: true
    }
});

const hasPages = computed(() => {
    return props.links && props.links.length > 3;
});

const prevLink = computed(() => {
    return props.links.find(link => link.label.includes('Previous'));
});

const nextLink = computed(() => {
    return props.links.find(link => link.label.includes('Next'));
});    // Filtrelenmiş bağlantılar - ilk, son ve aktif sayfa bağlantılarını içerir
const filteredLinks = computed(() => {
    if (!props.links) return [];
    
    return props.links.filter(link => {
        // "Önceki" ve "Sonraki" bağlantılarını hariç tut
        return !link.label.includes('Previous') && !link.label.includes('Next');
    });
});
</script>

<template>
    <div v-if="hasPages" class="flex justify-between items-center">
        <div class="flex items-center">
            <!-- <PERSON><PERSON><PERSON> Sayfa Bağlantısı -->
            <Link 
                v-if="prevLink && prevLink.url" 
                :href="prevLink.url" 
                class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
                <span>&laquo; Önceki</span>
            </Link>
            <button 
                v-else 
                class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-300 bg-white border border-gray-300 rounded-md cursor-not-allowed"
                disabled
            >
                <span>&laquo; Önceki</span>
            </button>
        </div>

        <div class="hidden md:flex">
            <!-- Sayfa Numaraları -->
            <template v-for="(link, i) in filteredLinks" :key="i">
                <div v-if="link.url === null" class="mx-1 px-4 py-2 text-gray-500 bg-white rounded-md">
                    ...
                </div>
                <Link 
                    v-else 
                    :href="link.url" 
                    :class="[
                        'mx-1 px-4 py-2 rounded-md',
                        link.active 
                            ? 'bg-[#56509a] text-white' 
                            : 'text-gray-700 bg-white hover:bg-gray-50'
                    ]"
                    v-html="link.label"
                >
                </Link>
            </template>
        </div>

        <div class="flex items-center">
            <!-- Sonraki Sayfa Bağlantısı -->
            <Link 
                v-if="nextLink && nextLink.url" 
                :href="nextLink.url" 
                class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
                <span>Sonraki &raquo;</span>
            </Link>
            <button 
                v-else 
                class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-300 bg-white border border-gray-300 rounded-md cursor-not-allowed"
                disabled
            >
                <span>Sonraki &raquo;</span>
            </button>
        </div>
    </div>
</template>
