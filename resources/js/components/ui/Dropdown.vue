<script setup>
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue';

const props = defineProps({
  align: {
    type: String,
    default: 'right',
  },
  width: {
    type: String,
    default: '48',
  },
  contentClasses: {
    type: Array,
    default: () => ['py-1', 'bg-white', 'dark:bg-gray-700']
  },
});

const closeOnEscape = (e) => {
  if (open.value && e.key === 'Escape') {
    open.value = false;
  }
};

const open = ref(false);

watch(open, value => {
  if (value) {
    document.addEventListener('keydown', closeOnEscape);
  } else {
    document.removeEventListener('keydown', closeOnEscape);
  }
});

const closeOnClickOutside = (event) => {
  if (!event.target.closest('[data-dropdown]')) {
    open.value = false;
  }
}

onMounted(() => {
  document.addEventListener('click', closeOnClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('keydown', closeOnEscape);
  document.removeEventListener('click', closeOnClickOutside);
});

const toggleDropdown = () => {
  open.value = !open.value;
};

defineExpose({ open, toggleDropdown });

const widthClass = {
  '48': 'w-48',
}[props.width.toString()];

const alignmentClasses = (() => {
  if (props.align === 'left') {
    return 'origin-top-left left-0';
  } else if (props.align === 'right') {
    return 'origin-top-right right-0';
  } else {
    return 'origin-top';
  }
})();
</script>

<template>
  <div class="relative" data-dropdown>
    <div @click="toggleDropdown">
      <slot name="trigger" />
    </div>

    <!-- Full Screen Overlay (Mobile) -->
    <div 
      v-show="open" 
      class="fixed inset-0 z-40 md:hidden" 
      @click="open = false"
    ></div>

    <transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <div
        v-show="open"
        class="absolute z-50 mt-2 rounded-md shadow-lg"
        :class="[widthClass, alignmentClasses]"
        @click="open = false"
      >
        <div class="rounded-md ring-1 ring-black ring-opacity-5" :class="contentClasses">
          <slot name="content" />
        </div>
      </div>
    </transition>
  </div>
</template>
