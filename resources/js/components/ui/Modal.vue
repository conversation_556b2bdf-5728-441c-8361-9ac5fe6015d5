<template>
  <Teleport to="body">
    <transition
      leave-active-class="duration-200"
    >
      <div v-show="show" class="fixed inset-0 overflow-y-auto px-4 py-6 sm:px-0 z-50">
        <transition
          enter-active-class="ease-out duration-300"
          enter-from-class="opacity-0"
          enter-to-class="opacity-100"
          leave-active-class="ease-in duration-200"
          leave-from-class="opacity-100"
          leave-to-class="opacity-0"
        >
          <div
            v-show="show"
            class="fixed inset-0 transform -all"
            @click="close"
          >
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>
        </transition>

        <transition
          enter-active-class="ease-out duration-300"
          enter-from-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          enter-to-class="opacity-100 translate-y-0 sm:scale-100"
          leave-active-class="ease-in duration-200"
          leave-from-class="opacity-100 translate-y-0 sm:scale-100"
          leave-to-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        >
          <div
            v-show="show"
            class="mb-6 bg-white rounded-lg overflow-hidden shadow-xl transform -all sm:w-full sm:mx-auto"
            :class="maxWidthClass"
          >
            <slot></slot>
          </div>
        </transition>
      </div>
    </transition>
  </Teleport>
</template>

<script>
import { computed, watch, onMounted, onUnmounted } from 'vue';

export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    maxWidth: {
      type: String,
      default: '2xl',
    },
    closeable: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['close'],
  setup(props, { emit }) {
    const close = () => {
      if (props.closeable) {
        emit('close');
      }
    };

    const closeOnEscape = (e) => {
      if (e.key === 'Escape' && props.show) {
        close();
      }
    };

    const maxWidthClass = computed(() => {
      return {
        'sm:max-w-sm': props.maxWidth === 'sm',
        'sm:max-w-md': props.maxWidth === 'md',
        'sm:max-w-lg': props.maxWidth === 'lg',
        'sm:max-w-xl': props.maxWidth === 'xl',
        'sm:max-w-2xl': props.maxWidth === '2xl',
        'sm:max-w-3xl': props.maxWidth === '3xl',
        'sm:max-w-4xl': props.maxWidth === '4xl',
        'sm:max-w-5xl': props.maxWidth === '5xl',
        'sm:max-w-6xl': props.maxWidth === '6xl',
        'sm:max-w-7xl': props.maxWidth === '7xl',
      };
    });

    watch(
      () => props.show,
      (value) => {
        if (value) {
          document.body.style.overflow = 'hidden';
        } else {
          document.body.style.overflow = null;
        }
      }
    );

    onMounted(() => {
      document.addEventListener('keydown', closeOnEscape);
    });

    onUnmounted(() => {
      document.removeEventListener('keydown', closeOnEscape);
      document.body.style.overflow = null;
    });

    return {
      close,
      maxWidthClass,
    };
  },
};
</script>
