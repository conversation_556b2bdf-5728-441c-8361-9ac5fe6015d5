<script setup lang="ts">
defineProps({
    paginator: {
        type: Object,
        required: true,
    },
});

const makeLabel = (label) => {
    if (label.includes('Previous')) {
        return '<PERSON><PERSON><PERSON>';
    } else if (label.includes('Next')) {
        return '<PERSON><PERSON><PERSON>';
    } else {
        return label;
    }
};
</script>

<template>
    <div class="flex items-start justify-between">
        <div class="flex items-center overflow-hidden rounded-md shadow-lg">
            <div v-for="link in paginator.links" :key="link.url">
                <component
                    :is="link.url ? 'Link' : 'a'"
                    :href="link.url"
                    :key="link.label"
                    class="grid h-12 w-12 place-items-center border-x border-slate-50 bg-white"
                    :class="{
                        'text-black hover:bg-slate-300': link.url,
                        'text-zinc-400': !link.url,
                        'font-bold text-theme-primary': link.active,
                    }"
                >
                    {{ makeLabel(link.label) }}
                </component>
            </div>
        </div>
        <p class="text-skate-600 text-sm">{{ paginator.from }} ile {{ paginator.to }} aras<PERSON> gösteriliyor (toplam {{ paginator.total }} sonuç)</p>
    </div>
</template>
