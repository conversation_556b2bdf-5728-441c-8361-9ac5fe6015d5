<script setup lang="ts">
import { useLocaleStore } from '@/Stores/locale';
import { computed, onMounted, onUnmounted, ref } from 'vue';

interface Banner {
    image: string;
    title?: string;
    subtitle?: string;
    description?: string;
    button_text?: string;
}

const props = defineProps<{
    banners: Banner[];
    autoplay?: boolean;
    interval?: number;
    isHero?: boolean;
}>();

// Set default values
const autoplay = props.autoplay ?? true;
const interval = props.interval ?? 5000;
const isHero = props.isHero ?? false;

const currentIndex = ref(0);
const slideTimer = ref<number | null>(null);
const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

const next = () => {
    if (props.banners.length <= 1) return;
    currentIndex.value = (currentIndex.value + 1) % props.banners.length;
};

const prev = () => {
    if (props.banners.length <= 1) return;
    currentIndex.value = (currentIndex.value - 1 + props.banners.length) % props.banners.length;
};

const goToSlide = (index: number) => {
    currentIndex.value = index;
    resetTimer();
};

const resetTimer = () => {
    if (slideTimer.value) {
        clearInterval(slideTimer.value);
    }

    if (autoplay && props.banners.length > 1) {
        slideTimer.value = setInterval(next, interval);
    }
};

onMounted(() => {
    if (autoplay && props.banners.length > 1) {
        slideTimer.value = setInterval(next, interval);
    }
});

onUnmounted(() => {
    if (slideTimer.value) {
        clearInterval(slideTimer.value);
    }
});
</script>

<template>
    <div v-if="banners && banners.length > 0" class="relative overflow-hidden">
        <!-- Slider container -->
        <div class="relative w-full" :class="isHero ? 'h-[500px] md:h-[600px] lg:h-[700px]' : 'h-[400px] md:h-[500px]'">
            >
            <div
                v-for="(banner, index) in banners"
                :key="index"
                class="-opacity absolute inset-0 h-full w-full duration-500 ease-in-out"
                :class="{ 'opacity-100': currentIndex === index, 'opacity-0': currentIndex !== index }"
                :style="{ 'z-index': currentIndex === index ? 10 : 0 }"
            >
                <!-- Banner image as background -->
                <div class="absolute inset-0 bg-cover bg-center" :style="{ backgroundImage: `url('${banner.image}')` }">
                    <div class="absolute inset-0 bg-black bg-opacity-40"></div>
                </div>

                <!-- Banner content -->
                <div class="relative z-10 flex h-full flex-col items-center justify-center px-4 text-center text-white">
                    <!-- Main Title -->
                    <h1 class="mb-4 text-4xl font-bold leading-tight md:text-5xl lg:text-6xl">
                        {{ banner.title || 'Doğal Yaşamın' }}
                        <br />
                        <span class="text-theme-secondary">{{ banner.subtitle || 'Gücünü Keşfedin!' }}</span>
                    </h1>

                    <!-- Description -->
                    <p class="mb-8 max-w-2xl text-lg opacity-90 md:text-xl">
                        {{ banner.description || 'Doğadan gelen en saf takviyelerle sağlığınızı destekleyin. Katkı maddesi yok, sadece doğal güç.' }}
                    </p>

                    <!-- Action buttons -->
                    <div class="mb-12 flex flex-col items-center gap-4 sm:flex-row">
                        <button class="rounded-lg bg-green-500 px-8 py-3 font-semibold text-white transition-colors hover:bg-green-600">
                            {{ banner.button_text || 'Ürünleri Keşfet' }}
                        </button>
                        <div class="relative">
                            <input type="text" placeholder="Ürün ara..." class="rounded-lg bg-white px-4 py-3 pr-12 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-400" />
                            <button class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Stats -->
                    <div class="flex flex-col gap-8 sm:flex-row sm:gap-16">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-theme-secondary md:text-4xl">100%</div>
                            <div class="text-sm opacity-90">Doğal İçerik</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-theme-secondary md:text-4xl">0</div>
                            <div class="text-sm opacity-90">Katkı Maddesi</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-theme-secondary md:text-4xl">10+</div>
                            <div class="text-sm opacity-90">Yıllık Deneyim</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Previous/Next controls with increased visibility -->
        <div v-if="banners.length > 1" class="absolute inset-0 z-20 flex items-center justify-between px-4">
            <button @click="prev" class="transform rounded-full bg-black bg-opacity-50 p-2 text-white hover:scale-110 hover:bg-opacity-70 focus:outline-none" :class="isHero ? 'p-3' : 'p-2'">
                <svg xmlns="http://www.w3.org/2000/svg" :class="isHero ? 'h-8 w-8' : 'h-6 w-6'" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </button>
            <button @click="next" class="transform rounded-full bg-black bg-opacity-50 p-2 text-white hover:scale-110 hover:bg-opacity-70 focus:outline-none" :class="isHero ? 'p-3' : 'p-2'">
                <svg xmlns="http://www.w3.org/2000/svg" :class="isHero ? 'h-8 w-8' : 'h-6 w-6'" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
        </div>

        <!-- Dots indicators at bottom with increased visibility -->
        <div v-if="banners.length > 1" class="absolute bottom-8 left-0 right-0 z-30 flex justify-center" :class="isHero ? 'space-x-3' : 'space-x-2'">
            <button
                v-for="(_, index) in banners"
                :key="index"
                @click="goToSlide(index)"
                class="-colors rounded-full duration-300 focus:outline-none"
                :class="[isHero ? 'h-5 w-5' : 'h-4 w-4', currentIndex === index ? 'bg-white' : 'bg-white bg-opacity-70 hover:bg-opacity-90']"
            ></button>
        </div>
    </div>
    <div v-else class="flex h-32 items-center justify-center rounded-lg bg-gray-100">
        <p class="text-gray-500">{{ locale === 'en' ? 'No banners available' : 'Banner bulunamadı' }}</p>
    </div>
</template>
