<script setup>
import { Link } from '@inertiajs/vue3';
import { useCartStore } from '@/stores/cart';
import { computed } from 'vue';
import { useLocaleStore } from '@/Stores/locale';

const cartStore = useCartStore();
const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
</script>

<template>
    <Link 
        :href="route('cart.index')" 
        class="relative inline-flex items-center p-2 text-gray-700 hover:text-blue-600 transition-colors duration-200"
        :title="locale === 'tr' ? 'Sepet' : 'Shopping Cart'"
    >
        <!-- Cart Icon -->
        <svg 
            xmlns="http://www.w3.org/2000/svg" 
            class="h-6 w-6" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
        >
            <path 
                stroke-linecap="round" 
                stroke-linejoin="round" 
                stroke-width="2" 
                d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.1 5H19M7 13v6a2 2 0 002 2h6a2 2 0 002-2v-6m-8 0V9a2 2 0 012-2h4a2 2 0 012 2v4" 
            />
        </svg>
        
        <!-- Item Count Badge -->
        <span 
            v-if="cartStore.itemCount > 0"
            class="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full min-w-[1.25rem] h-5"
        >
            {{ cartStore.itemCount > 99 ? '99+' : cartStore.itemCount }}
        </span>
        
        <!-- Screen Reader Text -->
        <span class="sr-only">
            {{ locale === 'tr' ? 'Sepet' : 'Shopping Cart' }}
            <span v-if="cartStore.itemCount > 0">
                - {{ cartStore.itemCount }} {{ locale === 'tr' ? 'ürün' : 'items' }}
            </span>
        </span>
    </Link>
</template>
