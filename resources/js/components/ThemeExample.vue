&lt;template&gt; &lt;div class="theme-example p-6 space-y-4"&gt; &lt;h2 class="text-2xl font-bold mb-4"&gt;Theme Colors Example&lt;/h2&gt; &lt;!-- CSS Classes ile kullanım --&gt; &lt;div class="space-y-2"&gt; &lt;h3 class="text-lg
font-semibold"&gt;CSS Classes ile:&lt;/h3&gt; &lt;div class="flex gap-2"&gt; &lt;div class="bg-theme-primary text-white p-3 rounded"&gt;Primary&lt;/div&gt; &lt;div class="bg-theme-secondary text-white p-3 rounded"&gt;Secondary&lt;/div&gt; &lt;div
class="bg-theme-accent text-white p-3 rounded"&gt;Accent&lt;/div&gt; &lt;/div&gt; &lt;/div&gt; &lt;!-- Tailwind utility classes ile kullanım --&gt; &lt;div class="space-y-2"&gt; &lt;h3 class="text-lg font-semibold"&gt;Tailwind Utility Classes
ile:&lt;/h3&gt; &lt;div class="flex gap-2"&gt; &lt;div class="bg-theme-primary text-white p-3 rounded hover:bg-opacity-80"&gt;Primary&lt;/div&gt; &lt;div class="bg-theme-secondary text-white p-3 rounded hover:bg-opacity-80"&gt;Secondary&lt;/div&gt;
&lt;div class="bg-theme-accent text-white p-3 rounded hover:bg-opacity-80"&gt;Accent&lt;/div&gt; &lt;/div&gt; &lt;/div&gt; &lt;!-- Inline styles ile kullanım (Composable kullanarak) --&gt; &lt;div class="space-y-2"&gt; &lt;h3 class="text-lg
font-semibold"&gt;Composable ile Inline Styles:&lt;/h3&gt; &lt;div class="flex gap-2"&gt; &lt;div :style="primaryStyle" class="text-white p-3 rounded"&gt;Primary&lt;/div&gt; &lt;div :style="secondaryStyle" class="text-white p-3
rounded"&gt;Secondary&lt;/div&gt; &lt;div :style="accentStyle" class="text-white p-3 rounded"&gt;Accent&lt;/div&gt; &lt;/div&gt; &lt;/div&gt; &lt;!-- Text colors --&gt; &lt;div class="space-y-2"&gt; &lt;h3 class="text-lg font-semibold"&gt;Text
Colors:&lt;/h3&gt; &lt;div class="space-y-1"&gt; &lt;p class="text-theme-primary font-bold"&gt;Primary colored text&lt;/p&gt; &lt;p class="text-theme-secondary font-bold"&gt;Secondary colored text&lt;/p&gt; &lt;p class="text-theme-accent
font-bold"&gt;Accent colored text&lt;/p&gt; &lt;/div&gt; &lt;/div&gt; &lt;!-- Border colors --&gt; &lt;div class="space-y-2"&gt; &lt;h3 class="text-lg font-semibold"&gt;Border Colors:&lt;/h3&gt; &lt;div class="flex gap-2"&gt; &lt;div class="border-2
border-theme-primary p-3 rounded"&gt;Primary Border&lt;/div&gt; &lt;div class="border-2 border-theme-secondary p-3 rounded"&gt;Secondary Border&lt;/div&gt; &lt;div class="border-2 border-theme-accent p-3 rounded"&gt;Accent Border&lt;/div&gt;
&lt;/div&gt; &lt;/div&gt; &lt;!-- Debug info --&gt; &lt;div class="mt-8 p-4 bg-gray-100 rounded"&gt; &lt;h3 class="text-lg font-semibold mb-2"&gt;Current Theme Colors:&lt;/h3&gt; &lt;div class="text-sm space-y-1"&gt;
&lt;p&gt;&lt;strong&gt;Primary:&lt;/strong&gt; {{ getPrimaryRGB() }}&lt;/p&gt; &lt;p&gt;&lt;strong&gt;Secondary:&lt;/strong&gt; {{ getSecondaryRGB() }}&lt;/p&gt; &lt;p&gt;&lt;strong&gt;Accent:&lt;/strong&gt; {{ getAccentRGB() }}&lt;/p&gt;
&lt;/div&gt; &lt;/div&gt; &lt;/div&gt; &lt;/template&gt; &lt;script setup lang="ts"&gt; import { useTheme } from '@/composables/useTheme'; const { primaryStyle, secondaryStyle, accentStyle, getPrimaryRGB, getSecondaryRGB, getAccentRGB,
setCSSCustomProperties } = useTheme(); // CSS custom properties'i set et setCSSCustomProperties(); &lt;/script&gt;
