<script setup>
import { ref, watch, onMounted } from 'vue';
import { QuillEditor } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    placeholder: {
        type: String,
        default: 'Write something...'
    },
    toolbar: {
        type: Array,
        default: () => [
            ['bold', 'italic', 'underline', 'strike'],
            ['blockquote', 'code-block'],
            [{ 'header': 1 }, { 'header': 2 }],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'script': 'sub'}, { 'script': 'super' }],
            [{ 'indent': '-1'}, { 'indent': '+1' }],
            [{ 'direction': 'rtl' }],
            [{ 'size': ['small', false, 'large', 'huge'] }],
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'font': [] }],
            [{ 'align': [] }],
            ['clean'],
            ['link', 'image', 'video']
        ]
    },
    editorHeight: {
        type: String,
        default: '200px'
    }
});

const emit = defineEmits(['update:modelValue']);

const editor = ref(null);
const content = ref(props.modelValue);

// Update content when modelValue changes
watch(() => props.modelValue, (newVal) => {
    if (newVal !== content.value) {
        content.value = newVal;
    }
});

// Emit changes back to parent
watch(() => content.value, (newVal) => {
    emit('update:modelValue', newVal);
});

const editorOptions = {
    modules: {
        toolbar: props.toolbar
    },
    placeholder: props.placeholder,
    theme: 'snow',
};
</script>

<template>
    <div>
        <QuillEditor
            ref="editor"
            v-model:content="content"
            :options="editorOptions"
            contentType="html"
            :style="{ height: editorHeight }"
        />
    </div>
</template>

<style>
.ql-editor {
    min-height: 100px;
}
</style>
