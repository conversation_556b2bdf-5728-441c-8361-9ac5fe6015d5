<script setup>
import { useLocaleStore } from '@/Stores/locale';
import { Link, usePage } from '@inertiajs/vue3';
import { computed, onMounted, onUnmounted, ref } from 'vue';

const emit = defineEmits(['close']);
const props = defineProps({
    user: Object,
});
const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

const page = usePage();
const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

// Mobile menu state
const isMobileMenuOpen = ref(false);
const openMenus = ref({});
const openDropdowns = ref({});

// Check screen size for responsive behavior
const checkScreenSize = () => {
    if (window.innerWidth >= 1024) {
        // lg breakpoint
        isMobileMenuOpen.value = false;
    }
};

// Toggle menu sections
const toggleMenu = (menuKey) => {
    openMenus.value = {
        ...openMenus.value,
        [menuKey]: !openMenus.value[menuKey],
    };
};

// Toggle dropdown
const toggleDropdown = (menuKey, event) => {
    event.preventDefault();

    // Close all other dropdowns first
    const newState = {};
    Object.keys(openDropdowns.value).forEach((key) => {
        if (key !== menuKey) {
            newState[key] = false;
        }
    });

    // Toggle current dropdown
    newState[menuKey] = !openDropdowns.value[menuKey];
    openDropdowns.value = newState;
};

// Close mobile menu
const closeMobileMenu = () => {
    isMobileMenuOpen.value = false;
    emit('close');
};

// Close dropdowns when clicking outside
const closeDropdowns = () => {
    openDropdowns.value = {};
};

// Menu items structure
const menuItems = [
    {
        key: 'dashboard',
        title: { tr: 'Gösterge Paneli', en: 'Dashboard' },
        icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6',
        href: '/admin/dashboard',
        active: () => page.url.startsWith('/admin/dashboard'),
    },
    {
        key: 'content',
        title: { tr: 'İçerik Yönetimi', en: 'Content Management' },
        icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2V6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10',
        children: [
            {
                key: 'pages',
                title: { tr: 'Sayfalar', en: 'Pages' },
                icon: 'M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z',
                href: '/admin/pages',
                active: () => page.url.startsWith('/admin/pages'),
            },
            {
                key: 'products',
                title: { tr: 'Ürünler', en: 'Products' },
                icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10',
                href: '/admin/products',
                active: () => page.url.startsWith('/admin/products'),
            },
            {
                key: 'categories',
                title: { tr: 'Kategoriler', en: 'Categories' },
                icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2V6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10',
                href: '/admin/categories',
                active: () => page.url.startsWith('/admin/categories'),
            },
            {
                key: 'menus',
                title: { tr: 'Menüler', en: 'Menus' },
                icon: 'M4 6h16M4 12h16M4 18h16',
                href: '/admin/menus',
                active: () => page.url.startsWith('/admin/menus'),
            },
            {
                key: 'gallery',
                title: { tr: 'Galeri', en: 'Gallery' },
                icon: 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z',
                href: '/admin/gallery',
                active: () => page.url.startsWith('/admin/gallery'),
            },
            {
                key: 'banners',
                title: { tr: 'Bannerlar', en: 'Banners' },
                icon: 'M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h3a1 1 0 110 2h-1v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6H4a1 1 0 110-2h3zM9 3v1h6V3H9z',
                href: '/admin/banners',
                active: () => page.url.startsWith('/admin/banners'),
            },
            {
                key: 'contacts',
                title: { tr: 'İletişim', en: 'Contacts' },
                icon: 'M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z',
                href: '/admin/contacts',
                active: () => page.url.startsWith('/admin/contacts'),
            },
            {
                key: 'orders',
                title: { tr: 'Siparişler', en: 'Orders' },
                icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
                href: '/admin/orders',
                active: () => page.url.startsWith('/admin/orders'),
            },
        ],
    },
    {
        key: 'users',
        title: { tr: 'Kullanıcı Yönetimi', en: 'User Management' },
        icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
        children: [
            {
                key: 'users',
                title: { tr: 'Kullanıcılar', en: 'Users' },
                icon: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
                href: '/admin/users',
                active: () => page.url.startsWith('/admin/users'),
            },
            // Other user management children...
        ],
    },
    {
        key: 'settings',
        title: { tr: 'Ayarlar', en: 'Settings' },
        icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z',
        href: route('profile.edit'),
        active: () => page.url.startsWith('/settings'),
    },
];

// Check if any child menu is active
const isMenuActive = (menuItem) => {
    if (!menuItem.children) return false;

    return menuItem.children.some((child) => {
        return page.url.startsWith(child.href);
    });
};

// Initialize open menus based on active state
onMounted(() => {
    menuItems.forEach((item) => {
        if (item.children && item.children.some((child) => page.url.startsWith(child.href))) {
            openMenus.value[item.key] = true;
        }
    });

    // Add resize event listener
    window.addEventListener('resize', checkScreenSize);

    // Add click event listener to close dropdowns when clicking outside
    document.addEventListener('click', (event) => {
        const navbarElement = document.getElementById('main-navbar');
        if (navbarElement && !navbarElement.contains(event.target)) {
            closeDropdowns();
        }
    });

    // Initial check
    checkScreenSize();
});

// Clean up event listeners
onUnmounted(() => {
    window.removeEventListener('resize', checkScreenSize);
    document.removeEventListener('click', closeDropdowns);
});
</script>

<template>
    <!-- Logo for mobile -->
    <div class="flex items-center justify-between border-b border-gray-200 bg-white px-4 py-2 lg:hidden">
        <Link :href="'/'" class="flex items-center justify-center font-poppins font-bold italic">
            <span class="text-xl">{{ appName }}</span>
        </Link>

        <!-- Mobile menu toggle button -->
        <button type="button" class="rounded-md p-2 text-gray-400" @click="isMobileMenuOpen = !isMobileMenuOpen">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path v-if="isMobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
        </button>
    </div>

    <!-- Horizontal Navbar -->
    <nav id="main-navbar" class="sticky top-0 z-30 border-b border-gray-200 bg-white shadow-sm">
        <div class="mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex h-16 justify-between">
                <!-- Logo & Main Menu -->
                <div class="flex items-center justify-center">
                    <!-- Logo -->
                    <div class="flex flex-shrink-0 items-center">
                        <Link :href="'/'" class="flex items-center justify-center font-poppins font-bold italic">
                            <span class="text-xl">{{ appName }}</span>
                        </Link>
                    </div>

                    <!-- Desktop horizontal menu -->
                    <div class="hidden md:ml-6 md:flex md:items-center md:space-x-4">
                        <!-- Loop through menu items -->
                        <div v-for="item in menuItems" :key="item.key" class="relative">
                            <!-- Items with children -->
                            <div v-if="item.children" class="relative">
                                <a
                                    href="#"
                                    @click="(e) => toggleDropdown(item.key, e)"
                                    class="inline-flex items-center px-3 py-2 text-sm font-medium transition duration-150 ease-in-out"
                                    :class="[isMenuActive(item) ? 'text-indigo-600' : 'text-gray-700 hover:text-indigo-500', openDropdowns[item.key] ? 'text-indigo-600' : '']"
                                >
                                    <span>{{ locale === 'tr' ? item.title.tr : item.title.en }}</span>
                                    <svg class="ml-1 h-5 w-5 transition-transform duration-200" :class="{ 'rotate-180 transform': openDropdowns[item.key] }" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </a>

                                <!-- Dropdown menu -->
                                <div v-if="openDropdowns[item.key]" class="absolute left-0 z-10 mt-2 w-56 origin-top-left rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                    <div class="py-1">
                                        <Link
                                            v-for="child in item.children"
                                            :key="child.key"
                                            :href="child.href"
                                            class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-indigo-600"
                                            :class="{ 'bg-gray-50 text-indigo-600': page.url.startsWith(child.href) }"
                                        >
                                            <svg
                                                v-if="child.icon"
                                                class="mr-3 h-5 w-5"
                                                :class="[page.url.startsWith(child.href) ? 'text-indigo-500' : 'text-gray-400 group-hover:text-indigo-500']"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="child.icon" />
                                            </svg>
                                            {{ locale === 'tr' ? child.title.tr : child.title.en }}
                                        </Link>
                                    </div>
                                </div>
                            </div>

                            <!-- Single items -->
                            <Link
                                v-else
                                :href="item.href"
                                class="inline-flex items-center px-3 py-2 text-sm font-medium transition duration-150 ease-in-out"
                                :class="[page.url.startsWith(item.href) ? 'text-indigo-600' : 'text-gray-700 hover:text-indigo-500']"
                            >
                                {{ locale === 'tr' ? item.title.tr : item.title.en }}
                            </Link>
                        </div>
                    </div>
                </div>

                <!-- User dropdown -->
                <div class="hidden items-center md:flex">
                    <div class="relative ml-3">
                        <div>
                            <button @click="(e) => toggleDropdown('profile', e)" class="flex items-center rounded-full text-sm focus:outline-none">
                                <span class="inline-flex h-8 w-8 items-center justify-center rounded-full bg-indigo-100">
                                    <span class="text-sm font-medium text-indigo-700">
                                        {{ user?.name?.charAt(0).toUpperCase() || 'U' }}
                                    </span>
                                </span>
                                <span class="ml-2 text-sm font-medium text-gray-700">{{ user?.name }}</span>
                                <svg class="ml-1 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>

                        <!-- Profile dropdown -->
                        <div v-if="openDropdowns['profile']" class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                            <Link :href="'/profile/edit'" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                {{ locale === 'tr' ? 'Profil' : 'Profile' }}
                            </Link>
                            <Link :href="'/logout'" method="post" as="button" class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100">
                                {{ locale === 'tr' ? 'Çıkış Yap' : 'Logout' }}
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile menu, show/hide based on menu state -->
        <div v-if="isMobileMenuOpen" class="md:hidden">
            <div class="space-y-1 px-2 pb-3 pt-2">
                <!-- Mobile menu items -->
                <div v-for="item in menuItems" :key="item.key" class="mb-1">
                    <!-- Menu items with children -->
                    <div v-if="item.children">
                        <button
                            @click="toggleMenu(item.key)"
                            class="flex w-full items-center rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-indigo-600"
                            :class="{ 'bg-gray-50 text-indigo-600': isMenuActive(item) }"
                        >
                            <svg class="mr-3 h-5 w-5" :class="[isMenuActive(item) ? 'text-indigo-500' : 'text-gray-400']" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                            </svg>
                            <span class="flex-1">{{ locale === 'tr' ? item.title.tr : item.title.en }}</span>
                            <svg class="h-5 w-5 transition-transform duration-200" :class="{ 'rotate-90 transform': openMenus[item.key] }" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>

                        <!-- Submenu -->
                        <div v-if="openMenus[item.key]" class="mt-1 space-y-1 pl-8">
                            <Link
                                v-for="child in item.children"
                                :key="child.key"
                                :href="child.href"
                                class="group flex items-center rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-indigo-600"
                                :class="{ 'bg-gray-50 text-indigo-600': page.url.startsWith(child.href) }"
                            >
                                <svg
                                    v-if="child.icon"
                                    class="mr-3 h-5 w-5"
                                    :class="[page.url.startsWith(child.href) ? 'text-indigo-500' : 'text-gray-400 group-hover:text-indigo-500']"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="child.icon" />
                                </svg>
                                {{ locale === 'tr' ? child.title.tr : child.title.en }}
                            </Link>
                        </div>
                    </div>

                    <!-- Single menu items -->
                    <Link v-else :href="item.href" class="flex items-center rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-indigo-600" :class="{ 'bg-gray-50 text-indigo-600': page.url.startsWith(item.href) }">
                        <svg class="mr-3 h-5 w-5" :class="[page.url.startsWith(item.href) ? 'text-indigo-500' : 'text-gray-400']" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                        </svg>
                        {{ locale === 'tr' ? item.title.tr : item.title.en }}
                    </Link>
                </div>
            </div>

            <!-- Mobile user menu -->
            <div class="border-t border-gray-200 pb-3 pt-4">
                <div class="flex items-center px-4">
                    <div class="flex-shrink-0">
                        <span class="inline-flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100">
                            <span class="text-sm font-medium text-indigo-700">
                                {{ user?.name?.charAt(0).toUpperCase() || 'U' }}
                            </span>
                        </span>
                    </div>
                    <div class="ml-3">
                        <div class="text-base font-medium text-gray-800">{{ user?.name }}</div>
                        <div class="text-sm font-medium text-gray-500">{{ user?.email }}</div>
                    </div>
                </div>
                <div class="mt-3 space-y-1 px-2">
                    <Link :href="'/profile/edit'" class="block rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-indigo-600">
                        {{ locale === 'tr' ? 'Profil' : 'Profile' }}
                    </Link>
                    <Link :href="'/logout'" method="post" as="button" class="block w-full rounded-md px-3 py-2 text-left text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-indigo-600">
                        {{ locale === 'tr' ? 'Çıkış Yap' : 'Logout' }}
                    </Link>
                </div>
            </div>
        </div>
    </nav>
    <slot></slot>
</template>
