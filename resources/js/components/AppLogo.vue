<script setup lang="ts">
import { usePage } from '@inertiajs/vue3';
import { computed } from 'vue';

interface Props {
    class?: string;
}
const appName = import.meta.env.VITE_APP_NAME || 'Laravel';
const appLogo = import.meta.env.VITE_APP_LOGO || '';

defineProps<Props>();

const page = usePage();
const mainGrup = computed(() => page.props.main_group);
</script>

<template>
    <div class="flex h-80 w-80 items-center justify-center font-poppins font-bold italic">
        <img v-if="appLogo" :src="appLogo" alt="Logo" class="h-32 w-32 object-contain" />
        <span v-else class="text-4xl">{{ appName }}</span>
    </div>
    <div class="ml-1 grid flex-1 text-left text-sm">
        <span class="mb-0.5 truncate font-semibold leading-none">{{ mainGrup }}</span>
    </div>
</template>
