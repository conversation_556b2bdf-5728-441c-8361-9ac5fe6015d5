<script setup>
import { ref, onMounted, computed } from 'vue';
import { Link } from '@inertiajs/vue3';
import { useLocaleStore } from '@/Stores/locale';

const menus = ref([]);
const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);

// Fetch menus on component mount
onMounted(async () => {
    try {
        const response = await fetch('/front-api/menus');
        if (response.ok) {
            const data = await response.json();
            // Filter active menus with liste=true and sort by order
            menus.value = data
                .filter(menu => menu.is_active && menu.liste)
                .sort((a, b) => a.order - b.order);
        }
    } catch (error) {
        console.error('Failed to fetch menus:', error);
    }
});
</script>

<template>
    <div v-if="menus.length > 0" class="bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo will be placed here by the parent component -->
                <div class="flex-1"></div>
                
                <!-- Menu Items - Center -->
                <div class="flex justify-center space-x-6">
                    <Link 
                        v-for="menu in menus" 
                        :key="menu.id" 
                        :href="menu.url"
                        class="text-sm font-medium text-gray-600 hover:text-gray-900  duration-150 ease-in-out px-4"
                    >
                        {{ menu.name[locale] }}
                    </Link>
                </div>
                
                <!-- Right side spacing for balance -->
                <div class="flex-1"></div>
            </div>
        </div>
    </div>
</template>
