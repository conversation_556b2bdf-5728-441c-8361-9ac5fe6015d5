<script setup>
import { useLocaleStore } from '@/Stores/locale';
import AppLogo from '@/components/AppLogo.vue';
import { Link } from '@inertiajs/vue3';
import { computed } from 'vue';

const localeStore = useLocaleStore();
const locale = computed(() => localeStore.locale);
const appName = import.meta.env.VITE_APP_NAME || 'Laravel';
const appEmail = import.meta.env.VITE_APP_EMAIL || 'Laravel';
const appAddress = import.meta.env.VITE_APP_EMAIL || 'Laravel';

const currentYear = new Date().getFullYear();
</script>

<template>
    <footer class="bg-gray-900 text-white">
        <!-- Main Footer Content -->
        <div class="container mx-auto px-6 pb-8 pt-12">
            <div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
                <!-- Company Information -->
                <div>
                    <div class="mb-4 flex items-center">
                        <AppLogo class="h-10 w-auto brightness-0 invert filter" />
                    </div>
                    <p class="mb-4 pr-4 text-gray-400">Professional solutions for your business needs. We provide high-quality services with a focus on customer satisfaction.</p>
                    <div class="mt-6 flex space-x-4">
                        <a href="#" class="-colors text-gray-400 hover:text-white">
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path
                                    fill-rule="evenodd"
                                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                        </a>
                        <a href="#" class="-colors text-gray-400 hover:text-white">
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path
                                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                                ></path>
                            </svg>
                        </a>
                        <a href="#" class="-colors text-gray-400 hover:text-white">
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path
                                    fill-rule="evenodd"
                                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                        </a>
                        <a href="#" class="-colors text-gray-400 hover:text-white">
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path
                                    fill-rule="evenodd"
                                    d="M19.7 3.037c-.88-.415-1.85-.618-2.926-.618-1.945 0-3.547.825-4.774 2.476-1.225-1.65-2.825-2.476-4.774-2.476-1.075 0-2.045.204-2.926.618C2.861 4.076 2 5.723 2 7.644c0 1.632.828 3.088 2.488 4.375 1.66 1.287 3.953 2.344 6.847 3.187-.194 2.31.142 4.492 1.013 6.538.87 2.046 2.287 3.256 4.233 3.256 1.948 0 3.366-1.21 4.236-3.256.87-2.046 1.207-4.228 1.013-6.538 2.894-.843 5.187-1.9 6.847-3.187C20.517 10.732 21 9.276 21 7.644c0-1.921-.861-3.568-2.3-4.607zM4 7.644c0-1.27.578-2.296 1.53-3.038.618-.267 1.3-.399 2.045-.399 1.601 0 2.902.73 3.902 2.188-1.218.798-2.261 1.641-3.01 2.536-.75.894-1.225 1.79-1.425 2.686C5.35 10.892 4 9.275 4 7.644zm8.119 10.867c-.312 1.242-.73 2.226-1.256 2.953-.527.727-1.115 1.09-1.763 1.09s-1.236-.363-1.763-1.09c-.526-.727-.944-1.711-1.256-2.953-.311-1.242-.419-2.606-.32-4.084 1.32.308 2.763.58 4.32.813 0 1.9-.32 3.37-.962 4.27zm7.759-9.623c-.2-.894-.674-1.79-1.425-2.685s-1.792-1.738-3.01-2.536c.4-.485.85-.895 1.35-1.237.5-.342 1.026-.61 1.576-.806.55-.195 1.114-.292 1.675-.292.746 0 1.428.133 2.046.4.952.741 1.53 1.767 1.53 3.038 0 1.631-1.35 3.248-3.742 4.118z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="mb-6 border-b border-gray-700 pb-2 text-lg font-bold">Quick Links</h3>
                    <ul class="space-y-3">
                        <li>
                            <Link :href="route('home')" class="-colors text-gray-400 hover:text-white"> Home </Link>
                        </li>
                        <li>
                            <Link :href="route('front.pages.show', 'about')" class="-colors text-gray-400 hover:text-white"> About Us </Link>
                        </li>
                        <li>
                            <Link :href="route('front.products.index')" class="-colors text-gray-400 hover:text-white"> Products </Link>
                        </li>
                        <li>
                            <Link :href="route('front.gallery.index')" class="-colors text-gray-400 hover:text-white"> Gallery </Link>
                        </li>
                        <li>
                            <Link :href="route('front.contact.form')" class="-colors text-gray-400 hover:text-white"> Contact </Link>
                        </li>
                    </ul>
                </div>

                <!-- Contact Information -->
                <div>
                    <h3 class="mb-6 border-b border-gray-700 pb-2 text-lg font-bold">Contact Us</h3>
                    <ul class="space-y-4">
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-6 w-6 flex-shrink-0 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <span class="text-gray-400">123 Business Avenue, Office Park, 34000</span>
                        </li>
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-6 w-6 flex-shrink-0 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <span class="text-gray-400">{{ appEmail }}</span>
                        </li>
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-6 w-6 flex-shrink-0 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                />
                            </svg>
                            <span class="text-gray-400">+90 555 123 4567</span>
                        </li>
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-6 w-6 flex-shrink-0 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="text-gray-400">Mon - Fri: 9:00 AM - 6:00 PM</span>
                        </li>
                    </ul>
                </div>

                <!-- Newsletter -->
                <div>
                    <h3 class="mb-6 border-b border-gray-700 pb-2 text-lg font-bold">Newsletter</h3>
                    <p class="mb-4 text-gray-400">Stay updated with our latest news and offers.</p>
                    <form class="flex flex-col space-y-3">
                        <input type="email" placeholder="Your email address" class="rounded border border-gray-700 bg-gray-800 px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" />
                        <button type="submit" class="-colors rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">Subscribe</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="bg-gray-950 py-4">
            <div class="container mx-auto flex flex-col items-center justify-between px-6 md:flex-row">
                <div class="mb-4 text-sm text-gray-400 md:mb-0">&copy; {{ currentYear }} {{ appName }}. All rights reserved.</div>
                <div class="flex space-x-6">
                    <Link :href="route('privacy.policy')" class="-colors text-sm text-gray-400 hover:text-white"> Privacy Policy </Link>
                    <Link :href="route('front.pages.show', 'terms')" class="-colors text-sm text-gray-400 hover:text-white"> Terms of Service </Link>
                </div>
            </div>
        </div>
    </footer>
</template>
