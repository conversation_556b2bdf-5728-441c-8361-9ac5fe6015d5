<template>
    <Transition name="fade">
        <div v-if="isVisible" class="pointer-events-none fixed inset-0 z-50 flex items-center justify-center">
            <div :class="['pointer-events-auto max-w-md rounded-lg px-6 py-3 text-center shadow-lg', message.type === 'error' ? 'bg-red-500 text-white' : 'bg-theme-primary text-white']">
                {{ message.text }}
            </div>
        </div>
    </Transition>
</template>

<script setup>
import { usePage } from '@inertiajs/vue3';
import { computed, onMounted, ref } from 'vue';

const isVisible = ref(false);
const message = ref({
    text: '',
    type: 'success',
});

const showMessage = (text, type = 'success') => {
    message.value = { text, type };
    isVisible.value = true;
    setTimeout(() => {
        isVisible.value = false;
    }, 3000);
};

// usePage() ile sayfa props'larına er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
const { props } = usePage();
const flash = computed(() => props.flash || {});

onMounted(() => {
    if (flash.value.message) {
        showMessage(flash.value.message, 'success');
    }
    if (flash.value.error) {
        showMessage(flash.value.error, 'error');
    }
});

defineExpose({
    showMessage,
});
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s ease;
}
.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>
