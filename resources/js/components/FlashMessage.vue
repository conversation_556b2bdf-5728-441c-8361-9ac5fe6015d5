<template>
    <Transition name="fade">
      <div
        v-if="isVisible"
        class="fixed inset-0 flex items-center justify-center z-50 pointer-events-none"
      >
        <div
          :class="[
            'px-6 py-3 rounded-lg shadow-lg max-w-md text-center pointer-events-auto',
            message.type === 'error'
              ? 'bg-red-500 text-white'
              : 'bg-green-500 text-white',
          ]"
        >
          {{ message.text }}
        </div>
      </div>
    </Transition>
  </template>
  
  <script setup>
  import { ref, onMounted, computed } from "vue";
  import { usePage } from "@inertiajs/vue3";
  
  const isVisible = ref(false);
  const message = ref({
    text: "",
    type: "success",
  });
  
  const showMessage = (text, type = "success") => {
    message.value = { text, type };
    isVisible.value = true;
    setTimeout(() => {
      isVisible.value = false;
    }, 3000);
  };
  
  // usePage() ile sayfa props'lar<PERSON>na er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  const { props } = usePage();
  const flash = computed(() => props.flash || {});
  
  onMounted(() => {
    if (flash.value.message) {
      showMessage(flash.value.message, "success");
    }
    if (flash.value.error) {
      showMessage(flash.value.error, "error");
    }
  });
  
  defineExpose({
    showMessage,
  });
  </script>
  
  <style scoped>
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.5s ease;
  }
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  </style>
  