<script setup>
import { ref, watch, onMounted } from 'vue';
import { router, useForm } from '@inertiajs/vue3';
import Modal from '@/components/Modal.vue';
import { useLocaleStore } from '@/Stores/locale';

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    type: {
        type: String,
        default: 'image'
    },
    mode: {
        type: String,
        default: 'single'
    }
});

const emit = defineEmits(['close', 'select']);

const localeStore = useLocaleStore();
const locale = ref(localeStore.locale);

const files = ref([]);
const loading = ref(true);
const search = ref('');
const selectedFile = ref(null);
const filters = ref({
    type: props.type,
    folder: '',
    search: ''
});

const fetchFiles = async () => {
    loading.value = true;
    try {
        const queryParams = { 
            ...filters.value,
            format: 'json' 
        };
        
        const response = await fetch(route('admin.files.index', queryParams), {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });
        
        if (response.ok) {
            const result = await response.json();
            files.value = result.files || [];
        } else {
            console.error('Failed to fetch files:', response.statusText);
            files.value = [];
        }
    } catch (error) {
        console.error('Error fetching files:', error);
        files.value = [];
    } finally {
        loading.value = false;
    }
};

const searchFiles = () => {
    filters.value.search = search.value;
    fetchFiles();
};

const selectFile = (file) => {
    selectedFile.value = file;
    emit('select', file);
    emit('close');
};

const isImage = (file) => {
    const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/svg+xml', 'image/webp'];
    return imageTypes.includes(file.mime_type);
};

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
};

watch(() => props.show, (newVal) => {
    if (newVal) {
        fetchFiles();
    }
});

onMounted(() => {
    if (props.show) {
        fetchFiles();
    }
});
</script>

<template>
    <Modal :show="show" max-width="6xl" @close="$emit('close')">
        <template #title>
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">
                    {{ locale === 'tr' ? 'Dosya Yöneticisi' : 'File Manager' }}
                </h3>
            </div>
        </template>
        
        <template #content>
            <div class="p-4">
                <!-- Search and Filters -->
                <div class="flex flex-col md:flex-row justify-between mb-6 space-y-4 md:space-y-0">
                    <div class="flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0 w-full">
                        <!-- Search -->
                        <div class="w-full md:w-1/2">
                            <input
                                v-model="search"
                                type="text"
                                :placeholder="locale === 'tr' ? 'Dosyalarda ara...' : 'Search files...'"
                                class="w-full rounded-md shadow-sm border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                @keyup.enter="searchFiles"
                            />
                        </div>

                        <!-- Type Filter -->
                        <div class="w-full md:w-1/4">
                            <select
                                v-model="filters.type"
                                class="w-full rounded-md shadow-sm border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                @change="fetchFiles"
                            >
                                <option value="">{{ locale === 'tr' ? 'Tüm Tipler' : 'All Types' }}</option>
                                <option value="image">{{ locale === 'tr' ? 'Resimler' : 'Images' }}</option>
                                <option value="document">{{ locale === 'tr' ? 'Dökümanlar' : 'Documents' }}</option>
                            </select>
                        </div>

                        <!-- Folder Filter -->
                        <div class="w-full md:w-1/4">
                            <select
                                v-model="filters.folder"
                                class="w-full rounded-md shadow-sm border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                @change="fetchFiles"
                            >
                                <option value="">{{ locale === 'tr' ? 'Tüm Klasörler' : 'All Folders' }}</option>
                                <option value="uploads">{{ locale === 'tr' ? 'Yüklemeler' : 'Uploads' }}</option>
                                <option value="products">{{ locale === 'tr' ? 'Ürünler' : 'Products' }}</option>
                                <option value="pages">{{ locale === 'tr' ? 'Sayfalar' : 'Pages' }}</option>
                                <option value="gallery">{{ locale === 'tr' ? 'Galeri' : 'Gallery' }}</option>
                                <option value="banners">{{ locale === 'tr' ? 'Bannerlar' : 'Banners' }}</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="flex justify-center items-center py-12">
                    <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </div>

                <!-- File Grid -->
                <div v-else-if="files.length > 0" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <div
                        v-for="file in files"
                        :key="file.id"
                        class="border rounded-lg overflow-hidden hover:shadow-md -shadow cursor-pointer"
                        @click="selectFile(file)"
                    >
                        <div class="relative h-32 bg-gray-100 flex items-center justify-center">
                            <!-- Image Preview -->
                            <img
                                v-if="isImage(file)"
                                :src="`/storage/${file.file_path}`"
                                :alt="file.original_filename"
                                class="h-full w-full object-cover object-center"
                            />
                            <!-- Document Icon -->
                            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <div class="p-2">
                            <div class="text-xs font-medium text-gray-900 truncate">
                                {{ file.original_filename }}
                            </div>
                            <div class="text-xs text-gray-500">
                                {{ formatFileSize(file.size) }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Empty State -->
                <div v-else class="text-center py-12">
                    <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">
                        {{ locale === 'tr' ? 'Dosya bulunamadı' : 'No files found' }}
                    </h3>
                    <p class="mt-1 text-sm text-gray-500">
                        {{ locale === 'tr' ? 'Yeni bir dosya yükleyerek başlayın.' : 'Get started by uploading a new file.' }}
                    </p>
                </div>
            </div>
        </template>
        
        <template #footer>
            <div class="flex justify-end space-x-3">
                <button
                    type="button"
                    class="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 "
                    @click="$emit('close')"
                >
                    {{ locale === 'tr' ? 'İptal' : 'Cancel' }}
                </button>
            </div>
        </template>
    </Modal>
</template>
