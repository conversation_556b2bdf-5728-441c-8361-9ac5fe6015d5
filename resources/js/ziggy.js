const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"home":{"uri":"\/","methods":["GET","HEAD"]},"bakiyeOmurTablosu":{"uri":"bakiyeOmurTablosu","methods":["GET","HEAD"]},"bakiyeOmurTablosu1931":{"uri":"bakiyeOmurTablosu1931","methods":["GET","HEAD"]},"b2b":{"uri":"b2b","methods":["GET","HEAD"]},"b2b.basketAdd":{"uri":"b2b\/basket\/add","methods":["POST"]},"b2b.getBasket":{"uri":"b2b\/basket\/get","methods":["GET","HEAD"]},"b2b.basket":{"uri":"b2b\/basket","methods":["GET","HEAD"]},"front.set_main_grup":{"uri":"set-main-grup\/{grup}","methods":["GET","HEAD"],"parameters":["grup"]},"front.pages.show":{"uri":"pages\/{slug}","methods":["GET","HEAD"],"parameters":["slug"]},"front.products.index":{"uri":"products","methods":["GET","HEAD"]},"front.products.proworker":{"uri":"products\/proworker-products","methods":["GET","HEAD"]},"front.products.solar":{"uri":"products\/solar-products","methods":["GET","HEAD"]},"front.products.show":{"uri":"products\/{slug}","methods":["GET","HEAD"],"parameters":["slug"]},"front.gallery.index":{"uri":"gallery","methods":["GET","HEAD"]},"front.contact.form":{"uri":"contact","methods":["GET","HEAD"]},"privacy.policy":{"uri":"privacy-policy","methods":["GET","HEAD"]},"api.locale.set":{"uri":"api\/locale\/{locale}","methods":["POST"],"parameters":["locale"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"admin.dashboard":{"uri":"admin\/dashboard","methods":["GET","HEAD"]},"admin.roles.index":{"uri":"admin\/roles","methods":["GET","HEAD"]},"admin.roles.create":{"uri":"admin\/roles\/create","methods":["GET","HEAD"]},"admin.roles.store":{"uri":"admin\/roles","methods":["POST"]},"admin.roles.show":{"uri":"admin\/roles\/{role}","methods":["GET","HEAD"],"parameters":["role"],"bindings":{"role":"id"}},"admin.roles.edit":{"uri":"admin\/roles\/{role}\/edit","methods":["GET","HEAD"],"parameters":["role"],"bindings":{"role":"id"}},"admin.roles.update":{"uri":"admin\/roles\/{role}","methods":["PUT","PATCH"],"parameters":["role"],"bindings":{"role":"id"}},"admin.roles.destroy":{"uri":"admin\/roles\/{role}","methods":["DELETE"],"parameters":["role"],"bindings":{"role":"id"}},"admin.permissions.index":{"uri":"admin\/permissions","methods":["GET","HEAD"]},"admin.permissions.create":{"uri":"admin\/permissions\/create","methods":["GET","HEAD"]},"admin.permissions.store":{"uri":"admin\/permissions","methods":["POST"]},"admin.permissions.show":{"uri":"admin\/permissions\/{permission}","methods":["GET","HEAD"],"parameters":["permission"]},"admin.permissions.edit":{"uri":"admin\/permissions\/{permission}\/edit","methods":["GET","HEAD"],"parameters":["permission"]},"admin.permissions.update":{"uri":"admin\/permissions\/{permission}","methods":["PUT","PATCH"],"parameters":["permission"]},"admin.permissions.destroy":{"uri":"admin\/permissions\/{permission}","methods":["DELETE"],"parameters":["permission"]},"admin.users.index":{"uri":"admin\/users","methods":["GET","HEAD"]},"admin.users.create":{"uri":"admin\/users\/create","methods":["GET","HEAD"]},"admin.users.store":{"uri":"admin\/users","methods":["POST"]},"admin.users.show":{"uri":"admin\/users\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.edit":{"uri":"admin\/users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.update":{"uri":"admin\/users\/{user}","methods":["PUT","PATCH"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.destroy":{"uri":"admin\/users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"admin.pages.index":{"uri":"admin\/pages","methods":["GET","HEAD"]},"admin.pages.create":{"uri":"admin\/pages\/create","methods":["GET","HEAD"]},"admin.pages.store":{"uri":"admin\/pages","methods":["POST"]},"admin.pages.show":{"uri":"admin\/pages\/{page}","methods":["GET","HEAD"],"parameters":["page"]},"admin.pages.edit":{"uri":"admin\/pages\/{page}\/edit","methods":["GET","HEAD"],"parameters":["page"]},"admin.pages.update":{"uri":"admin\/pages\/{page}","methods":["PUT","PATCH"],"parameters":["page"]},"admin.pages.destroy":{"uri":"admin\/pages\/{page}","methods":["DELETE"],"parameters":["page"]},"admin.products.index":{"uri":"admin\/products","methods":["GET","HEAD"]},"admin.products.create":{"uri":"admin\/products\/create","methods":["GET","HEAD"]},"admin.products.store":{"uri":"admin\/products","methods":["POST"]},"admin.products.show":{"uri":"admin\/products\/{product}","methods":["GET","HEAD"],"parameters":["product"]},"admin.products.edit":{"uri":"admin\/products\/{product}\/edit","methods":["GET","HEAD"],"parameters":["product"]},"admin.products.update":{"uri":"admin\/products\/{product}","methods":["PUT","PATCH"],"parameters":["product"]},"admin.products.destroy":{"uri":"admin\/products\/{product}","methods":["DELETE"],"parameters":["product"]},"admin.products.imageUpload":{"uri":"admin\/products\/imageUpload","methods":["POST"]},"admin.product.getProductImages":{"uri":"admin\/products\/{product}\/images","methods":["GET","HEAD"],"parameters":["product"]},"admin.products.deleteProductImage":{"uri":"admin\/products\/images\/{productImage}","methods":["DELETE"],"parameters":["productImage"],"bindings":{"productImage":"id"}},"admin.products.imageOrderUpadte":{"uri":"admin\/products\/imageOrderUpadte","methods":["POST"]},"admin.categories.index":{"uri":"admin\/categories","methods":["GET","HEAD"]},"admin.categories.create":{"uri":"admin\/categories\/create","methods":["GET","HEAD"]},"admin.categories.store":{"uri":"admin\/categories","methods":["POST"]},"admin.categories.show":{"uri":"admin\/categories\/{category}","methods":["GET","HEAD"],"parameters":["category"]},"admin.categories.edit":{"uri":"admin\/categories\/{category}\/edit","methods":["GET","HEAD"],"parameters":["category"]},"admin.categories.update":{"uri":"admin\/categories\/{category}","methods":["PUT","PATCH"],"parameters":["category"]},"admin.categories.destroy":{"uri":"admin\/categories\/{category}","methods":["DELETE"],"parameters":["category"]},"admin.menus.index":{"uri":"admin\/menus","methods":["GET","HEAD"]},"admin.menus.create":{"uri":"admin\/menus\/create","methods":["GET","HEAD"]},"admin.menus.store":{"uri":"admin\/menus","methods":["POST"]},"admin.menus.show":{"uri":"admin\/menus\/{menu}","methods":["GET","HEAD"],"parameters":["menu"]},"admin.menus.edit":{"uri":"admin\/menus\/{menu}\/edit","methods":["GET","HEAD"],"parameters":["menu"]},"admin.menus.update":{"uri":"admin\/menus\/{menu}","methods":["PUT","PATCH"],"parameters":["menu"]},"admin.menus.destroy":{"uri":"admin\/menus\/{menu}","methods":["DELETE"],"parameters":["menu"]},"admin.gallery.index":{"uri":"admin\/gallery","methods":["GET","HEAD"]},"admin.gallery.create":{"uri":"admin\/gallery\/create","methods":["GET","HEAD"]},"admin.gallery.store":{"uri":"admin\/gallery","methods":["POST"]},"admin.gallery.show":{"uri":"admin\/gallery\/{gallery}","methods":["GET","HEAD"],"parameters":["gallery"]},"admin.gallery.edit":{"uri":"admin\/gallery\/{gallery}\/edit","methods":["GET","HEAD"],"parameters":["gallery"]},"admin.gallery.update":{"uri":"admin\/gallery\/{gallery}","methods":["PUT","PATCH"],"parameters":["gallery"]},"admin.gallery.destroy":{"uri":"admin\/gallery\/{gallery}","methods":["DELETE"],"parameters":["gallery"]},"admin.contacts.index":{"uri":"admin\/contacts","methods":["GET","HEAD"]},"admin.contacts.create":{"uri":"admin\/contacts\/create","methods":["GET","HEAD"]},"admin.contacts.store":{"uri":"admin\/contacts","methods":["POST"]},"admin.contacts.show":{"uri":"admin\/contacts\/{contact}","methods":["GET","HEAD"],"parameters":["contact"]},"admin.contacts.edit":{"uri":"admin\/contacts\/{contact}\/edit","methods":["GET","HEAD"],"parameters":["contact"]},"admin.contacts.update":{"uri":"admin\/contacts\/{contact}","methods":["PUT","PATCH"],"parameters":["contact"]},"admin.contacts.destroy":{"uri":"admin\/contacts\/{contact}","methods":["DELETE"],"parameters":["contact"]},"admin.banners.index":{"uri":"admin\/banners","methods":["GET","HEAD"]},"admin.banners.create":{"uri":"admin\/banners\/create","methods":["GET","HEAD"]},"admin.banners.store":{"uri":"admin\/banners","methods":["POST"]},"admin.banners.show":{"uri":"admin\/banners\/{banner}","methods":["GET","HEAD"],"parameters":["banner"]},"admin.banners.edit":{"uri":"admin\/banners\/{banner}\/edit","methods":["GET","HEAD"],"parameters":["banner"]},"admin.banners.update":{"uri":"admin\/banners\/{banner}","methods":["PUT","PATCH"],"parameters":["banner"]},"admin.banners.destroy":{"uri":"admin\/banners\/{banner}","methods":["DELETE"],"parameters":["banner"]},"admin.gallery_images.show":{"uri":"admin\/galleries\/{gallery}\/galleryimages","methods":["GET","HEAD"],"parameters":["gallery"]},"admin.gallery_images.create":{"uri":"admin\/galleries\/{gallery}\/galleryimages\/create","methods":["GET","HEAD"],"parameters":["gallery"]},"admin.gallery_images.store":{"uri":"admin\/galleries\/{gallery}\/galleryimages","methods":["POST"],"parameters":["gallery"]},"admin.gallery_images.edit":{"uri":"admin\/galleries\/{gallery}\/galleryimages\/{id}\/edit","methods":["GET","HEAD"],"parameters":["gallery","id"]},"admin.gallery_images.update":{"uri":"admin\/galleries\/{gallery}\/galleryimages\/{id}","methods":["PUT"],"parameters":["gallery","id"]},"admin.gallery_images.destroy":{"uri":"admin\/galleries\/{gallery}\/galleryimages\/{id}","methods":["DELETE"],"parameters":["gallery","id"]},"admin.files.index":{"uri":"admin\/files","methods":["GET","HEAD"]},"admin.files.create":{"uri":"admin\/files\/create","methods":["GET","HEAD"]},"admin.files.store":{"uri":"admin\/files","methods":["POST"]},"admin.files.show":{"uri":"admin\/files\/{file}","methods":["GET","HEAD"],"parameters":["file"],"bindings":{"file":"id"}},"admin.files.edit":{"uri":"admin\/files\/{file}\/edit","methods":["GET","HEAD"],"parameters":["file"],"bindings":{"file":"id"}},"admin.files.update":{"uri":"admin\/files\/{file}","methods":["PUT","PATCH"],"parameters":["file"],"bindings":{"file":"id"}},"admin.files.destroy":{"uri":"admin\/files\/{file}","methods":["DELETE"],"parameters":["file"],"bindings":{"file":"id"}},"admin.files.bulk-upload":{"uri":"admin\/files\/bulk-upload","methods":["POST"]},"admin.backups.index":{"uri":"admin\/backups","methods":["GET","HEAD"]},"admin.backups.create":{"uri":"admin\/backups","methods":["POST"]},"admin.backups.download":{"uri":"admin\/backups\/{filename}","methods":["GET","HEAD"],"parameters":["filename"]},"admin.backups.destroy":{"uri":"admin\/backups\/{filename}","methods":["DELETE"],"parameters":["filename"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"profile.photo.update":{"uri":"settings\/profile\/photo","methods":["POST"]},"profile.preferences.update":{"uri":"settings\/profile\/preferences","methods":["POST"]},"profile.two-factor.toggle":{"uri":"settings\/profile\/two-factor","methods":["POST"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
