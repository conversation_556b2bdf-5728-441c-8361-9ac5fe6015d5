<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Theme Colors
    |--------------------------------------------------------------------------
    |
    | These color values are used throughout the application for consistent
    | theming. Colors are defined as RGB values for maximum flexibility.
    |
    */

    'colors' => [
        'primary' => env('APP_PRIMARY_COLOR', '119 176 170'), // A<PERSON><PERSON><PERSON>, linkler
        'secondary' => env('APP_SECONDARY_COLOR', '19 93 102'), // Orta <PERSON> al<PERSON>, ikonlar
        'general' => env('APP_GENERAL_COLOR', '0 60 67'), // <PERSON>yu <PERSON>/<PERSON><PERSON><PERSON> - <PERSON>, <PERSON><PERSON><PERSON>, footer
        'accent' => env('APP_ACCENT_COLOR', '227 254 247'), // Çok <PERSON>çık <PERSON>e <PERSON>şili - Ana arka plan
    ],

    /*
    |--------------------------------------------------------------------------
    | CSS Custom Properties
    |--------------------------------------------------------------------------
    |
    | Generate CSS custom properties for use in frontend
    |
    */

    'css_variables' => [
        '--color-primary' => env('APP_PRIMARY_COLOR', '119 176 170'),
        '--color-secondary' => env('APP_SECONDARY_COLOR', '19 93 102'),
        '--color-general' => env('APP_GENERAL_COLOR', '0 60 67'),
        '--color-accent' => env('APP_ACCENT_COLOR', '227 254 247'),
    ],
];
