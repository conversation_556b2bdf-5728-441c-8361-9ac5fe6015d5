<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Theme Colors
    |--------------------------------------------------------------------------
    |
    | These color values are used throughout the application for consistent
    | theming. Colors are defined as RGB values for maximum flexibility.
    |
    */

    'colors' => [
        'primary' => env('APP_PRIMARY_COLOR', '184 207 206'), // Açık yeşilimsi mavi
        'secondary' => env('APP_SECONDARY_COLOR', '160 200 120'), // Açık yeşil
        'general' => env('APP_GENERAL_COLOR', '20 61 96'), // Koyu mavi
        'accent' => env('APP_ACCENT_COLOR', '221 235 157'), // Açık sarıms<PERSON> yeşil
    ],

    /*
    |--------------------------------------------------------------------------
    | CSS Custom Properties
    |--------------------------------------------------------------------------
    |
    | Generate CSS custom properties for use in frontend
    |
    */

    'css_variables' => [
        '--color-primary' => env('APP_PRIMARY_COLOR', '184 207 206'),
        '--color-secondary' => env('APP_SECONDARY_COLOR', '160 200 120'),
        '--color-general' => env('APP_GENERAL_COLOR', '20 61 96'),
        '--color-accent' => env('APP_ACCENT_COLOR', '221 235 157'),
    ],
];
