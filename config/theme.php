<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Theme Colors
    |--------------------------------------------------------------------------
    |
    | These color values are used throughout the application for consistent
    | theming. Colors are defined as RGB values for maximum flexibility.
    |
    */

    'colors' => [
        'primary' => env('APP_PRIMARY_COLOR', '2000'), // Sky-500
        'secondary' => env('APP_SECONDARY_COLOR', '000'), // Indigo-600
        'accent' => env('APP_ACCENT_COLOR', '000'), // Orange-500
    ],

    /*
    |--------------------------------------------------------------------------
    | CSS Custom Properties
    |--------------------------------------------------------------------------
    |
    | Generate CSS custom properties for use in frontend
    |
    */

    'css_variables' => [
        '--color-primary' => env('APP_PRIMARY_COLOR', '000'),
        '--color-secondary' => env('APP_SECONDARY_COLOR', '000'),
        '--color-accent' => env('APP_ACCENT_COLOR', '000'),
    ],
];
