<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Theme Colors
    |--------------------------------------------------------------------------
    |
    | These color values are used throughout the application for consistent
    | theming. Colors are defined as RGB values for maximum flexibility.
    |
    */

    'colors' => [
        'primary' => env('APP_PRIMARY_COLOR', '184 207 206'), // Açık ye<PERSON>ilimsi mavi
        'secondary' => env('APP_SECONDARY_COLOR', '127 140 170'), // Gri-mavi
        'accent' => env('APP_ACCENT_COLOR', '234 239 239'), // Çok açık gri
        'general' => env('APP_GENERAL_COLOR', '51 52 70'), // Koyu gri-mavi
    ],

    /*
    |--------------------------------------------------------------------------
    | CSS Custom Properties
    |--------------------------------------------------------------------------
    |
    | Generate CSS custom properties for use in frontend
    |
    */

    'css_variables' => [
        '--color-primary' => env('APP_PRIMARY_COLOR', '184 207 206'),
        '--color-secondary' => env('APP_SECONDARY_COLOR', '127 140 170'),
        '--color-accent' => env('APP_ACCENT_COLOR', '234 239 239'),
        '--color-general' => env('APP_GENERAL_COLOR', '51 52 70'),
    ],
];
